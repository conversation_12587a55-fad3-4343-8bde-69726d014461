package com.maersk.sd1.sde.controller.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * Input DTO replicating the stored procedure parameters.
 * We particularly want "documentoCargaId" and "idiomaId".
 */
@Data
public class HistoryDateOverStayInput {

    @Data
    public static class Input {
        @JsonProperty("documento_carga_id")
        @NotNull
        private Integer documentoCargaId;

        @JsonProperty("idioma_id")
        @NotNull
        private Integer idiomaId;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("SDG")
        private Prefix prefix;
    }
}