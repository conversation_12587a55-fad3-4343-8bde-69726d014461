package com.maersk.sd1.sde.controller.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

@Data
public class BookingDetailsListOutput {

    @JsonProperty("resp_estado")
    private Integer respStatus;

    @JsonProperty("resp_mensaje")
    private String respMessage;

    @JsonProperty("lista_contenedores")
    private List<BookingDetailsListOutputDetail> listContainers;

    @Data
    public static class BookingDetailsListOutputDetail {

        @JsonProperty("tno_cnt")
        private String containerNumber;

        @JsonProperty("cat_tipo_contenedor_desc")
        private String containerTypeDescription;

        @JsonProperty("solicitado")
        private Integer requested;

        @JsonProperty("asignado")
        private Integer assigned;

        @JsonProperty("carga_maxima_requerido")
        private Integer maxLoadRequired;

        @JsonProperty("booking_detalle_id")
        private Integer bookingDetailId;

    }
}