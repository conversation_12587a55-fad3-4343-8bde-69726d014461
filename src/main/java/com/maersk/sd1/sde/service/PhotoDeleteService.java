package com.maersk.sd1.sde.service;

import com.maersk.sd1.common.model.*;
import com.maersk.sd1.common.repository.*;
import com.maersk.sd1.sde.dto.PhotoDeleteOutput;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

@Service
@RequiredArgsConstructor
@Log4j2
public class PhotoDeleteService {

    private final EirRepository eirRepository;
    private final CatalogRepository catalogRepository;
    private final ChassisEstimateEirPhotoRepository chassisEstimateEirPhotoRepository;
    private final EstimateEmrEirPhotoRepository estimateEmrEirPhotoRepository;
    private final SdfEirPhotoRepository eirPhotoRepository;
    private final AttachmentRepository attachmentRepository;
    private final UserRepository userRepository;

    @Transactional
    public PhotoDeleteOutput deletePhoto(Integer eirId,
                                         Integer attachmentId,
                                         Integer userModificationId,
                                         Integer equipmentCategoryId) {
        PhotoDeleteOutput output = new PhotoDeleteOutput();
        try {
            log.info("deletePhoto - Starting deletion for eirId: {} and attachmentId: {}", eirId, attachmentId);

            User modificationUser = userRepository.findById(userModificationId)
                    .orElseThrow(() -> new IllegalArgumentException("Invalid userModificationId"));

            Catalog isChassisCatalog = catalogRepository.findByAlias("sd1_equipment_category_chassis");
            Catalog isEmptyCatalog = catalogRepository.findByAlias("43083");
            if (equipmentCategoryId.equals(isChassisCatalog.getId())) {
                deletePhotoFromChassisEstimate(eirId, attachmentId, modificationUser);
            } else {
                Eir eir = eirRepository.findById(eirId)
                        .orElseThrow(() -> new IllegalArgumentException("EIR not found with id " + eirId));

                if (eir.getCatEmptyFull() != null && eir.getCatEmptyFull().getId().equals(isEmptyCatalog.getId())) {
                    deletePhotoFromEstimate(eirId, attachmentId, modificationUser);
                } else {
                    deletePhotoFromEirPhoto(eirId, attachmentId, modificationUser);
                }
            }

            updateAttachment(attachmentId, modificationUser);

            output.setRespEstado(1);
            output.setRespMensaje("Photo were successfully deleted");

            log.info("deletePhoto - Deletion successful for eirId: {} and attachmentId: {}", eirId, attachmentId);
        } catch (Exception e) {
            log.error("deletePhoto - Error occurred: ", e);
            output.setRespEstado(0);
            output.setRespMensaje(e.getMessage());
        }
        return output;
    }

    private void deletePhotoFromChassisEstimate(Integer eirId, Integer attachmentId, User modificationUser) {
        chassisEstimateEirPhotoRepository.findByEirChassis_IdAndAttachment_Id(eirId, attachmentId)
                .ifPresent(photo -> {
                    photo.setActive(false);
                    photo.setModificationDate(LocalDateTime.now());
                    photo.setModificationUser(modificationUser);
                    chassisEstimateEirPhotoRepository.save(photo);
                });
    }

    private void deletePhotoFromEstimate(Integer eirId, Integer attachmentId, User modificationUser) {
        estimateEmrEirPhotoRepository.findByEir_IdAndAttachment_Id(eirId, attachmentId)
                .ifPresent(photo -> {
                    photo.setActive(false);
                    photo.setModificationDate(LocalDateTime.now());
                    photo.setModificationUser(modificationUser);
                    estimateEmrEirPhotoRepository.save(photo);
                });
    }

    private void deletePhotoFromEirPhoto(Integer eirId, Integer attachmentId, User modificationUser) {
        eirPhotoRepository.findByEir_IdAndAttached_Id(eirId, attachmentId)
                .ifPresent(photo -> {
                    photo.setActive(false);
                    photo.setModificationDate(LocalDateTime.now());
                    photo.setUserModification(modificationUser);
                    eirPhotoRepository.save(photo);
                });
    }

    private void updateAttachment(Integer attachmentId, User modificationUser) {
        attachmentRepository.findById(attachmentId)
                .ifPresent(attachment -> {
                    attachment.setStatus(false);
                    attachment.setModificationDate(LocalDateTime.now());
                    attachment.setModificationUser(modificationUser);
                    attachmentRepository.save(attachment);
                });
    }
}

