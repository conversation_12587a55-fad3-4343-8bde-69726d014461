package com.maersk.sd1.sde.service;

import com.maersk.sd1.sde.dto.CedexSetupCorrectOutput;
import com.maersk.sd1.common.repository.CedexMercRepository;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Map;

@RequiredArgsConstructor
@Service
public class CedexSetupCorrectService {

    private static final Logger logger = LogManager.getLogger(CedexSetupCorrectService.class);

    private final CedexMercRepository cedexMercRepository;

    @Transactional
    public CedexSetupCorrectOutput processCedexSetupCorrect(@Valid BigDecimal subBusinessUnitId,
                                                            @Valid String inspectionType,
                                                            @Valid Integer shippingLineId,
                                                            @Valid BigDecimal containerTypeId,
                                                            @Valid BigDecimal containerSizeId,
                                                            @Valid String cedexSetup) {
        CedexSetupCorrectOutput output = new CedexSetupCorrectOutput();
        try {
            // Call the stored procedure via JPA NamedStoredProcedureQuery
            Map<String, Object> result = cedexMercRepository.callCedexSetupCorrect(
                    subBusinessUnitId,
                    inspectionType,
                    shippingLineId,
                    containerTypeId,
                    containerSizeId,
                    cedexSetup);

            // The procedure sets @resp_status and @resp_message.
            // They come back in the result map using the out parameter names.
            Integer respStatus = (Integer) result.get("@resp_status");
            String respMessage = (String) result.get("@resp_message");

            output.setRespStatus(respStatus);
            output.setRespMessage(respMessage);
        } catch (Exception e) {
            logger.error("Error calling cedex_setup_correct procedure", e);
            output.setRespStatus(0);
            output.setRespMessage(e.getMessage());
        }
        return output;
    }
}
