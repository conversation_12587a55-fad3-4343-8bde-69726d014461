package com.maersk.sd1.sde.service;

import com.maersk.sd1.common.model.Catalog;
import com.maersk.sd1.common.repository.CatalogLanguageRepository;
import com.maersk.sd1.common.repository.CatalogRepository;
import com.maersk.sd1.common.model.CedexMerc;
import com.maersk.sd1.sde.dto.BuscarCedexMercXCodigoInput;
import com.maersk.sd1.sde.dto.BuscarCedexMercXCodigoOutput;
import com.maersk.sd1.common.repository.CedexMercRepository;
import org.hibernate.service.spi.ServiceException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.ArrayList;
import java.util.List;

@Service
public class BuscarCedexMercXCodigoService {

    private static final Logger logger = LogManager.getLogger(BuscarCedexMercXCodigoService.class);

    private final CedexMercRepository cedexMercRepository;
    private final CatalogRepository catalogRepository;
    private final CatalogLanguageRepository catalogLanguageRepository;

    @Autowired
    public BuscarCedexMercXCodigoService(CedexMercRepository cedexMercRepository,
                                         CatalogRepository catalogRepository,
                                         CatalogLanguageRepository catalogLanguageRepository) {
        this.cedexMercRepository = cedexMercRepository;
        this.catalogRepository = catalogRepository;
        this.catalogLanguageRepository = catalogLanguageRepository;
    }

    @Transactional(readOnly = true)
    public List<BuscarCedexMercXCodigoOutput> buscarCedexMercXCodigo(BuscarCedexMercXCodigoInput.Input input) {
        try {
            Integer catEquipmentCategoryId = getCatEquipmentCategoryId();
            Integer shippingLineId = adjustShippingLineId(input.getShippingLineId());
            String localizacionDano = getLocalizacionDano(input.getCatEstimadoUbicacionDanoId());
            String codigoComponente = getCodigoComponente(input.getCatEstimadoComponenteId());
            String codigoMetodoRep = getCodigoMetodoRep(input.getCatEstimadoMetodoRepId());
            String dimension = getDimension(input.getCatEstimadoDimensionTipoId());

            if (areAllFieldsEmpty(codigoComponente, codigoMetodoRep, localizacionDano)) {
                input.setPiezasReparar(0);
            }

            String tipoEstimado = getTipoEstimado(input.getCatTipoEstimadoId(), input.getIdiomaId());

            List<CedexMerc> entities = queryCedexMerc(input, catEquipmentCategoryId, shippingLineId, localizacionDano, codigoComponente, codigoMetodoRep, dimension);

            return mapToOutputDTO(entities, tipoEstimado);
        } catch (Exception e) {
            logger.error("Error during buscarCedexMercXCodigo", e);
            throw new ServiceException("Error fetching Cedex Merc data.", e);
        }
    }

    private Integer getCatEquipmentCategoryId() {
        Catalog catEquipmentContainer = catalogRepository.findByAlias("sd1_equipment_category_container");
        return catEquipmentContainer != null ? catEquipmentContainer.getId() : null;
    }

    private Integer adjustShippingLineId(Integer shippingLineId) {
        if (shippingLineId == null || shippingLineId == 4102) {
            shippingLineId = 4104;
        }
        return shippingLineId;
    }

    private String getLocalizacionDano(Long catEstimadoUbicacionDanoId) {
        if (catEstimadoUbicacionDanoId != null) {
            Catalog ubicacionCatalog = catalogRepository.findById(catEstimadoUbicacionDanoId.intValue()).orElse(null);
            if (ubicacionCatalog != null && ubicacionCatalog.getDescription() != null) {
                String val = ubicacionCatalog.getDescription().trim();
                if (val.length() > 2) {
                    val = val.substring(0, 2);
                }
                return val;
            }
        }
        return "";
    }

    private String getCodigoComponente(Long catEstimadoComponenteId) {
        if (catEstimadoComponenteId != null) {
            Catalog componenteCat = catalogRepository.findById(catEstimadoComponenteId.intValue()).orElse(null);
            if (componenteCat != null && componenteCat.getDescription() != null) {
                return componenteCat.getDescription();
            }
        }
        return "";
    }

    private String getCodigoMetodoRep(Long catEstimadoMetodoRepId) {
        if (catEstimadoMetodoRepId != null) {
            Catalog metodoCat = catalogRepository.findById(catEstimadoMetodoRepId.intValue()).orElse(null);
            if (metodoCat != null && metodoCat.getDescription() != null) {
                return metodoCat.getDescription();
            }
        }
        return "";
    }

    private String getDimension(Long catEstimadoDimensionTipoId) {
        if (catEstimadoDimensionTipoId != null) {
            Catalog dimensionCat = catalogRepository.findById(catEstimadoDimensionTipoId.intValue()).orElse(null);
            if (dimensionCat != null && dimensionCat.getDescription() != null) {
                return dimensionCat.getDescription();
            }
        }
        return "";
    }

    private boolean areAllFieldsEmpty(String codigoComponente, String codigoMetodoRep, String localizacionDano) {
        return (codigoComponente == null || codigoComponente.isBlank()) &&
                (codigoMetodoRep == null || codigoMetodoRep.isBlank()) &&
                (localizacionDano == null || localizacionDano.isBlank());
    }

    private String getTipoEstimado(Long catTipoEstimadoId, Integer idiomaId) {
        String tipoEstimado = catalogLanguageRepository.fnCatalogoTraducidoDesLarga(catTipoEstimadoId.intValue(), idiomaId);
        return (tipoEstimado == null || tipoEstimado.isEmpty()) ? "" : tipoEstimado;
    }

    private List<CedexMerc> queryCedexMerc(BuscarCedexMercXCodigoInput.Input input, Integer catEquipmentCategoryId, Integer shippingLineId, String localizacionDano, String codigoComponente, String codigoMetodoRep, String dimension) {
        return cedexMercRepository.filterCedexMerc(
                input.getSubUnidadNegocioId(),
                input.getCatTipoEstimadoId(),
                input.getEsReefer(),
                shippingLineId,
                catEquipmentCategoryId,
                input.getCedexMercCodigo() == null ? "" : input.getCedexMercCodigo(),
                input.getCedexMercDescripcion() == null ? "" : input.getCedexMercDescripcion(),
                codigoComponente,
                codigoMetodoRep,
                localizacionDano,
                dimension,
                input.getPiezasReparar() == null ? 0 : input.getPiezasReparar(),
                input.getMonedaId()
        );
    }

    private List<BuscarCedexMercXCodigoOutput> mapToOutputDTO(List<CedexMerc> entities, String tipoEstimado) {
        List<BuscarCedexMercXCodigoOutput> result = new ArrayList<>();
        for (CedexMerc cm : entities) {
            BuscarCedexMercXCodigoOutput dto = new BuscarCedexMercXCodigoOutput();

            dto.setCedexMercCode(cm.getCedexMercCode());
            dto.setCedexMercDescription(cm.getCedexMercDescription());
            dto.setMercManHours(cm.getMercManHours());
            dto.setMercCostMaterial(cm.getMercMaterialCost());
            dto.setDamageLocations(cm.getDamageLocations());
            dto.setMercMaxParts(cm.getMercMaxParts());
            dto.setMercDimension(cm.getMercDimension());
            dto.setMercMinValue(cm.getMercMinValue());
            dto.setMercMaxValue(cm.getMercMaxValue());
            dto.setContainerSize(cm.getContainerSize());
            dto.setCurrencyName((cm.getCurrency() != null) ? cm.getCurrency().getName() : null);
            dto.setMercObservation(cm.getMercObservation());

            setComponentDetails(dto, cm.getComponentCode());
            setMethodDetails(dto, cm.getCodeRepMethod());
            setDimensionDetails(dto, cm.getMercDimension());

            dto.setTipoEstimado(tipoEstimado);

            result.add(dto);
        }
        return result;
    }

    private void setComponentDetails(BuscarCedexMercXCodigoOutput dto, String componentCode) {
        if (componentCode != null && !componentCode.isBlank()) {
            Catalog catComponente = catalogRepository.findByParentAndDescriptionActive(46563, componentCode).orElse(null);
            if (catComponente != null) {
                dto.setCatEstimateComponentId(catComponente.getId());
                dto.setComponentLongDescription(catComponente.getLongDescription());
            }
        }
    }

    private void setMethodDetails(BuscarCedexMercXCodigoOutput dto, String codeRepMethod) {
        if (codeRepMethod != null && !codeRepMethod.isBlank()) {
            Catalog catMetodo = catalogRepository.findByParentAndDescriptionActive(47378, codeRepMethod).orElse(null);
            if (catMetodo != null) {
                dto.setCatEstimateMethodRepId(catMetodo.getId());
                dto.setMethodLongDescription(catMetodo.getLongDescription());
            }
        }
    }

    private void setDimensionDetails(BuscarCedexMercXCodigoOutput dto, String mercDimension) {
        if (mercDimension != null && !mercDimension.isBlank()) {
            Catalog catDimension = catalogRepository.findByParentAndDescriptionActive(47478, mercDimension).orElse(null);
            if (catDimension != null) {
                dto.setDimensionId(catDimension.getId());
            }
        }
    }
}