package com.maersk.sd1.sde.service;

import com.maersk.sd1.common.model.User;
import com.maersk.sd1.sde.dto.GateTransmissionSettingRemoveOutput;
import com.maersk.sd1.common.repository.GateTransmissionSettingRepository;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

@RequiredArgsConstructor
@Service
public class GateTransmissionSettingRemoveService {

    private static final Logger logger = LogManager.getLogger(GateTransmissionSettingRemoveService.class);

    private final GateTransmissionSettingRepository gateTransmissionSettingRepository;

    @Transactional
    public GateTransmissionSettingRemoveOutput removeGateTransmissionSetting(Integer seteoEdiCodecoId,
                                                                             Integer usuarioModificacionId) {
        GateTransmissionSettingRemoveOutput output = new GateTransmissionSettingRemoveOutput();
        try {
            logger.info("Starting removeGateTransmissionSetting with id: {} and user: {}", seteoEdiCodecoId, usuarioModificacionId);

            User modificationUser = new User();
            modificationUser.setId(usuarioModificacionId);

            int rowsUpdated = gateTransmissionSettingRepository.deactivateGateTransmissionSetting(
                    seteoEdiCodecoId,
                    modificationUser,
                    LocalDateTime.now()
            );

            if (rowsUpdated > 0) {
                output.setRespEstado(1);
                output.setRespMensaje("Record deleted successfully");
            } else {
                output.setRespEstado(0);
                output.setRespMensaje("No record was found with the specified ID");
            }

            logger.info("Finished removeGateTransmissionSetting successfully.");

        } catch (Exception e) {
            logger.error("Error in removeGateTransmissionSetting", e);
            output.setRespEstado(0);
            output.setRespMensaje(e.getMessage());
        }

        return output;
    }
}
