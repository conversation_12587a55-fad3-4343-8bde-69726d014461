package com.maersk.sd1.sde.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

@Data
public class ContainerRestrictionReleaseInput {

    @Data
    public static class Input {

        @JsonProperty("language_id")
        @NotNull
        private Integer languageId;

        @JsonProperty("restriction_id")
        @NotNull
        private Integer restrictionId;

        @JsonProperty("observation")
        @Size(max = 200)
        private String observation;

        @JsonProperty("cat_origin_release_restriction_alias")
        @Size(max = 10)
        private String catOriginReleaseRestrictionAlias;

        @JsonProperty("user_modification_id")
        @NotNull
        private Integer userModificationId;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private ContainerRestrictionReleaseInput.Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("SDE")
        private ContainerRestrictionReleaseInput.Prefix prefix;
    }
}
