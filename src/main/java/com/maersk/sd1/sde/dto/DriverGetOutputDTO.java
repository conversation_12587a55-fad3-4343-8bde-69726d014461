package com.maersk.sd1.sde.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
@JsonFormat(shape = JsonFormat.Shape.ARRAY)
public class DriverGetOutputDTO {
    @JsonProperty("person_id")
    private Integer personId;

    @JsonProperty("full_name")
    private String fullName;

    @JsonProperty("identity_document")
    private String identityDocument;

    @JsonProperty("driver_license")
    private String driverLicense;

    @JsonProperty("employment_status")
    private Integer employmentStatus;

    @JsonProperty("response_status")
    private Integer responseStatus;

    @JsonProperty("response_message")
    private String responseMessage;
}
