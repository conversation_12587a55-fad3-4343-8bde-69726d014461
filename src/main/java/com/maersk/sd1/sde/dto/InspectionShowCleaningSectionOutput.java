package com.maersk.sd1.sde.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class InspectionShowCleaningSectionOutput {

    @JsonProperty("cedex_merc_id")
    private Integer cedexMercId;

    @JsonProperty("flag_show_cleaning_section_interior")
    private Boolean flagShowCleaningSectionInterior;

    @JsonProperty("flag_show_cleaning_section_bottom")
    private Boolean flagShowCleaningSectionBottom;

    @JsonProperty("flag_show_cleaning_section_top")
    private Boolean flagShowCleaningSectionTop;

    @JsonProperty("flag_show_cleaning_section_right")
    private Boolean flagShowCleaningSectionRight;

    @JsonProperty("flag_show_cleaning_section_left")
    private Boolean flagShowCleaningSectionLeft;
}
