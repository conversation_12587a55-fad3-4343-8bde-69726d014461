package com.maersk.sd1.business.core.planing.domain.planificacion_cantidadremovidos;

import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ActualizarCantidadRemovidosDataCommand {
	private List<ActualizarCantidadRemovidosDataCommandItems> bloques_filas;
	private int usuario_id;
}
