package com.maersk.sd1.business.core.planing.usecase.planificacion;

import com.maersk.sd1.business.core.planing.domain.planificacion_contenedorsalida.BusquedaSalidaContenedoresCommand;
import com.maersk.sd1.business.core.planing.domain.planificacion_contenedorsalida.BusquedaSalidaResponse;

public interface BuscarContenedoresParaSalidaUseCase {

	public BusquedaSalidaResponse execute(BusquedaSalidaContenedoresCommand command) throws Exception;

}
