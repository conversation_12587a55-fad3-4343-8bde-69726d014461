package com.maersk.sd1.business.core.planing.usecase.planificacion.reglaubicacion;

import java.util.ArrayList;
import java.util.Collection;

import com.maersk.sd1.business.core.planing.domain.Contenedor;
import com.maersk.sd1.business.core.planing.domain.ReglaPlanificacionPatio;
import com.maersk.sd1.business.core.planing.domain.planificacion.CriterioBusquedaReglaContenedor;
import com.maersk.sd1.business.core.planing.domain.planificacion.DatosPlanificacion;
import com.maersk.sd1.business.core.planing.domain.planificacion.DatosPlanificacionContenedor;
import com.maersk.sd1.business.core.shared.ResultadoValidacion;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
public class ObtenerReglasPlanificacionUseCaseImpl implements ObtenerReglasPlanificacionUseCase {

	private final ManejadorReglasPlanificacionUseCase manejadorReglas;

	@Override
	public Collection<ResultadoValidacion> EncontrarRegla(DatosPlanificacion datos) throws Exception {
				
		Collection<CriterioBusquedaReglaContenedor> rulesCriteria = CriterioBusquedaReglaContenedor.crear(datos);		
		datos.CargarCriterios(rulesCriteria);
		
		Collection<ResultadoValidacion> resultado = new ArrayList<ResultadoValidacion>();
		
		Collection<ReglaPlanificacionPatio> rules = new ArrayList<ReglaPlanificacionPatio>();				
		if (datos.getBloque_destino() != null) {
			Collection<ReglaPlanificacionPatio> rulesByBlockFound = datos.getBloqueOmitirId() == null && datos.getIndicesFilaOmitidos() == null || datos.getIndicesFilaOmitidos().isEmpty() ? manejadorReglas.FindRulesByCriteriaInBlock(rulesCriteria, datos.getBloque_destino().getBloque_id()) : manejadorReglas.FindRulesByCriteriaInBlock(rulesCriteria, datos.getBloque_destino().getBloque_id(), datos.getIndicesFilaOmitidos(), datos.getBloqueOmitirId());
			rules.addAll(rulesByBlockFound);
		} else {			
			Collection<ReglaPlanificacionPatio> rulesFoundByYard = datos.getBloqueOmitirId() == null && datos.getIndicesFilaOmitidos() == null || datos.getIndicesFilaOmitidos().isEmpty() ? manejadorReglas.FindRulesByCriteriaInYard(rulesCriteria, datos.getPatio_id()) : manejadorReglas.FindRulesByCriteriaInYard(rulesCriteria, datos.getPatio_id(), datos.getIndicesFilaOmitidos(), datos.getBloqueOmitirId());
			rules.addAll(rulesFoundByYard);
		}
		
		rulesCriteria.forEach(criteria -> {
			
			DatosPlanificacionContenedor equipmentPlanificationData = datos.getEquipmentPlanificationData()
					.stream()
					.filter(item -> item.getContenedor().getContenedor_id() == criteria.getContenedor_id())
					.findFirst().get();
			
			Contenedor equipment = equipmentPlanificationData.getContenedor();
			
			ReglaPlanificacionPatio rule = rules.stream().filter(item -> item.getContenedor_id() == criteria.getContenedor_id()).findFirst().orElse(null);
			if(rule != null) {					
				equipmentPlanificationData.setRegla_seleccionada(rule);
				equipmentPlanificationData.getCriterios_seleccion_regla().setContenedor(equipment);
				resultado.add(ResultadoValidacion.Success());
			} else {									
				resultado.add(ResultadoValidacion
						.Fail("No Rule for Container: "
								+ equipment.getNumero_contenedor()));
			}				
		});
		
		return resultado;
	}
}
