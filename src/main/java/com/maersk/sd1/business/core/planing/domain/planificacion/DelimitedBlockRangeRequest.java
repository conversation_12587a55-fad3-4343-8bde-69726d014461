package com.maersk.sd1.business.core.planing.domain.planificacion;

import com.maersk.sd1.business.core.planing.domain.RangoReglaPlanificacionPatio;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class DelimitedBlockRangeRequest {
	private int RowIndexBegin;
	private int RowIndexEnd;
	private int ColIndexBegin;
	private int ColIndexEnd;
	private int BloqueId;
	private int rango_regla_planificacion_patio_id;

	
	public DelimitedBlockRangeRequest(Integer rowIndexFrom, Integer rowIndexTo, Integer columnIndexFrom, Integer columnIndexTo,
        RangoReglaPlanificacionPatio range)
    {
        RowIndexBegin = GetRowIndexBegin(rowIndexFrom, rowIndexTo, range);
        RowIndexEnd = GetRowIndexEnd(rowIndexFrom, rowIndexTo, range);

        ColIndexBegin = GetColIndexBegin(columnIndexFrom, columnIndexTo, range);
        ColIndexEnd = GetColIndexEnd(columnIndexFrom, columnIndexTo, range);
        
        if(range != null) {
        	BloqueId = range.getBloque_id();
            rango_regla_planificacion_patio_id = range.getRango_regla_planificacion_patio_id();	
        }        
    }

	public static <T> T getValueOrDefault(T value, T defaultValue) {
	    return value == null ? defaultValue : value;
	}
	
	private int GetRowIndexBegin(Integer rowIndexFrom, Integer rowIndexTo, RangoReglaPlanificacionPatio range)
    {
        range.IsRowOrderDescendent = (rowIndexFrom == null ? 0 : rowIndexFrom) > (rowIndexTo == null ? 0 : rowIndexTo);

        return ((rowIndexFrom == null ? 1 : rowIndexFrom)) < (rowIndexTo == null ? range.Bloque.getFilas() : rowIndexTo) ?
                (rowIndexFrom == null ? 1 : rowIndexFrom) : (rowIndexTo == null ? range.Bloque.getFilas() : rowIndexTo);
    }

	private int GetColIndexBegin(Integer columnIndexFrom, Integer columnIndexTo, RangoReglaPlanificacionPatio range)
    {
        //range.IsColOrderDescendent = (columnIndexFrom == null ? 0 : columnIndexFrom) > (columnIndexTo == null ? 0 : columnIndexTo);
        return (columnIndexFrom == null ? 1 : columnIndexFrom) < (columnIndexTo == null ? range.Bloque.getColumnas() : columnIndexTo) ?
            (columnIndexFrom == null ? 1 : columnIndexFrom) : (columnIndexTo == null ? range.Bloque.getColumnas() : columnIndexTo);
    }

	private int GetRowIndexEnd(Integer rowIndexFrom, Integer rowIndexTo, RangoReglaPlanificacionPatio range)
    {
        range.IsRowOrderDescendent = (rowIndexFrom == null ? 0 : rowIndexFrom) > (rowIndexTo == null ? 0 : rowIndexTo);
        return (rowIndexTo == null ? 1 : rowIndexTo) > (rowIndexFrom == null ? range.Bloque.getFilas() : rowIndexFrom) ?
            (rowIndexTo == null ? 1 : rowIndexTo) : (rowIndexFrom == null ? range.Bloque.getFilas() : rowIndexFrom);
    }

	private int GetColIndexEnd(Integer columnIndexFrom, Integer columnIndexTo, RangoReglaPlanificacionPatio range)
    {
        //range.IsColOrderDescendent = (columnIndexFrom == null ? 0 : columnIndexFrom) > (columnIndexTo == null ? 0 : columnIndexTo);
        return (columnIndexTo == null ? range.Bloque.getColumnas() : columnIndexTo) > (columnIndexFrom == null ? range.Bloque.getColumnas() : columnIndexFrom) ?
               (columnIndexTo == null ? 1 : columnIndexTo) : (columnIndexFrom == null ? range.Bloque.getColumnas() : columnIndexFrom);
    }
	
	@Override
	public boolean equals(Object obj) {
		if (obj == this)
			return true;
		if(!(obj instanceof DelimitedBlockRangeRequest))
			return false;
		DelimitedBlockRangeRequest u = (DelimitedBlockRangeRequest)obj;
		return  u.RowIndexBegin == this.RowIndexBegin &&
				u.ColIndexBegin == this.ColIndexBegin &&
				u.RowIndexEnd == this.RowIndexEnd &&
				u.ColIndexEnd == this.ColIndexEnd;
	}
}
