package com.maersk.sd1.business.core.planing.domain;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
public class Tipo {
	private int id;
	private Integer padre_id;
	private String codigo;
	private String descripcion;
	private boolean activo;
	private String variable_2;
	private Integer variable_3;
	private Integer Indice;
	
	public Tipo(int id, Integer padre_id, String codigo, String nombre, boolean activo) {
		this.id = id;
		this.padre_id = padre_id;
		this.codigo = codigo;
		this.descripcion = nombre;
		this.activo = activo;
	}
	
	public Tipo(int id, String codigo, String nombre, Integer variable_3) {
		this.id = id;
		this.codigo = codigo;
		this.descripcion = nombre;
		this.variable_3 = variable_3;
	}
}
