package com.maersk.sd1.business.core.planing.usecase.planificacion;

import com.maersk.sd1.business.core.planing.domain.planificacion.ConfirmarInstruccionMovimientoCommand;
import com.maersk.sd1.business.core.planing.domain.planificacion.ConfirmarInstruccionMovimientoResponse;
import com.maersk.sd1.business.core.planing.domain.planificacion.confirm_move_instruction.ConfirmMoveInstructionCommand;

public interface ConfirmarInstruccionMovimientoUseCase {
	
	public ConfirmarInstruccionMovimientoResponse Execute(ConfirmarInstruccionMovimientoCommand command) throws Exception;

	public ConfirmarInstruccionMovimientoResponse confirmMoveInstruction(ConfirmMoveInstructionCommand command) throws Exception;
	
	public ConfirmarInstruccionMovimientoResponse ExecutePlanning(ConfirmMoveInstructionCommand command) throws Exception;
}
