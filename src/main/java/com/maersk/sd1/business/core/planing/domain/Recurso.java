package com.maersk.sd1.business.core.planing.domain;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

@Setter
@Getter
@SuperBuilder
public class Recurso {
	private int id;
	private Tipo tipo_recurso;
	private Tipo tipo_carga_contenedor;
//	private Patio patio;
	private String codigo;
	private String nombre;
	private String marca;
	private int maximo_nivel_apilamiento;
	private boolean activo;
	private Integer unidad_negocio_id;
}
