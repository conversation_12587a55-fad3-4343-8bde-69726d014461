package com.maersk.sd1.business.infraestructure.dto.planificacion;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class AtenderPlanificacionCommandDto {
	
	public AtenderPlanificacionCommandDto(int usuario_id) {
		this.usuario_id = usuario_id;
	}
	private String instrucciones_movimiento;
	private int usuario_id;
	
	public void addMoveInstructionId(Integer instruccion_numero) {
		
	}
}
