package com.maersk.sd1.business.service.module.sdy;

import jakarta.servlet.http.HttpServletRequest;
import ohSolutions.ohJpo.dao.Jpo;
import ohSolutions.ohJpo.dao.JpoClass;
import ohSolutions.ohJpo.dao.Procedure;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import ohSolutions.ohRest.util.bean.Response;

@JpoClass(source = "dsinland", oauth2Enable = true)
public class SDYPatioService {

//	@RequestMapping(value = "/sdypatioRegistrar", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdypatioRegistrar(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("sdy.patio_registrar","SDY");
			pResult.input("plano_id", Jpo.INTEGER);
			pResult.input("codigo", Jpo.STRING);
			pResult.input("nombre", Jpo.STRING);
			pResult.input("zoom", Jpo.INTEGER);
			pResult.input("latitud", Jpo.DECIMAL);
			pResult.input("color", Jpo.STRING);
			pResult.input("longitud", Jpo.DECIMAL);
			pResult.input("configuracion", Jpo.STRING);
			pResult.input("activo", Jpo.CHARACTER);
			pResult.input("usuario_registro_id", Jpo.DECIMAL);
			pResult.input("unidad_negocio_id", Jpo.INTEGER);
			pResult.input("configuracion_plano", Jpo.STRING);
			pResult.input("uid_seleccionada", Jpo.STRING);
			pResult.input("idioma_id", Jpo.INTEGER);
			pResult.output("resp_new_id", Jpo.INTEGER);
			pResult.output("resp_estado", Jpo.INTEGER);
			pResult.output("resp_mensaje", Jpo.STRING);
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

//	@RequestMapping(value = "/sdypatioEditar", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdypatioEditar(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("sdy.patio_editar","SDY");
			pResult.input("patio_id", Jpo.INTEGER);
			pResult.input("plano_id", Jpo.INTEGER);
			pResult.input("codigo", Jpo.STRING);
			pResult.input("nombre", Jpo.STRING);
			pResult.input("zoom", Jpo.INTEGER);
			pResult.input("latitud", Jpo.DECIMAL);
			pResult.input("color", Jpo.STRING);
			pResult.input("longitud", Jpo.DECIMAL);
			pResult.input("configuracion", Jpo.STRING);
			pResult.input("activo", Jpo.CHARACTER);
			pResult.input("usuario_modificacion_id", Jpo.DECIMAL);
			pResult.input("uid_seleccionada", Jpo.STRING);
			pResult.input("idioma_id", Jpo.INTEGER);
			pResult.output("resp_estado", Jpo.INTEGER);
			pResult.output("resp_mensaje", Jpo.STRING);
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

//	@RequestMapping(value = "/sdypatioObtener", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdypatioObtener(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("sdy.patio_obtener","SDY");
			pResult.input("patio_id", Jpo.INTEGER);
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

//	@RequestMapping(value = "/sdypatioListar", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdypatioListar(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("sdy.patio_listar","SDY");
			pResult.input("patio_id", Jpo.INTEGER);
			pResult.input("plano_id", Jpo.INTEGER);
			pResult.input("codigo", Jpo.STRING);
			pResult.input("nombre", Jpo.STRING);
			pResult.input("color", Jpo.STRING);
			pResult.input("configuracion", Jpo.STRING);
			pResult.input("unidad_negocio_id", Jpo.INTEGER);
			pResult.input("activo", Jpo.CHARACTER);
			pResult.input("fecha_registro_min", Jpo.DATE);
			pResult.input("fecha_registro_max", Jpo.DATE);
			pResult.input("fecha_modificacion_min", Jpo.DATE);
			pResult.input("fecha_modificacion_max", Jpo.DATE);
			pResult.input("page", Jpo.INTEGER);
			pResult.input("size", Jpo.INTEGER);
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

//	@RequestMapping(value = "/sdypatioEliminar", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdypatioEliminar(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("sdy.patio_eliminar","SDY");
			pResult.input("patio_id", Jpo.DECIMAL);
			pResult.input("usuario_modificacion_id", Jpo.DECIMAL);
			pResult.input("idioma_id", Jpo.INTEGER);
			pResult.output("resp_estado", Jpo.INTEGER);
			pResult.output("resp_mensaje", Jpo.STRING);
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

//	@RequestMapping(value = "/sdyobtenerPatioBySubUnidadNegocio", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdyobtenerPatioBySubUnidadNegocio(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("sdy.obtener_patio_by_sub_unidad_negocio","SDY");
			pResult.input("sub_unidad_negocio_local_id", Jpo.INTEGER);
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

	@RequestMapping(value = "/sdyfindContainerByBusinessUnitAndNumber", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdyfindContainerByBusinessUnitAndNumber(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("sdy.find_container_by_business_unit_and_number","SDY");
			pResult.input("container_number", Jpo.STRING);
			pResult.input("yard_id", Jpo.INTEGER);
			pResult.input("cat_empty_full_id", Jpo.INTEGER);
			pResult.input("cat_container_size_id", Jpo.INTEGER);
			pResult.input("cat_container_family_id", Jpo.INTEGER);
			pResult.input("cat_container_type_id", Jpo.INTEGER);
			pResult.input("cat_container_class_id", Jpo.INTEGER);
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

}