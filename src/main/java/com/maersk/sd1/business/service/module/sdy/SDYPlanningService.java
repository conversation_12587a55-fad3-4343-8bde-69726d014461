package com.maersk.sd1.business.service.module.sdy;

import jakarta.servlet.http.HttpServletRequest;
import ohSolutions.ohJpo.dao.Jpo;
import ohSolutions.ohJpo.dao.JpoClass;
import ohSolutions.ohJpo.dao.Procedure;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import ohSolutions.ohRest.util.bean.Response;

@JpoClass(source = "dsinland", oauth2Enable = true)
public class SDYPlanningService {

	@RequestMapping(value = "/sdyplanningPlaneGet", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdyplanningPlaneGet(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("sdy.planning_plane_get","SDY");
			pResult.input("sub_business_unit_id", Jpo.INTEGER);
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

	@RequestMapping(value = "/sdycontainerLocationGetIds", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdycontainerLocationGetIds(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("sdy.container_location_get_ids","SDY");
			pResult.input("business_unit_id", Jpo.INTEGER);
			pResult.input("block_code", Jpo.STRING);
			pResult.input("row", Jpo.INTEGER);
			pResult.input("cell_code", Jpo.STRING);
			pResult.input("index", Jpo.INTEGER);
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

	@RequestMapping(value = "/sdycontainerSearchFind", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdycontainerSearchFind(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("sdy.container_search_find","SDY");
			pResult.input("sub_business_unit_id", Jpo.INTEGER);
			pResult.input("container_number", Jpo.STRING);
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

}