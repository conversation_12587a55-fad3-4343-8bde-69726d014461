package com.maersk.sd1.business.service.module.sdy;

import jakarta.servlet.http.HttpServletRequest;
import ohSolutions.ohJpo.dao.Jpo;
import ohSolutions.ohJpo.dao.JpoClass;
import ohSolutions.ohJpo.dao.Procedure;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import ohSolutions.ohRest.util.bean.Response;

@JpoClass(source = "dsinland", oauth2Enable = true)
public class SDYRecursoService {

	@RequestMapping(value = "/sdyrecursoEditar", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdyrecursoEditar(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("sdy.recurso_editar","SDY");
			pResult.input("recurso_id", Jpo.INTEGER);
			pResult.input("cat_recurso_id", Jpo.DECIMAL);
			pResult.input("cat_carga_contenedor_id", Jpo.DECIMAL);
			pResult.input("patio_id", Jpo.INTEGER);
			pResult.input("codigo", Jpo.STRING);
			pResult.input("nombre", Jpo.STRING);
			pResult.input("marca", Jpo.STRING);
			pResult.input("maximo_nivel_apilamiento", Jpo.INTEGER);
			pResult.input("activo", Jpo.CHARACTER);
			pResult.input("usuario_modificacion_id", Jpo.DECIMAL);
			pResult.input("punto_trabajo_id", Jpo.INTEGER);
			pResult.input("unidad_negocio_id", Jpo.INTEGER);
			pResult.input("idioma_id", Jpo.INTEGER);
			pResult.output("resp_estado", Jpo.INTEGER);
			pResult.output("resp_mensaje", Jpo.STRING);
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

//	@RequestMapping(value = "/sdyrecursoRegistrar", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdyrecursoRegistrar(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("sdy.recurso_registrar","SDY");
			pResult.input("cat_recurso_id", Jpo.DECIMAL);
			pResult.input("cat_carga_contenedor_id", Jpo.DECIMAL);
			pResult.input("patio_id", Jpo.INTEGER);
			pResult.input("codigo", Jpo.STRING);
			pResult.input("nombre", Jpo.STRING);
			pResult.input("marca", Jpo.STRING);
			pResult.input("maximo_nivel_apilamiento", Jpo.INTEGER);
			pResult.input("activo", Jpo.CHARACTER);
			pResult.input("usuario_registro_id", Jpo.DECIMAL);
			pResult.input("punto_trabajo_id", Jpo.INTEGER);
			pResult.input("unidad_negocio_id", Jpo.INTEGER);
			pResult.input("idioma_id", Jpo.INTEGER);
			pResult.output("resp_new_id", Jpo.INTEGER);
			pResult.output("resp_estado", Jpo.INTEGER);
			pResult.output("resp_mensaje", Jpo.STRING);
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

//	@RequestMapping(value = "/sdyrecursoObtener", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdyrecursoObtener(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("sdy.recurso_obtener","SDY");
			pResult.input("recurso_id", Jpo.INTEGER);
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

//	@RequestMapping(value = "/sdyrecursoListar", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdyrecursoListar(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("sdy.recurso_listar","SDY");
			pResult.input("recurso_id", Jpo.INTEGER);
			pResult.input("cat_recurso_id", Jpo.DECIMAL);
			pResult.input("cat_carga_contenedor_id", Jpo.DECIMAL);
			pResult.input("patio_id", Jpo.INTEGER);
			pResult.input("codigo", Jpo.STRING);
			pResult.input("nombre", Jpo.STRING);
			pResult.input("marca", Jpo.STRING);
			pResult.input("maximo_nivel_apilamiento", Jpo.INTEGER);
			pResult.input("activo", Jpo.CHARACTER);
			pResult.input("fecha_registro_min", Jpo.DATE);
			pResult.input("fecha_registro_max", Jpo.DATE);
			pResult.input("fecha_modificacion_min", Jpo.DATE);
			pResult.input("fecha_modificacion_max", Jpo.DATE);
			pResult.input("punto_trabajo_id", Jpo.INTEGER);
			pResult.input("sub_unidad_negocio_id", Jpo.INTEGER);
			pResult.input("page", Jpo.INTEGER);
			pResult.input("size", Jpo.INTEGER);
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

	@RequestMapping(value = "/sdyrecursoEliminar", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdyrecursoEliminar(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("sdy.recurso_eliminar","SDY");
			pResult.input("recurso_id", Jpo.DECIMAL);
			pResult.input("usuario_modificacion_id", Jpo.DECIMAL);
			pResult.input("idioma_id", Jpo.INTEGER);
			pResult.output("resp_estado", Jpo.INTEGER);
			pResult.output("resp_mensaje", Jpo.STRING);
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

}