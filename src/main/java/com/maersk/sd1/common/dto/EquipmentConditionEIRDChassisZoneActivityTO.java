package com.maersk.sd1.common.dto;

import lombok.Data;

@Data
public class EquipmentConditionEIRDChassisZoneActivityTO {

    private Integer eirChassisZoneActivityId;
    private Boolean completed;
    private Boolean isPartialInspection;
    private Boolean structureChassisDamaged;

    public EquipmentConditionEIRDChassisZoneActivityTO(Integer eirChassisZoneActivityId, Boolean completed, Boolean isPartialInspection, Boolean structureChassisDamaged) {
        this.eirChassisZoneActivityId = eirChassisZoneActivityId;
        this.completed = completed;
        this.isPartialInspection = isPartialInspection;
        this.structureChassisDamaged = structureChassisDamaged;
    }

}