package com.maersk.sd1.common.model;

import com.maersk.sd1.common.model.BusinessUnit;
import com.maersk.sd1.common.model.User;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.*;
import jakarta.persistence.*;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.annotations.Nationalized;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@Table(name = "patio", schema = "sdy")
public class Yard {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "patio_id", nullable = false)
    private Integer id;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "plano_id", nullable = false)
    private Layout layout;

    @Size(max = 10)
    @NotNull
    @Column(name = "codigo", nullable = false, length = 10)
    private String code;

    @Size(max = 100)
    @Column(name = "nombre", length = 100)
    private String name;

    @Column(name = "zoom")
    private Integer zoom;

    @Column(name = "latitud", precision = 19, scale = 7)
    private BigDecimal latitude;

    @Size(max = 20)
    @Nationalized
    @Column(name = "color", length = 20)
    private String color;

    @Column(name = "longitud", precision = 19, scale = 7)
    private BigDecimal longitude;

    @NotNull
    @Lob
    @Column(name = "configuracion", nullable = false)
    private String configuration;

    @NotNull
    @ColumnDefault("1")
    @Column(name = "activo", nullable = false)
    private Boolean active = false;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "usuario_registro_id", nullable = false)
    private User registrationUser;

    @NotNull
    @ColumnDefault("getdate()")
    @Column(name = "fecha_registro", nullable = false)
    private LocalDateTime registrationDate;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "usuario_modificacion_id")
    private User modificationUser;

    @Column(name = "fecha_modificacion")
    private LocalDateTime modificationDate;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "unidad_negocio_id")
    private BusinessUnit businessUnit;

    public Yard(Integer yardId) {
        this.id = yardId;
    }
}