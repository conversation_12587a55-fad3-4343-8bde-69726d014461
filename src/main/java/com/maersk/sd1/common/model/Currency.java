package com.maersk.sd1.common.model;

import com.maersk.sd1.common.model.User;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.*;
import jakarta.persistence.*;
import org.hibernate.annotations.ColumnDefault;

import java.time.LocalDateTime;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@Table(name = "moneda", schema = "ges")
public class Currency {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "moneda_id", nullable = false)
    private Integer id;

    @Size(max = 100)
    @NotNull
    @Column(name = "nombre", nullable = false, length = 100)
    private String name;

    @Size(max = 5)
    @NotNull
    @Column(name = "abreviatura", nullable = false, length = 5)
    private String abbreviation;

    @Size(max = 5)
    @NotNull
    @Column(name = "simbolo", nullable = false, length = 5)
    private String symbol;

    @Column(name = "estado")
    private Integer status;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "usuario_registro_id", nullable = false)
    private User registrationUser;

    @NotNull
    @ColumnDefault("getdate()")
    @Column(name = "fecha_registro", nullable = false)
    private LocalDateTime registrationDate;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "usuario_modificacion_id")
    private User modificationUser;

    @Column(name = "fecha_modificacion")
    private LocalDateTime modificationDate;

    @Size(max = 5)
    @Column(name = "separador_miles", length = 5)
    private String separatorMiles;

    @Size(max = 5)
    @Column(name = "separador_decimales", length = 5)
    private String separatorDecimals;

    @Size(max = 5)
    @Column(name = "\"precision\"", length = 5)
    private String precision;

    @Size(max = 5)
    @Column(name = "ICU", length = 5)
    private String icu;

    @Size(max = 50)
    @Column(name = "plural", length = 50)
    private String plural;

}