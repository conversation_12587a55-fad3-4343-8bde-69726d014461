package com.maersk.sd1.common.model;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.*;
import jakarta.persistence.*;
import org.hibernate.annotations.ColumnDefault;

import java.time.LocalDateTime;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@Table(name = "automatic_report", schema = "ges")
public class AutomaticReport {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "automatic_report_id", nullable = false)
    private Integer id;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "sub_business_unit_id", nullable = false)
    private BusinessUnit subBusinessUnit;

    @NotNull
    @Column(name = "report_start_date", nullable = false)
    private LocalDateTime reportStartDate;

    @Column(name = "report_end_date")
    private LocalDateTime reportEndDate;

    @NotNull
    @Column(name = "recurrency", nullable = false)
    private Integer recurrency;

    @NotNull
    @Lob
    @Column(name = "columns", nullable = false)
    private String columns;

    @Lob
    @Column(name = "filter_params")
    private String filterParams;

    @Size(max = 50)
    @Column(name = "alias", length = 50)
    private String alias;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "email_template_id", nullable = false)
    private EmailTemplate emailTemplate;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "report_id", nullable = false)
    private Report report;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "depot_id", nullable = false)
    private Depot depot;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "azure_storage_config_id", nullable = false)
    private AzureStorageConfig azureStorageConfig;

    @NotNull
    @ColumnDefault("1")
    @Column(name = "active", nullable = false)
    private Boolean active = false;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "user_registration_id", nullable = false)
    private User registrationUser;

    @NotNull
    @ColumnDefault("getdate()")
    @Column(name = "registration_date", nullable = false)
    private LocalDateTime registrationDate;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_modification_id")
    private User modificationUser;

    @Column(name = "modification_date")
    private LocalDateTime modificationDate;

    @Size(max = 200)
    @Column(name = "email_subject", length = 200)
    private String emailSubject;

}