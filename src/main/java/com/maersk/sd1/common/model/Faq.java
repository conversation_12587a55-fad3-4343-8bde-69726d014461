package com.maersk.sd1.common.model;

import com.maersk.sd1.common.model.BusinessUnit;
import com.maersk.sd1.common.model.Menu;
import com.maersk.sd1.common.model.User;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.*;
import jakarta.persistence.*;
import org.hibernate.annotations.ColumnDefault;

import java.time.LocalDateTime;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@Table(name = "faq", schema = "ges")
public class Faq {
    @Id
    @Column(name = "faq_id", nullable = false)
    private Integer id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "menu_id")
    private Menu menu;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "unidad_negocio_id")
    private BusinessUnit businessUnit;

    @Size(max = 100)
    @NotNull
    @Column(name = "version", nullable = false, length = 100)
    private String version;

    @NotNull
    @Lob
    @Column(name = "descripcion", nullable = false)
    private String description;

    @NotNull
    @ColumnDefault("'1'")
    @Column(name = "estado", nullable = false)
    private Character status;

    @NotNull
    @ColumnDefault("getdate()")
    @Column(name = "fecha_registro", nullable = false)
    private LocalDateTime registrationDate;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "usuario_registro_id", nullable = false)
    private User registrationUser;

    @Column(name = "fecha_modificacion")
    private LocalDateTime modificationDate;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "usuario_modificacion_id")
    private User modificationUser;

}