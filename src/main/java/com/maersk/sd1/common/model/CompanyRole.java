package com.maersk.sd1.common.model;

import jakarta.validation.constraints.NotNull;
import lombok.*;
import jakarta.persistence.*;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@Table(name = "empresa_rol", schema = "ges", uniqueConstraints = {
        @UniqueConstraint(name = "U_empresa_rol_empresa_id_tipo_rol_id", columnNames = {"empresa_id", "tipo_rol_id"})
})
public class CompanyRole {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "empresa_rol", nullable = false)
    private Integer id;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "empresa_id", nullable = false)
    private Company company;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "tipo_rol_id", nullable = false)
    private Catalog catRoleType;

}