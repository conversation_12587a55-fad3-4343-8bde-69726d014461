package com.maersk.sd1.common.model;

import com.maersk.sd1.common.model.NotificationTemplate;
import com.maersk.sd1.common.model.Role;
import jakarta.validation.constraints.NotNull;
import lombok.*;
import jakarta.persistence.*;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@Table(name = "notificacion_plantilla_rol", schema = "seg")
public class NotificationTemplateRole {
    @Id
    @Column(name = "notificacion_plantilla_rol_id", nullable = false)
    private Integer id;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "notificacion_plantilla_id", nullable = false)
    private NotificationTemplate notificationTemplate;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "rol_id", nullable = false)
    private Role role;

}