package com.maersk.sd1.common.model;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.*;
import jakarta.persistence.*;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@Table(name = "notificacion_plantilla_atributo", schema = "seg")
public class NotificationTemplateAttribute {
    @Id
    @Column(name = "notificacion_plantilla_atributo_id", nullable = false)
    private Integer id;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "notificacion_plantilla_id", nullable = false)
    private NotificationTemplate notificationTemplate;

    @Size(max = 100)
    @NotNull
    @Column(name = "campo", nullable = false, length = 100)
    private String field;

}