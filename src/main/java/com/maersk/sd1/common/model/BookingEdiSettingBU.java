package com.maersk.sd1.common.model;

import com.maersk.sd1.common.model.BusinessUnit;
import com.maersk.sd1.common.model.Catalog;
import com.maersk.sd1.common.model.User;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.*;
import jakarta.persistence.*;
import org.hibernate.annotations.ColumnDefault;

import java.time.LocalDateTime;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@Table(name = "seteo_edi_coparn_un", schema = "sds")
public class BookingEdiSettingBU {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "seteo_edi_coparn_un_id", nullable = false)
    private Integer id;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "seteo_edi_coparn_id", nullable = false)
    private BookingEdiSetting bookingEdiSetting;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "sub_unidad_negocio_id", nullable = false)
    private BusinessUnit subBusinessUnit;

    @Column(name = "aplica_envio_copia_coparn")
    private Boolean bkEdiApplyCopySend;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "cat_canal_reenvio_coparn_id")
    private Catalog catBkEdiForwardChannel;

    @Size(max = 100)
    @Column(name = "sftp_reenvio_coparn_id", length = 100)
    private String bkEdiForwardSftpId;

    @Size(max = 100)
    @Column(name = "ftp_reenvio_coparn_id", length = 100)
    private String bkEdiForwardFftpId;

    @Size(max = 200)
    @Column(name = "carpeta_reenvio_coparn_ruta", length = 200)
    private String bkEdiForwardFolderRoute;

    @NotNull
    @Column(name = "activo", nullable = false)
    private Boolean active = false;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "usuario_registro_id", nullable = false)
    private User registrationUser;

    @NotNull
    @ColumnDefault("getdate()")
    @Column(name = "fecha_registro", nullable = false)
    private LocalDateTime registrationDate;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "usuario_modificacion_id")
    private User modificationUser;

    @Column(name = "fecha_modificacion")
    private LocalDateTime modificationDate;

}