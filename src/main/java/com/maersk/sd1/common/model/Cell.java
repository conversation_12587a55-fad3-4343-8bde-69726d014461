package com.maersk.sd1.common.model;

import com.maersk.sd1.common.model.Block;
import com.maersk.sd1.common.model.User;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.*;
import jakarta.persistence.*;
import org.hibernate.annotations.ColumnDefault;

import java.time.LocalDateTime;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@Table(name = "celda", schema = "sdy")
public class Cell {
    @Id
    @Column(name = "celda_id", nullable = false)
    private Integer id;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "bloque_id", nullable = false)
    private Block block;

    @Size(max = 10)
    @NotNull
    @Column(name = "fila", nullable = false, length = 10)
    private String row;

    @Size(max = 10)
    @NotNull
    @Column(name = "columna", nullable = false, length = 10)
    private String column;

    @NotNull
    @Column(name = "indice_fila", nullable = false)
    private Integer rowIndex;

    @NotNull
    @Column(name = "indice_columna", nullable = false)
    private Integer columnIndex;

    @NotNull
    @Column(name = "cantidad_contenedores", nullable = false)
    private Integer containersQuantity;

    @NotNull
    @Column(name = "bloqueado", nullable = false)
    private Boolean blocked = false;

    @NotNull
    @ColumnDefault("1")
    @Column(name = "activo", nullable = false)
    private Boolean active = false;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "usuario_registro_id", nullable = false)
    private User registrationUser;

    @NotNull
    @ColumnDefault("getdate()")
    @Column(name = "fecha_registro", nullable = false)
    private LocalDateTime registrationDate;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "usuario_modificacion_id")
    private User modificationUser;

    @Column(name = "fecha_modificacion")
    private LocalDateTime modificationDate;

    public Cell(Integer cellId) {
        this.id = cellId;
    }
}