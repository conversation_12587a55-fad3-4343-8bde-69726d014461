package com.maersk.sd1.common.model;

import com.maersk.sd1.common.model.BusinessUnit;
import com.maersk.sd1.common.model.Menu;
import com.maersk.sd1.common.model.User;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.*;
import jakarta.persistence.*;
import org.hibernate.annotations.ColumnDefault;

import java.time.LocalDateTime;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@Table(name = "activity_log", schema = "sds")
public class ActivityLog {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "activity_log_id", nullable = false)
    private Integer id;

    @Size(max = 10)
    @NotNull
    @Column(name = "activity_alias", nullable = false, length = 10)
    private String activityAlias;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "sub_business_unit_id", nullable = false)
    private BusinessUnit subBusinessUnit;

    @Column(name = "eir_number")
    private Integer eirNumber;

    @Size(max = 50)
    @Column(name = "container_number", length = 50)
    private String containerNumber;

    @Size(max = 50)
    @Column(name = "chassis_number", length = 50)
    private String chassisNumber;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "cat_status_id", nullable = false)
    private Catalog catStatus;

    @Lob
    @Column(name = "data_input")
    private String inputData;

    @Lob
    @Column(name = "data_output")
    private String outputData;

    @NotNull
    @ColumnDefault("'1'")
    @Column(name = "active", nullable = false)
    private Character active;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "user_registration_id", nullable = false)
    private User registrationUser;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_modification_id")
    private User modificationUser;

    @Column(name = "modification_date")
    private LocalDateTime modificationDate;

    @ColumnDefault("getdate()")
    @Column(name = "registration_date")
    private LocalDateTime registrationDate;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "activity_log_reference_id")
    private ActivityLog activityLogReference;

    @Size(max = 50)
    @Column(name = "module_alias", length = 50)
    private String moduleAlias;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "module_menu_id")
    private Menu moduleMenu;

    @Column(name = "retryable")
    private Boolean retryable;

}