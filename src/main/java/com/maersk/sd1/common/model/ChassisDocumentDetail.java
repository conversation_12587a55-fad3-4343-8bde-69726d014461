package com.maersk.sd1.common.model;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.*;

import java.time.LocalDateTime;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@Table(name = "document_chassis_detail", schema = "sdh", indexes = {
        @Index(name = "chadocdetx_chadoc_id", columnList = "document_chassis_id"),
        @Index(name = "chadocdetx_cha_id", columnList = "chassis_id"),
        @Index(name = "chadocdetx_chabk_id", columnList = "document_chassis_booking_id")
})
public class ChassisDocumentDetail {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "document_chassis_detail_id", nullable = false)
    private Integer id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "document_chassis_id")
    private ChassisDocument chassisDocument;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "document_chassis_booking_id")
    private ChassisBookingDocument chassisBookingDocument;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "chassis_id")
    private Chassis chassis;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "transport_company_id")
    private Company transportCompany;

    @Column(name = "date_eta")
    private LocalDateTime dateEta;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "cat_source_creation_id", nullable = false)
    private Catalog catSourceCreation;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "cat_status_chassis_id", nullable = false)
    private Catalog catStatusChassis;

    @NotNull
    @Column(name = "active", nullable = false)
    private Boolean active = false;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "user_registration_id", nullable = false)
    private User registrationUser;

    @Column(name = "registration_date")
    private LocalDateTime registrationDate;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_modification_id")
    private User modificationUser;

    @Column(name = "modification_date")
    private LocalDateTime modificationDate;

    @Size(max = 20)
    @Column(name = "trace_chassis_doc_detail", length = 20)
    private String traceChassisDocDetail;

}