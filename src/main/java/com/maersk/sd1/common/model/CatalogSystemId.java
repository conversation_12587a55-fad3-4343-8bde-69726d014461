package com.maersk.sd1.common.model;

import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import jakarta.persistence.Entity;
import jakarta.validation.constraints.NotNull;
import lombok.*;
import jakarta.persistence.*;
import org.hibernate.Hibernate;

import java.io.Serializable;
import java.util.Objects;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Embeddable
public class CatalogSystemId implements Serializable {
    private static final long serialVersionUID = -1603511679289210184L;
    @NotNull
    @Column(name = "catalogo_id", nullable = false)
    private Integer catalogId;

    @NotNull
    @Column(name = "sistema_id", nullable = false)
    private Integer systemId;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || Hibernate.getClass(this) != Hibernate.getClass(o)) return false;
        CatalogSystemId entity = (CatalogSystemId) o;
        return Objects.equals(this.systemId, entity.systemId) &&
                Objects.equals(this.catalogId, entity.catalogId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(systemId, catalogId);
    }

}