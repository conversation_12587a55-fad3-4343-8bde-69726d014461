package com.maersk.sd1.common.repository;

import com.maersk.sd1.common.model.ContainerLocation;
import com.maersk.sd1.common.model.User;
import com.maersk.sd1.sdg.dto.LocationContainer;
import com.maersk.sd1.sdy.dto.*;
import jakarta.transaction.Transactional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;

public interface ContainerLocationRepository extends JpaRepository<ContainerLocation, Integer> {
    List<ContainerLocation> findByContainerId(Integer containerId);

    @Query("SELECT new com.maersk.sd1.sdg.dto.LocationContainer(cl.block, cl.cell, cl.level, cl.id) FROM ContainerLocation cl WHERE cl.container.id = :containerId")
    List<LocationContainer> findLocationContainerById(Integer containerId);

    @Modifying
    @Transactional
    @Query("update ContainerLocation set container = null where container.id = :containerId")
    void updateContainerIdNull(@Param("containerId") Integer containerId);

    @Query(value = "SELECT sdy.fn_get_container_location(:containerId)", nativeQuery = true)
    String getContainerLocation(@Param("containerId") Integer containerId);

    @Query("SELECT cl FROM ContainerLocation cl " +
            "WHERE cl.container.id = :containerId " +
            "ORDER BY cl.cell.id ASC")
    List<ContainerLocation> findByContainerIdOrderByCellAsc(@Param("containerId") Integer containerId);

    @Query("SELECT cl FROM ContainerLocation cl " +
            "JOIN cl.cell c " +
            "WHERE cl.block.id = :blockId " +
            "AND c.rowIndex BETWEEN :rowFrom AND :rowTo " +
            "AND c.columnIndex BETWEEN :colFrom AND :colTo " +
            "AND cl.active = true")
    List<ContainerLocation> findActiveWithBlockBetweenRowsCols(
            @Param("blockId") Integer blockId,
            @Param("rowFrom") Integer rowFrom,
            @Param("rowTo") Integer rowTo,
            @Param("colFrom") Integer colFrom,
            @Param("colTo") Integer colTo);

    @Modifying
    @Query("UPDATE ContainerLocation cl SET cl.active = false, cl.modificationUser = :user, cl.modificationDate = :date "
            + "WHERE cl.block.id = :blockId")
    void deactivateContainerLocationsByBlockId(@Param("blockId") Integer blockId,
                                               @Param("user") User user,
                                               @Param("date") LocalDateTime modificationDate);

    @Query("SELECT cl.container.containerNumber FROM ContainerLocation cl "
            + "JOIN cl.cell c "
            + "JOIN cl.block b "
            + "WHERE cl.container.id IS NOT NULL "
            + "AND (:catSizeId IS NULL OR cl.container.catSize.id <> :catSizeId) "
            + "AND b.id = :blockId "
            + "AND c.rowIndex BETWEEN :rowFrom AND :rowUntil "
            + "AND c.columnIndex BETWEEN :colFrom AND :colUntil ")
    List<String> findFirstDifferentSizeContainer(
            @Param("blockId") Integer blockId,
            @Param("catSizeId") Integer catSizeId,
            @Param("rowFrom") Integer rowFrom,
            @Param("rowUntil") Integer rowUntil,
            @Param("colFrom") Integer colFrom,
            @Param("colUntil") Integer colUntil,
            Pageable pageable
    );

    @Query("""
                SELECT con.containerNumber
                FROM ContainerLocation clc
                JOIN clc.block blk
                JOIN clc.cell cll
                JOIN clc.container con
                JOIN StockEmpty smty ON smty.container.id = con.id
                JOIN smty.gateInEir eir
                WHERE clc.container IS NOT NULL
                  AND smty.inStock = true
                  AND eir.catEmptyFull.id <> :catEmptyFullId
                  AND blk.id = :blockId
                  AND cll.rowIndex BETWEEN :rowIndexFrom AND :rowIndexTo
                  AND cll.columnIndex BETWEEN :columnIndexFrom AND :columnIndexTo
            """)
    List<String> findFirstByContainerIdAndInStockEmpty(
            @Param("catEmptyFullId") Integer catEmptyFullId,
            @Param("blockId") Integer blockId,
            @Param("rowIndexFrom") Integer rowIndexFrom,
            @Param("rowIndexTo") Integer rowIndexTo,
            @Param("columnIndexFrom") Integer columnIndexFrom,
            @Param("columnIndexTo") Integer columnIndexTo,
            Pageable pageable
    );

    @Query("""
                SELECT con.containerNumber
                FROM ContainerLocation clc
                JOIN clc.block blk
                JOIN clc.cell cll
                JOIN clc.container con
                JOIN StockFull smtf ON smtf.container.id = con.id
                JOIN smtf.gateInEir eir
                WHERE clc.container IS NOT NULL
                  AND smtf.inStock = true
                  AND eir.catEmptyFull.id <> :catEmptyFullId
                  AND blk.id = :blockId
                  AND cll.rowIndex BETWEEN :rowIndexFrom AND :rowIndexTo
                  AND cll.columnIndex BETWEEN :columnIndexFrom AND :columnIndexTo
            """)
    List<String> findFirstByContainerIdAndInStockFull(
            @Param("catEmptyFullId") Integer catEmptyFullId,
            @Param("blockId") Integer blockId,
            @Param("rowIndexFrom") Integer rowIndexFrom,
            @Param("rowIndexTo") Integer rowIndexTo,
            @Param("columnIndexFrom") Integer columnIndexFrom,
            @Param("columnIndexTo") Integer columnIndexTo,
            Pageable pageable
    );

    @Query("""
            SELECT con.containerNumber
            FROM ContainerLocation clc
            JOIN clc.block blk
            JOIN clc.cell cll
            JOIN clc.container con
            WHERE clc.container IS NOT NULL
              AND blk.id = :blockId
              AND cll.rowIndex BETWEEN :rowIndexFrom AND :rowIndexTo
              AND cll.columnIndex BETWEEN :columnIndexFrom AND :columnIndexTo
              AND (
                EXISTS (
                  SELECT se FROM StockEmpty se
                  WHERE se.container.id = con.id AND se.inStock = true
                )
                OR EXISTS (
                  SELECT sf FROM StockFull sf
                  WHERE sf.container.id = con.id AND sf.inStock = true
                )
              )
            """)
    List<String> findFirstContainerAnyStock(
            @Param("blockId") Integer blockId,
            @Param("rowIndexFrom") Integer rowIndexFrom,
            @Param("rowIndexTo") Integer rowIndexTo,
            @Param("columnIndexFrom") Integer columnIndexFrom,
            @Param("columnIndexTo") Integer columnIndexTo,
            Pageable pageable
    );

    ContainerLocation findTopByContainer_IdAndActiveOrderByCell_RowIndexAsc(Integer containerId, Boolean active);

    @Query("SELECT cl FROM ContainerLocation cl " +
            "WHERE cl.block.code = :bloqueCodigo " +
            "  AND cl.cell.row = :fila " +
            "  AND cl.cell.column = :columna " +
            "  AND cl.level.index = :nivel " +
            "  AND cl.active = true " +
            "  AND cl.block.active = true " +
            "  AND cl.cell.active = true " +
            "  AND cl.level.active = true")
    ContainerLocation findActiveLocation(@Param("bloqueCodigo") String bloqueCodigo,
                                         @Param("fila") String fila,
                                         @Param("columna") String columna,
                                         @Param("nivel") Integer nivel);

    @Query("SELECT new com.maersk.sd1.sdy.dto.ContainerLocationDetailsDTO(" +
            "b.id, c.id, l.id, c.rowIndex, c.columnIndex, " +
            "cl.id, cl.container.id, cl.container.containerNumber, null) " +
            "FROM Block b " +
            "JOIN Cell c ON c.block.id = b.id " +
            "JOIN Level l ON l.cell.id = c.id " +
            "LEFT JOIN ContainerLocation cl ON cl.block.id = b.id AND cl.cell.id = c.id AND cl.level.id = l.id " +
            "LEFT JOIN Container con ON con.id = cl.container.id " +
            "WHERE b.yard.id = :yardId " +
            "AND b.code = :blockCode " +
            "AND c.row = :rowCode " +
            "AND c.column = :columnCode " +
            "AND l.index = :levelIndex")
    ContainerLocationDetailsDTO findContainerLocationDetails(
            @Param("yardId") Integer yardId,
            @Param("blockCode") String blockCode,
            @Param("rowCode") String rowCode,
            @Param("columnCode") String columnCode,
            @Param("levelIndex") Integer levelIndex);

    @Query("SELECT new com.maersk.sd1.sdy.dto.ContainerLocationDetailsDTO(" +
            "b.id, c.id, l.id, c.rowIndex, c.columnIndex, " +
            "cl.id, con.id, con.containerNumber, CAST(cat.code AS integer)) " +
            "FROM Block b " +
            "JOIN Cell c ON c.block.id = b.id " +
            "JOIN Level l ON l.cell.id = c.id " +
            "LEFT JOIN ContainerLocation cl ON cl.block.id = b.id AND cl.cell.id = c.id AND cl.level.id = l.id " +
            "LEFT JOIN Container con ON con.id = cl.container.id " +
            "LEFT JOIN Catalog cat ON cat.id = con.catSize.id " +
            "WHERE b.yard.id = :yardId " +
            "AND b.code = :blockCode " +
            "AND c.row = :rowCode " +
            "AND c.column = :columnCode " +
            "AND l.index = :levelIndex - 1")
    ContainerLocationDetailsDTO findBelowContainerLocationDetails(
            @Param("yardId") Integer yardId,
            @Param("blockCode") String blockCode,
            @Param("rowCode") String rowCode,
            @Param("columnCode") String columnCode,
            @Param("levelIndex") Integer levelIndex);

    @Query("SELECT new com.maersk.sd1.sdy.dto.ContainerCurrentLocationDTO(" +
            "b.code, c.rowIndex, c.columnIndex, l.index) " +
            "FROM ContainerLocation cl " +
            "JOIN Container con ON con.id = cl.container.id " +
            "JOIN Block b ON b.id = cl.block.id " +
            "JOIN Cell c ON c.id = cl.cell.id " +
            "JOIN Level l ON l.id = cl.level.id " +
            "WHERE con.id = :containerId " +
            "ORDER BY c.rowIndex ASC")
    Page<ContainerCurrentLocationDTO> findContainerCurrentLocation(
            @Param("containerId") Integer containerId,
            Pageable pageable);

    @Query("""
            SELECT new com.maersk.sd1.sdy.dto.AboveContainerDTO(
                con.id, con.containerNumber)
            FROM Block b
            JOIN Cell c ON c.block.id = b.id
            JOIN Level l ON l.cell.id = c.id
            LEFT JOIN ContainerLocation cl ON cl.block.id = b.id AND cl.cell.id = c.id AND cl.level.id = l.id
            LEFT JOIN Container con ON con.id = cl.container.id
            WHERE b.code = :blockCode
            AND c.rowIndex = :rowIndex
            AND c.columnIndex = :columnIndex
            AND l.index = :levelIndex + 1
            AND b.yard.id = :yardId
            """)
    AboveContainerDTO findAboveContainer(
            @Param("yardId") Integer yardId,
            @Param("blockCode") String blockCode,
            @Param("rowIndex") Integer rowIndex,
            @Param("columnIndex") Integer columnIndex,
            @Param("levelIndex") Integer levelIndex);

    @Query(value = """
    SELECT
        CON.contenedor_id as containerId,
        EIR.cat_empty_full_id as catEmptyFullId
    FROM
        sdy.bloque BLK (NOLOCK)
        INNER JOIN sdy.celda CLL (NOLOCK) ON CLL.bloque_id = BLK.bloque_id
        INNER JOIN sdy.nivel LVL (NOLOCK) ON LVL.celda_id = CLL.celda_id
        LEFT OUTER JOIN sdy.ubicacion_contenedor UBC (NOLOCK) ON UBC.bloque_id = BLK.bloque_id AND UBC.celda_id = CLL.celda_id AND UBC.nivel_id = LVL.nivel_id
        LEFT OUTER JOIN sds.contenedor CON (NOLOCK) ON CON.contenedor_id = UBC.contenedor_id
        CROSS APPLY (
            SELECT TOP 1
                cat_empty_full_id
            FROM
                sde.eir EIR (NOLOCK)
            WHERE
                EIR.contenedor_id = CON.contenedor_id
            ORDER BY
                fecha_registro DESC
        ) AS EIR
    WHERE
        BLK.patio_id = :yardId AND
        BLK.codigo = :blockCode AND
        CLL.indice_fila = :rowIndex AND
        CLL.indice_columna = :columnIndex AND
        LVL.indice < :levelIndex
    """, nativeQuery = true)
    List<BelowContainerDTO> findBelowContainersNative(
            @Param("yardId") Integer yardId,
            @Param("blockCode") String blockCode,
            @Param("rowIndex") Integer rowIndex,
            @Param("columnIndex") Integer columnIndex,
            @Param("levelIndex") Integer levelIndex
    );

    @Query("""
    SELECT new com.maersk.sd1.sdy.dto.ContainerLocationDetailExtendedDTO(
        blk.id,
        cell.id,
        level.id,
        cell.rowIndex,
        cell.columnIndex,
        loc.id,
        loc.container.id,
        cont.containerNumber
    )
    FROM Block blk
    JOIN Cell cell ON cell.block.id = blk.id
    JOIN Level level ON level.cell.id = cell.id
    LEFT JOIN ContainerLocation loc ON loc.block.id = blk.id AND loc.cell.id = cell.id AND loc.level.id = level.id
    LEFT JOIN Container cont ON cont.id = loc.container.id
    WHERE blk.yard.id = :yardId
    AND blk.code = :blockCode
    AND cell.rowIndex = :rowIndex + 1
    AND cell.columnIndex = :columnIndex
    AND level.index = :levelIndex
    """)
    ContainerLocationDetailExtendedDTO findAdjacentContainerLocationDetails(
            @Param("yardId") Integer yardId,
            @Param("blockCode") String blockCode,
            @Param("rowIndex") Integer rowIndex,
            @Param("columnIndex") Integer columnIndex,
            @Param("levelIndex") Integer levelIndex
    );

    @Query("SELECT new com.maersk.sd1.sdy.dto.ContainerLocationDetailsDTO(" +
            "b.id, c.id, l.id, c.rowIndex, c.columnIndex, " +
            "cl.id, con.id, con.containerNumber, CAST(cat.code AS integer)) " +
            "FROM Block b " +
            "JOIN Cell c ON c.block.id = b.id " +
            "JOIN Level l ON l.cell.id = c.id " +
            "LEFT JOIN ContainerLocation cl ON cl.block.id = b.id AND cl.cell.id = c.id AND cl.level.id = l.id " +
            "LEFT JOIN Container con ON con.id = cl.container.id " +
            "LEFT JOIN Catalog cat ON cat.id = con.catSize.id " +
            "WHERE b.yard.id = :yardId " +
            "AND b.code = :blockCode " +
            "AND c.rowIndex = :rowCode " +
            "AND c.columnIndex = :columnCode " +
            "AND l.index = :levelIndex - 1")
    ContainerLocationDetailsDTO findBelow40ContainerLocationDetails(
            @Param("yardId") Integer yardId,
            @Param("blockCode") String blockCode,
            @Param("rowCode") Integer rowCode,
            @Param("columnCode") Integer columnCode,
            @Param("levelIndex") Integer levelIndex);

    @Query("""
    SELECT cont.containerNumber
    FROM ContainerLocation loc
    JOIN Block blk ON blk.id = loc.block.id
    JOIN Cell cell ON cell.id = loc.cell.id
    JOIN Level level ON level.id = loc.level.id
    LEFT JOIN Container cont ON cont.id = loc.container.id
    WHERE blk.yard.id = :yardId
    AND blk.code = :blockCode
    AND cell.row = :rowCode
    AND cell.column = :columnCode
    AND level.index = :levelIndex
    """)
    String findExistingContainerNumberAtHeapLocation(
            @Param("yardId") Integer yardId,
            @Param("blockCode") String blockCode,
            @Param("rowCode") String rowCode,
            @Param("columnCode") String columnCode,
            @Param("levelIndex") Integer levelIndex
    );

    @Modifying
    @Query("""
                            UPDATE ContainerLocation cl
                            SET cl.container = NULL,
                                cl.modificationUser.id = :userId,
                                cl.modificationDate = CURRENT_TIMESTAMP
                            WHERE (cl.block.id = :o20BlockId AND cl.cell.id = :o20CellId AND cl.level.id = :o20LevelId)
                               OR (cl.block.id = :o40BlockId AND cl.cell.id = :o40CellId AND cl.level.id = :o40LevelId)
""")
    int updateContainerLocation(
            @Param("userId") Integer userId,
            @Param("o20BlockId") Integer o20BlockId,
            @Param("o20CellId") Integer o20CellId,
            @Param("o20LevelId") Integer o20LevelId,
            @Param("o40BlockId") Integer o40BlockId,
            @Param("o40CellId") Integer o40CellId,
            @Param("o40LevelId") Integer o40LevelId
    );

    @Modifying
    @Query("""
                UPDATE ContainerLocation cl
                SET cl.container.id = :containerId,
                    cl.modificationUser.id = :userId,
                    cl.modificationDate = CURRENT_TIMESTAMP
                WHERE (cl.block.id = :d20BlockId AND cl.cell.id = :d20CellId AND cl.level.id = :d20LevelId)
                   OR (cl.block.id = :d40BlockId AND cl.cell.id = :d40CellId AND cl.level.id = :d40LevelId)
            """)
    int updateContainerDestLocation(
            @Param("containerId") Integer containerId,
            @Param("userId") Integer userId,
            @Param("d20BlockId") Integer d20BlockId,
            @Param("d20CellId") Integer d20CellId,
            @Param("d20LevelId") Integer d20LevelId,
            @Param("d40BlockId") Integer d40BlockId,
            @Param("d40CellId") Integer d40CellId,
            @Param("d40LevelId") Integer d40LevelId
    );

    List<ContainerLocation> findByBlock_IdAndCell_RowAndContainerIsNotNullOrderByBlock_IdAscCell_ColumnIndexAscLevel_IndexDesc(
            Integer blockId,
            String row
    );

    @Query("""
            SELECT new com.maersk.sd1.sdy.dto.PlanificacionUbicacionTraerOutput$PlanificacionUbicacionTraerRow(
                cl.id,
                bl.id,
                ce.id,
                lv.id,
                CASE WHEN cl.container IS NOT NULL THEN cl.container.id ELSE NULL END,
                cl.visitId,
                cl.active,
                bl.yard.id,
                bl.catBlockType.id,
                bl.code,
                bl.name,
                bl.rows,
                bl.columns,
                bl.levels,
                bl.row20Label,
                bl.row40label,
                bl.columnLabels,
                1,
                bl.configuration,
                bl.active,
                ce.row,
                ce.column,
                ce.rowIndex,
                ce.columnIndex,
                ce.containersQuantity,
                ce.blocked,
                ce.active,
                lv.index,
                lv.active,
                bl.catBlockType.code
            )
            FROM ContainerLocation cl
            JOIN cl.block bl
            JOIN cl.cell ce
            JOIN cl.level lv
            LEFT JOIN bl.catBlockType cbtp
            WHERE cl.active = TRUE
              AND bl.code = :bloqueCodigo
              AND ce.rowIndex = :indiceFila
              AND ce.columnIndex = :indiceColumna
              AND lv.index = :indiceNivel
              AND bl.yard.id = :patioId
            """)
    List<PlanificacionUbicacionTraerOutput.PlanificacionUbicacionTraerRow> findPlanificacionUbicacion(
            @Param("patioId") Integer patioId,
            @Param("bloqueCodigo") String bloqueCodigo,
            @Param("indiceFila") Integer indiceFila,
            @Param("indiceColumna") Integer indiceColumna,
            @Param("indiceNivel") Integer indiceNivel
    );

    @Query("SELECT cl.container.id FROM ContainerLocation cl " +
            "WHERE cl.active = TRUE AND cl.container.id IN :candidateContainers")
    Optional<List<Integer>> findContainersWithActiveLocations(@Param("candidateContainers") List<Integer> containerIds);

    @Query("""
            SELECT new com.maersk.sd1.sdy.dto.ContainerLocationDTO(
                cl.container.id, cl.id, cl.block.id, cl.cell.rowIndex, cl.cell.columnIndex, cl.level.index)
            FROM ContainerLocation cl
            JOIN cl.block b
            JOIN cl.cell c
            JOIN cl.level l
            JOIN b.yard y
            WHERE cl.container.id IN :containerIds
            AND y.businessUnit.id = :businessUnitId
            """)
    List<ContainerLocationDTO> findContainerLocationsForFull(
            @Param("containerIds") List<Integer> containerIds,
            @Param("businessUnitId") Integer businessUnitId);

    @Query("SELECT new com.maersk.sd1.sdy.dto.ContainerLocationDTO(" +
            "c.id, cl.id, b.id, cell.rowIndex, cell.columnIndex, l.index) " +
            "FROM ContainerLocation cl " +
            "JOIN cl.container c " +
            "JOIN cl.block b " +
            "JOIN cl.cell cell " +
            "JOIN cl.level l " +
            "JOIN b.yard y " +
            "WHERE c.id IN :containerIds AND y.businessUnit.id = :businessUnitId ")
    List<ContainerLocationDTO> findContainerLocations(
            @Param("containerIds") List<Integer> containerIds,
            @Param("businessUnitId") Integer businessUnitId);

    @Query("""
                SELECT new com.maersk.sd1.sdy.dto.ContainerLocationDTO(
                    cl.container.id, cl.id, cl.block.id, cl.cell.rowIndex, cl.cell.columnIndex, cl.level.index)
                FROM ContainerLocation cl
                WHERE cl.block.id = :blockId
                AND cl.cell.rowIndex IN :rowIndices
                AND cl.container IS NOT NULL
                AND cl.active = true
            """)
    List<ContainerLocationDTO> findBlockingContainersLocationsByBlockAndRows(
            @Param("blockId") Integer blockId,
            @Param("rowIndices") Set<Integer> rowIndices);

    @Query("SELECT new com.maersk.sd1.sdy.dto.HeapBlockCurrentContainer(" +
            "b.id, " +
            "c.id, " +
            "FUNCTION('CAST', cat.code, 'integer'), " +
            "b.catHeapLimitType.id) " +
            "FROM ContainerLocation cl " +
            "JOIN Block b ON b.id = cl.block.id " +
            "JOIN Container c ON c.id = cl.container.id " +
            "JOIN Catalog cat ON cat.id = c.catSize.id " +
            "WHERE b.id IN :blockIds")
    List<HeapBlockCurrentContainer> findContainersInBlocks(@Param("blockIds") List<Integer> blockIds);

    @Query("""
            SELECT new com.maersk.sd1.sdy.dto.ContainerStackHeapDTO(
                cl.id, cl.container.id, b.id, b.code,
                cell.id, cell.row, cell.rowIndex, cell.column, cell.columnIndex,
                level.id, level.index, mi.id)
            FROM ContainerLocation cl
            JOIN Block b ON b.id = cl.block.id
            JOIN Cell cell ON cell.id = cl.cell.id
            JOIN Level level ON level.id = cl.level.id
            LEFT JOIN MovementInstruction mi ON (mi.catStatus.id NOT IN :excludedStatuses AND (
                mi.destinationLevel.id = level.id OR mi.destination40Level.id = level.id
            ))
            WHERE b.yard.id = :yardId
            AND b.code = :blockCode
            AND (
                (:containerSize <= 20 AND cell.row = :rowLabel)
                OR (:containerSize > 20 AND (
                    cell.row = CAST(:rowIndexMinus1 AS string)
                    OR cell.row = CAST(:rowIndexPlus1 AS string)
                ))
            )
            AND cell.column = :columnLabel
            AND level.index = :levelIndex
            AND cl.active = true
            """)
    List<ContainerStackHeapDTO> findStackLocation(
            @Param("yardId") Integer yardId,
            @Param("blockCode") String blockCode,
            @Param("rowLabel") String rowLabel,
            @Param("rowIndexMinus1") Integer rowIndexMinus1,
            @Param("rowIndexPlus1") Integer rowIndexPlus1,
            @Param("columnLabel") String columnLabel,
            @Param("levelIndex") Integer levelIndex,
            @Param("containerSize") Integer containerSize,
            @Param("excludedStatuses") List<Integer> excludedStatuses
    );

    /**
     * Finds heap location with pagination
     */
    @Query("SELECT new com.maersk.sd1.sdy.dto.ContainerStackHeapDTO(" +
            "cl.id, b.id, b.code, " +
            "cell.id, cell.row, cell.rowIndex, cell.column, cell.columnIndex, " +
            "level.id, level.index) " +
            "FROM ContainerLocation cl " +
            "JOIN Block b ON b.id = cl.block.id " +
            "JOIN Cell cell ON cell.id = cl.cell.id " +
            "JOIN Level level ON level.id = cl.level.id " +
            "LEFT JOIN MovementInstruction mi ON (mi.catStatus.id NOT IN :excludedStatuses AND (" +
            "    mi.destinationLevel.id = level.id OR mi.destination40Level.id = level.id" +
            ")) " +
            "WHERE b.yard.id = :yardId " +
            "AND b.code = :blockCode " +
            "AND cl.container.id IS NULL " +
            "AND cl.active = true " +
            "ORDER BY cell.id ASC")
    Page<ContainerStackHeapDTO> findHeapLocation(
            @Param("yardId") Integer yardId,
            @Param("blockCode") String blockCode,
            @Param("excludedStatuses") List<Integer> excludedStatuses,
            Pageable pageable
    );

    @Query("""
                SELECT new com.maersk.sd1.sdy.dto.ContainerBelow(
                    c.id,
                    FUNCTION('CAST', COALESCE(catSize.code, '0'), 'integer'),
                    COALESCE(
                        (SELECT e.catEmptyFull.id FROM StockEmpty se JOIN Eir e ON e.id = se.gateInEir.id
                         WHERE se.container.id = c.id AND se.active = true),
                        (SELECT e.catEmptyFull.id FROM StockFull sf JOIN Eir e ON e.id = sf.gateInEir.id
                         WHERE sf.container.id = c.id AND sf.active = true)
                    )
                )
                FROM ContainerLocation cl
                JOIN cl.container c
                JOIN cl.level lvl
                LEFT JOIN c.catSize catSize
                WHERE cl.block.id = :blockId
                AND cl.cell.id = :cellId
                AND lvl.index = :levelIndex
                AND cl.container.id IS NOT NULL
            """)
    ContainerBelow findContainerBelow(
            @Param("blockId") Integer blockId,
            @Param("cellId") Integer cellId,
            @Param("levelIndex") Integer levelIndex
    );

    /**
     * Find container below at specific block, cell and level index for 40ft position
     */
    @Query("""
                SELECT new com.maersk.sd1.sdy.dto.ContainerBelow(
                    c.id,
                    FUNCTION('CAST', COALESCE(catSize.code, '0'), 'integer'),
                    COALESCE(
                        (SELECT e.catEmptyFull.id FROM StockEmpty se JOIN Eir e ON e.id = se.gateInEir.id
                         WHERE se.container.id = c.id AND se.active = true),
                        (SELECT e.catEmptyFull.id FROM StockFull sf JOIN Eir e ON e.id = sf.gateInEir.id
                         WHERE sf.container.id = c.id AND sf.active = true)
                    )
                )
                FROM ContainerLocation cl
                JOIN cl.container c
                JOIN cl.level lvl
                LEFT JOIN c.catSize catSize
                WHERE cl.block.id = :blockId
                AND cl.cell.id = :cellId
                AND lvl.index = :levelIndex
                AND cl.container.id IS NOT NULL
            """)
    ContainerBelow findContainerBelow40(
            @Param("blockId") Integer blockId,
            @Param("cellId") Integer cellId,
            @Param("levelIndex") Integer levelIndex
    );

    @Query(value = """
            SELECT cl.contenedor_id as container_id,
                   cl.ubicacion_contenedor_id as container_location_id,
                   cl.bloque_id as block_id,
                   cl.celda_id as cell_id,
                   cl.nivel_id as level_id,
                   b.codigo as block_code,
                   c.indice_fila as row_index,
                   c.indice_columna as column_index,
                   n.indice as level_index,
                   c.fila as row_label,
                   c.columna as column_label
            FROM sdy.ubicacion_contenedor cl
            JOIN sdy.bloque b ON cl.bloque_id = b.bloque_id
            JOIN sdy.celda c ON cl.celda_id = c.celda_id
            JOIN sdy.nivel n ON cl.nivel_id = n.nivel_id
            JOIN sdy.patio p ON b.patio_id = p.patio_id
            WHERE cl.contenedor_id IN :containerIds
            AND p.unidad_negocio_id = :unidadNegocioId
            """, nativeQuery = true)
    List<Map<String, Object>> findContainerLocationsNow(
            @Param("containerIds") List<Integer> containerIds,
            @Param("unidadNegocioId") Integer unidadNegocioId);

    @Query(value = """
            SELECT
                CLC.contenedor_id as container_id,
                CLC.ubicacion_contenedor_id as container_location_id,
                CLC.bloque_id as block_id,
                CLL.indice_fila as row_index,
                CLL.indice_columna as column_index,
                LVL.indice as level_index
            FROM
                sdy.ubicacion_contenedor CLC
                INNER JOIN sdy.bloque BLK ON BLK.bloque_id = CLC.bloque_id
                INNER JOIN sdy.celda CLL ON CLL.celda_id = CLC.celda_id
                INNER JOIN sdy.nivel LVL ON LVL.nivel_id = CLC.nivel_id
            WHERE
                CLC.bloque_id IN :blockIds
                AND CLL.indice_fila IN (SELECT value FROM STRING_SPLIT(:rowIndices, ','))
                AND CLC.contenedor_id IS NOT NULL
                AND CLC.activo = 1
            """, nativeQuery = true)
    List<Map<String, Object>> findBlockingContainerLocations(
            @Param("blockIds") List<Integer> blockIds,
            @Param("rowIndices") String rowIndices);

    @Modifying
    @Query("UPDATE ContainerLocation cl " +
           "SET cl.container = null, " +
           "cl.modificationUser.id = :userId, " +
           "cl.modificationDate = CURRENT_TIMESTAMP " +
           "WHERE (cl.block.id = :originBlockId AND cl.cell.id = :originCellId AND cl.level.id = :originLevelId) " +
           "OR (cl.block.id = :origin40BlockId AND cl.cell.id = :origin40CellId AND cl.level.id = :origin40LevelId) " +
           "AND cl.container IS NOT NULL")
    int removeContainersFromOriginLocations(
        @Param("originBlockId") Integer originBlockId,
        @Param("originCellId") Integer originCellId,
        @Param("originLevelId") Integer originLevelId,
        @Param("origin40BlockId") Integer origin40BlockId,
        @Param("origin40CellId") Integer origin40CellId,
        @Param("origin40LevelId") Integer origin40LevelId,
        @Param("userId") Integer userId);

    @Modifying
    @Query("UPDATE ContainerLocation cl " +
           "SET cl.container.id = :containerId, " +
           "cl.modificationUser.id = :userId, " +
           "cl.modificationDate = CURRENT_TIMESTAMP " +
           "WHERE (cl.block.id = :destinyBlockId AND cl.cell.id = :destinyCellId AND cl.level.id = :destinyLevelId) " +
           "OR (cl.block.id = :destiny40BlockId AND cl.cell.id = :destiny40CellId AND cl.level.id = :destiny40LevelId) " +
           "AND :destinyBlockId <> :outBlockId")
    int placeContainerOnDestinyLocation(
        @Param("containerId") Integer containerId,
        @Param("destinyBlockId") Integer destinyBlockId,
        @Param("destinyCellId") Integer destinyCellId,
        @Param("destinyLevelId") Integer destinyLevelId,
        @Param("destiny40BlockId") Integer destiny40BlockId,
        @Param("destiny40CellId") Integer destiny40CellId,
        @Param("destiny40LevelId") Integer destiny40LevelId,
        @Param("outBlockId") Integer outBlockId,
        @Param("userId") Integer userId);

    @Query("SELECT CASE WHEN COUNT(cl) > 0 THEN true ELSE false END " +
           "FROM ContainerLocation cl " +
           "WHERE cl.container.id = :containerId")
    boolean isContainerInStock(@Param("containerId") Integer containerId);

    @Query("SELECT CONCAT('{\"location_id\":', cl.id, " +
           "'\"block_id\":', cl.block.id, " +
           "'\"cell_id\":', cl.cell.id, " +
           "'\"level_id\":', cl.level.id, '}') " +
           "FROM ContainerLocation cl " +
           "WHERE cl.container.id = :containerId")
    String getContainerLocationJson(@Param("containerId") Integer containerId);

    @Query("SELECT c FROM ContainerLocation c " +
            "WHERE c.block.code = :blockCode " +
            "  AND c.cell.row = :cellRow " +
            "  AND c.cell.column = :cellColumn " +
            "  AND c.level.index = :levelIndex " +
            "  AND c.active = true")
    Optional<ContainerLocation> findByBlockCodeCellData(@Param("blockCode") String blockCode,
                                                        @Param("cellRow") String cellRow,
                                                        @Param("cellColumn") String cellColumn,
                                                        @Param("levelIndex") Integer levelIndex);

    @Query("SELECT COUNT(cl.id) FROM ContainerLocation cl WHERE cl.container.id = :containerId AND cl.active = true")
    long countActiveLocationsForContainer(@Param("containerId") Integer containerId);

    @Query("SELECT cl FROM ContainerLocation cl WHERE cl.container.id = :containerId AND cl.active = true")
    List<ContainerLocation> findByContainerIdAndActiveTrue(@Param("containerId") Integer containerId);

    @Query("""
        SELECT cl FROM ContainerLocation cl
        INNER JOIN cl.container con
        INNER JOIN cl.block blo
        INNER JOIN cl.cell cel
        INNER JOIN cl.level niv
        WHERE con.containerNumber = :containerNumber
        AND cel.row = (
            SELECT MIN(celT.row)
            FROM ContainerLocation clT
            INNER JOIN clT.cell celT
            WHERE clT.container.id = con.id
        )
        """)
    Optional<ContainerLocation> findReferenceContainerLocationByContainerNumber(@Param("containerNumber") String containerNumber);

}