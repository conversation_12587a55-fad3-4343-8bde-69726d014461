package com.maersk.sd1.common.repository;

import com.maersk.sd1.common.model.Yard;
import com.maersk.sd1.sdg.dto.TruckDepartureRegisterCreateim;
import com.maersk.sd1.sdy.dto.EirZoneActivityMaxDto;
import com.maersk.sd1.sdy.dto.RetrieveYardPlanningOutput;
import com.maersk.sd1.sdy.dto.TEirContainerDto;
import com.maersk.sd1.sdy.dto.YardObtainOutput;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.Optional;

public interface YardRepository extends JpaRepository<Yard, Integer>, JpaSpecificationExecutor<Yard> {

    @Query(value = """
    SELECT new com.maersk.sd1.sdg.dto.TruckDepartureRegisterCreateim(
        pat,
        blo,
        cel,
        niv
    )
    FROM
        Yard pat
        LEFT JOIN Block blo ON blo.yard = pat AND blo.active = true AND blo.code = 'Out'
        LEFT JOIN Cell cel ON cel.block = blo AND cel.active = true
        LEFT JOIN Level niv ON niv.cell = cel AND niv.active = true
    WHERE
        pat.active = true AND
        pat.businessUnit.id = :subBusinessUnitLocalId
    """)
    List<TruckDepartureRegisterCreateim> findTopBlockCellLevelByBusinessUnitId(@Param("subBusinessUnitLocalId") Integer subBusinessUnitLocalId);

    @Query(value = """
    SELECT pat
    FROM
        Yard pat
        LEFT JOIN Block blo ON blo.yard = pat AND blo.active = true AND blo.code = 'OnTruck'
        LEFT JOIN Cell cel ON cel.block = blo AND cel.active = true
        LEFT JOIN Level niv ON niv.cell = cel AND niv.active = true
    WHERE
        pat.active = true AND pat.businessUnit.id = :subBusinessUnitLocalId
    """)
    List<Yard> findTopOriginYardIdByBusinessUnitId(@Param("subBusinessUnitLocalId") Integer subBusinessUnitLocalId);

    Page<Yard> findAll(Specification<Yard> spec, Pageable pageable);

    @Query("SELECT new com.maersk.sd1.sdy.dto.YardObtainOutput(" +
            "y.id, " +
            "y.layout.id, " +
            "y.code, " +
            "y.name, " +
            "y.zoom, " +
            "y.latitude, " +
            "y.color, " +
            "y.longitude, " +
            "y.configuration, " +
            "y.active, " +
            "y.registrationDate, " +
            "y.modificationDate) " +
            "FROM Yard y " +
            "WHERE y.id = :yardId")
    Optional<YardObtainOutput> findYardObtainOutputById(@Param("yardId") Integer yardId);

    @Query("SELECT COUNT(y) FROM Yard y " +
            "WHERE y.businessUnit.id = :unidadNegocioId " +
            "AND y.active = true " +
            "AND y.id <> :excludedYardId")
    long countActiveYardByBusinessUnitAndDifferentYard(@Param("unidadNegocioId") Integer businessUnitId,
                                                       @Param("excludedYardId") Integer excludedYardId);

    List<Yard> findByBusinessUnit_IdAndActive(Integer businessUnitId, Boolean active);

    long countByActiveTrueAndBusinessUnit_Id(Integer businessUnitId);

    @Query(value = "SELECT plano_id FROM sdy.patio WHERE patio_id = :yardId", nativeQuery = true)
    Integer findLayoutIdByYardId(@Param("yardId") Integer yardId);

    // 1) Obtain the yard code
    @Query("SELECT y.code FROM Yard y WHERE y.businessUnit.id = :unidadNegocioId AND y.active = true")
    String findYardCodeByBusinessUnitId(@Param("unidadNegocioId") Integer unidadNegocioId);

    // 2) Gather EIR IDs and Container IDs from stock_vacio
    @Query("SELECT new com.maersk.sd1.sdy.dto.TEirContainerDto( COALESCE(s.gateOutEir.id, s.gateInEir.id), s.container.id ) "
            + "FROM StockEmpty s "
            + "WHERE s.inStock = true "
            + "  AND s.active = true "
            + "  AND s.gateInEir.localSubBusinessUnit.id = :unidadNegocioId "
            + "  AND s.container.id IN (SELECT cl.container.id FROM ContainerLocation cl WHERE cl.active = true AND cl.container.id = s.container.id) ")
    List<TEirContainerDto> findStockEmptyContainers(@Param("unidadNegocioId") Integer unidadNegocioId);

    // 3) Gather EIR IDs and Container IDs from stock_full
    @Query("SELECT new com.maersk.sd1.sdy.dto.TEirContainerDto( COALESCE(sf.gateOutEir.id, sf.gateInEir.id), sf.container.id ) "
            + "FROM StockFull sf "
            + "WHERE sf.inStock = true "
            + "  AND sf.active = true "
            + "  AND sf.gateInEir.localSubBusinessUnit.id = :unidadNegocioId "
            + "  AND sf.container.id IN (SELECT cl.container.id FROM ContainerLocation cl WHERE cl.active = true AND cl.container.id = sf.container.id) ")
    List<TEirContainerDto> findStockFullContainers(@Param("unidadNegocioId") Integer unidadNegocioId);

    @Query("SELECT y.id FROM Yard y WHERE y.businessUnit.id = :subBusinessUnitId AND y.active = true")
    Integer findCurrentYardIdBySubBusinessUnit(@Param("subBusinessUnitId") Integer subBusinessUnitId);

    // 4) For each eirId, find the max eirActivityZoneId
    @Query("SELECT eaz.eir.id AS eirId, MAX(eaz.id) AS eirZoneActivityId "
            + "FROM EirActivityZone eaz "
            + "WHERE eaz.eir.id IN :eirIds "
            + "GROUP BY eaz.eir.id")
    List<EirZoneActivityMaxDto> findMaxEirZoneActivityIds(@Param("eirIds") List<Integer> eirIds);

    @Query("SELECT y.id FROM Yard y WHERE y.businessUnit.id = :subBusinessUnitLocalId AND y.active = true")
    Optional<Integer> findActiveYardByBusinessUnit(@Param("subBusinessUnitLocalId") Integer subBusinessUnitLocalId);

    List<Yard> findByBusinessUnit_IdAndActiveTrue(Integer businessUnitId);

    @Query("select new com.maersk.sd1.sdy.dto.RetrieveYardPlanningOutput(" +
            "y.id, y.layout.id, y.code, y.name, y.zoom, y.latitude, y.color, y.longitude, " +
            "y.configuration, y.active, y.registrationUser.id, y.registrationDate, " +
            "y.modificationUser.id, y.modificationDate, y.businessUnit.id, bu.parentBusinessUnit.id) " +
            "from Yard y " +
            "left join y.businessUnit bu " +
            "where y.active = true and y.code = :yardCode")
    RetrieveYardPlanningOutput getYardDetails(@Param("yardCode") String yardCode);

    @Query("SELECT y.id FROM Yard y WHERE y.businessUnit.id = :unidadNegocioId AND y.active = true")
    Integer findYardIdByBusinessUnitId(@Param("unidadNegocioId") Integer unidadNegocioId);
}