package com.maersk.sd1.common.repository;

import com.maersk.sd1.common.model.Rule;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface RuleRepository extends JpaRepository<Rule, Integer> {

    @Modifying
    @Transactional
    @Query(value = "UPDATE seg.regla " +
            "SET estado = 0, " +
            "usuario_modificacion_id = :usuario_modificacion_id, " +
            "fecha_modificacion = CURRENT_TIMESTAMP " +
            "WHERE regla_id = :regla_id", nativeQuery = true)
    void executeReglaEliminar(@Param("regla_id") Integer reglaId,
                              @Param("usuario_modificacion_id") Long usuarioModificacionId);


    @Modifying
    @Query(value = "UPDATE Rule r SET r.system.id = :systemId, r.parameters = :parameters, r.status = :status, r.modificationUser.id = :modifiedByUserId, r.catRule.id = :categoryRuleId, r.modificationDate = CURRENT_TIMESTAMP WHERE r.id = :ruleId")
    int updateRule(@Param("ruleId") int ruleId,
                   @Param("systemId") double systemId,
                   @Param("parameters") String parameters,
                   @Param("status") char status,
                   @Param("modifiedByUserId") double modifiedByUserId,
                   @Param("categoryRuleId") double categoryRuleId);


    @Query("SELECT COUNT(r) FROM Rule r " +
            "WHERE (:reglaId IS NULL OR r.id = :reglaId) " +
            "AND (:sistemaId IS NULL OR r.system.id = :sistemaId) " +
            "AND (:parametros IS NULL OR r.parameters LIKE %:parametros%) " +
            "AND (:estado IS NULL OR r.status = :estado) " +
            "AND (:fechaRegistroMin IS NULL OR :fechaRegistroMax IS NULL OR r.registrationDate >= :fechaRegistroMin AND r.registrationDate < DATEADD(DAY, 1, :fechaRegistroMax)) " +
            "AND (:fechaModificacionMin IS NULL OR :fechaModificacionMax IS NULL OR r.modificationDate >= :fechaModificacionMin AND r.modificationDate < DATEADD(DAY, 1, :fechaModificacionMax)) " +
            "AND (:catReglaId IS NULL OR r.catRule.id = :catReglaId)")
    Integer countReglas(@Param("reglaId") Integer reglaId,
                        @Param("sistemaId") Long sistemaId,
                        @Param("parametros") String parametros,
                        @Param("estado") Character estado,
                        @Param("fechaRegistroMin") LocalDateTime fechaRegistroMin,
                        @Param("fechaRegistroMax") LocalDateTime fechaRegistroMax,
                        @Param("fechaModificacionMin") LocalDateTime fechaModificacionMin,
                        @Param("fechaModificacionMax") LocalDateTime fechaModificacionMax,
                        @Param("catReglaId") Long catReglaId);

    @Query(value = """
        SELECT
            REG.regla_id,
            REG.parametros,
            CAT.codigo AS id,
            REG.sistema_id,
            REG.estado,
            REG.fecha_registro,
            REG.fecha_modificacion,
            REG.cat_regla_id,
            REG.usuario_registro_id,
            REG.usuario_modificacion_id,
            USUA.nombres AS usuario_registro_nombres,
            USUA.apellido_paterno + ' ' + USUA.apellido_materno AS usuario_registro_apellidos,
            USUB.nombres AS usuario_modificacion_nombres,
            USUB.apellido_paterno + ' ' + USUB.apellido_materno AS usuario_modificacion_apellidos
        FROM seg.regla AS REG (NOLOCK)
        INNER JOIN seg.usuario AS USUA (NOLOCK) ON USUA.usuario_id = REG.usuario_registro_id
        LEFT JOIN seg.usuario AS USUB (NOLOCK) ON USUB.usuario_id = REG.usuario_modificacion_id
        LEFT JOIN ges.catalogo CAT (NOLOCK) ON CAT.catalogo_id = REG.cat_regla_id
        WHERE (:reglaId IS NULL OR REG.regla_id LIKE :reglaId)
            AND (:sistemaId IS NULL OR REG.sistema_id LIKE :sistemaId)
            AND (:parametros IS NULL OR REG.parametros LIKE '%' + :parametros + '%')
            AND (:estado IS NULL OR REG.estado LIKE '%' + :estado + '%')
            AND (:fechaRegistroMin IS NULL AND :fechaRegistroMax IS NULL OR REG.fecha_registro >= :fechaRegistroMin AND REG.fecha_registro < DATEADD(DAY, 1, :fechaRegistroMax))
            AND (:fechaModificacionMin IS NULL AND :fechaModificacionMax IS NULL OR REG.fecha_modificacion >= :fechaModificacionMin AND REG.fecha_modificacion < DATEADD(DAY, 1, :fechaModificacionMax))
            AND (:catReglaId IS NULL OR REG.cat_regla_id LIKE :catReglaId)
        ORDER BY 1 DESC
        """, nativeQuery = true)
    List<Object[]> findReglas1(@Param("reglaId") Integer reglaId,
                               @Param("sistemaId") Long sistemaId,
                               @Param("parametros") String parametros,
                               @Param("estado") Character estado,
                               @Param("fechaRegistroMin") LocalDateTime fechaRegistroMin,
                               @Param("fechaRegistroMax") LocalDateTime fechaRegistroMax,
                               @Param("fechaModificacionMin") LocalDateTime fechaModificacionMin,
                               @Param("fechaModificacionMax") LocalDateTime fechaModificacionMax,
                               @Param("catReglaId") Long catReglaId);


    @Query(value = "SELECT " +
            "REG.regla_id, " +
            "REG.parametros, " +
            "CAT.codigo AS id " +
            "FROM seg.regla REG (NOLOCK) " +
            "INNER JOIN ges.catalogo CAT (NOLOCK) " +
            "ON CAT.catalogo_id = REG.cat_regla_id " +
            "WHERE REG.sistema_id = :sistemaId " +
            "AND REG.estado = '1'", nativeQuery = true)
    List<Object[]> findReglas(@Param("sistemaId") Long sistemaId);
}
