package com.maersk.sd1.common.repository;

import com.maersk.sd1.common.model.LanguageSystemId;
import com.maersk.sd1.common.model.SystemLanguage;
import com.maersk.sd1.seg.dto.LanguageSystemListDTO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface SystemLanguageRepository extends JpaRepository<SystemLanguage, LanguageSystemId> {

    @Query("""
        SELECT COUNT(1)
        FROM SystemLanguage sl
        WHERE (:systemId IS NULL OR sl.system.id = :systemId)
          AND (:languageId IS NULL OR sl.language.id = :languageId)
    """)
    Integer countBySystemIdAndLanguageId(
            @Param("systemId") Integer systemId,
            @Param("languageId") Integer languageId
    );

    @Query("""
        SELECT new com.maersk.sd1.seg.dto.LanguageSystemListDTO(
            sl.system.id,
            sl.language.id
        )
        FROM SystemLanguage sl
        WHERE (:systemId IS NULL OR sl.system.id = :systemId)
          AND (:languageId IS NULL OR sl.language.id = :languageId)
        ORDER BY sl.system.id DESC
    """)
    List<LanguageSystemListDTO> findSystemAndLanguageIds(
            @Param("systemId") Integer systemId,
            @Param("languageId") Integer languageId
    );

    @Query("SELECT sl FROM SystemLanguage sl " +
            "JOIN Language l ON l.id = sl.id.languageId " +
            "WHERE sl.id.systemId = :systemId AND l.active = '1' " +
            "ORDER BY l.name ASC")
    List<SystemLanguage> findLanguagesForSystem(Long systemId);

}