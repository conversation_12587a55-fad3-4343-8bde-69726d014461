package com.maersk.sd1.common.repository;

import com.maersk.sd1.common.model.Cell;
import com.maersk.sd1.common.model.User;
import com.maersk.sd1.common.model.Level;
import com.maersk.sd1.sdy.dto.CellListOutput;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.time.LocalDate;
import java.time.LocalDateTime;

import java.util.List;
import java.util.Optional;

public interface CellRepository extends JpaRepository<Cell, Integer>, JpaSpecificationExecutor<Cell> {

    @Modifying
    @Query("UPDATE Cell c SET c.active = false, c.modificationUser = :user, c.modificationDate = :date WHERE c.block.id = :blockId")
    void deactivateCellsByBlockId(@Param("blockId") Integer blockId,
                                  @Param("user") User user,
                                  @Param("date") LocalDateTime modificationDate);

    @Query("SELECT lvl FROM Level lvl "
            + "JOIN lvl.cell c "
            + "JOIN c.block b "
            + "WHERE b.id = :blockId "
            + "AND b.active = true "
            + "AND c.active = true "
            + "AND lvl.active = true")
    List<Level> findAllActiveLevelsByBlock(@Param("blockId") Integer blockId);

    @Query(value = """
        SELECT COUNT(c)
        FROM Cell c
        WHERE (:celdaId IS NULL OR c.id = :celdaId)
          AND (:bloqueId IS NULL OR c.block.id = :bloqueId)
          AND (:fila IS NULL OR UPPER(c.row) LIKE CONCAT('%', UPPER(:fila), '%'))
          AND (:columna IS NULL OR UPPER(c.column) LIKE CONCAT('%', UPPER(:columna), '%'))
          AND (:indiceFila IS NULL OR c.rowIndex = :indiceFila)
          AND (:indiceColumna IS NULL OR c.columnIndex = :indiceColumna)
          AND (:cantidadContenedores IS NULL OR c.containersQuantity = :cantidadContenedores)
          AND (:bloqueado IS NULL OR c.blocked = :bloqueado)
          AND (:activo IS NULL OR c.active = :activo)
          AND ((:fechaRegistroMin IS NULL AND :fechaRegistroMax IS NULL)
               OR (c.registrationDate >= :fechaRegistroMin AND c.registrationDate < :fechaRegistroMaxPlusOne))
          AND ((:fechaModificacionMin IS NULL AND :fechaModificacionMax IS NULL)
               OR (c.modificationDate >= :fechaModificacionMin AND c.modificationDate < :fechaModificacionMaxPlusOne))
    """)
    long countByFilters(
            @Param("celdaId") Integer celdaId,
            @Param("bloqueId") Integer bloqueId,
            @Param("fila") String fila,
            @Param("columna") String columna,
            @Param("indiceFila") Integer indiceFila,
            @Param("indiceColumna") Integer indiceColumna,
            @Param("cantidadContenedores") Integer cantidadContenedores,
            @Param("bloqueado") Boolean bloqueado,
            @Param("activo") Boolean activo,
            @Param("fechaRegistroMin") LocalDate fechaRegistroMin,
            @Param("fechaRegistroMax") LocalDate fechaRegistroMax,
            @Param("fechaRegistroMaxPlusOne") LocalDate fechaRegistroMaxPlusOne,
            @Param("fechaModificacionMin") LocalDate fechaModificacionMin,
            @Param("fechaModificacionMax") LocalDate fechaModificacionMax,
            @Param("fechaModificacionMaxPlusOne") LocalDate fechaModificacionMaxPlusOne
    );

    @Query(value = """
        SELECT new com.maersk.sd1.sdy.dto.CellListOutput$CellDto(
            c.id,
            c.block.id,
            c.row,
            c.column,
            c.rowIndex,
            c.columnIndex,
            c.containersQuantity,
            c.blocked,
            c.active,
            c.registrationDate,
            c.modificationDate
        )
        FROM Cell c
        WHERE (:celdaId IS NULL OR c.id = :celdaId)
          AND (:bloqueId IS NULL OR c.block.id = :bloqueId)
          AND (:fila IS NULL OR UPPER(c.row) LIKE CONCAT('%', UPPER(:fila), '%'))
          AND (:columna IS NULL OR UPPER(c.column) LIKE CONCAT('%', UPPER(:columna), '%'))
          AND (:indiceFila IS NULL OR c.rowIndex = :indiceFila)
          AND (:indiceColumna IS NULL OR c.columnIndex = :indiceColumna)
          AND (:cantidadContenedores IS NULL OR c.containersQuantity = :cantidadContenedores)
          AND (:bloqueado IS NULL OR c.blocked = :bloqueado)
          AND (:activo IS NULL OR c.active = :activo)
          AND ((:fechaRegistroMin IS NULL AND :fechaRegistroMax IS NULL)
               OR (c.registrationDate >= :fechaRegistroMin AND c.registrationDate < :fechaRegistroMaxPlusOne))
          AND ((:fechaModificacionMin IS NULL AND :fechaModificacionMax IS NULL)
               OR (c.modificationDate >= :fechaModificacionMin AND c.modificationDate < :fechaModificacionMaxPlusOne))
        ORDER BY c.id DESC
    """)
    Page<CellListOutput.CellDto> findByFilters(
            @Param("celdaId") Integer celdaId,
            @Param("bloqueId") Integer bloqueId,
            @Param("fila") String fila,
            @Param("columna") String columna,
            @Param("indiceFila") Integer indiceFila,
            @Param("indiceColumna") Integer indiceColumna,
            @Param("cantidadContenedores") Integer cantidadContenedores,
            @Param("bloqueado") Boolean bloqueado,
            @Param("activo") Boolean activo,
            @Param("fechaRegistroMin") LocalDate fechaRegistroMin,
            @Param("fechaRegistroMax") LocalDate fechaRegistroMax,
            @Param("fechaRegistroMaxPlusOne") LocalDate fechaRegistroMaxPlusOne,
            @Param("fechaModificacionMin") LocalDate fechaModificacionMin,
            @Param("fechaModificacionMax") LocalDate fechaModificacionMax,
            @Param("fechaModificacionMaxPlusOne") LocalDate fechaModificacionMaxPlusOne,
            Pageable pageable
    );

    @Query("SELECT c FROM Cell c WHERE c.block.id = :blockId " +
            "AND c.row = :rowValue " +
            "AND c.column = :columnValue " +
            "AND c.active = true")
    Optional<Cell> findByBlockAndRowColumn(@Param("blockId") Integer blockId,
                                           @Param("rowValue") String rowValue,
                                           @Param("columnValue") String columnValue);
}