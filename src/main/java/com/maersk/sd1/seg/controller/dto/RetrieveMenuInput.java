package com.maersk.sd1.seg.controller.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

public class RetrieveMenuInput {
    @Data
    public static class Input {

        private Integer menu_id;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private RetrieveMenuInput.Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("SEG")
        private RetrieveMenuInput.Prefix seg;

        public RetrieveMenuInput.Input getInput() {
            return seg != null ? seg.getInput() : null;
        }
    }
}
