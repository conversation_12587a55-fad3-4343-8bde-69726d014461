package com.maersk.sd1.seg.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.seg.dto.UserDeleteInput;
import com.maersk.sd1.seg.dto.UserDeleteOutput;
import com.maersk.sd1.seg.service.UserDeleteService;
import jakarta.validation.Valid;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/ModuleADM/module/adm/ADMUsuarioServiceImp")
public class UserDeleteController {

    private static final Logger logger = LogManager.getLogger(UserDeleteController.class);

    private final UserDeleteService userDeleteService;

    @Autowired
    public UserDeleteController(UserDeleteService userDeleteService) {
        this.userDeleteService = userDeleteService;
    }

    @PostMapping("/segusuarioEliminar")
    public ResponseEntity<ResponseController<UserDeleteOutput>> deleteUser(@RequestBody @Valid UserDeleteInput.Root request) {
        try {
            Integer userId = request.getPrefix().getInput().getUserId();
            Integer userAdminId = request.getPrefix().getInput().getUserAdminId();

            UserDeleteOutput output = userDeleteService.deleteUser(userId, userAdminId);
            return ResponseEntity.ok(new ResponseController<>(output));
        } catch (Exception e) {
            logger.error("An error occurred while processing the request.", e);
            UserDeleteOutput output = new UserDeleteOutput();
            output.setRespEstado(0);
            output.setRespMensaje(e.getMessage());
            return ResponseEntity.status(500).body(new ResponseController<>(output));
        }
    }
}

