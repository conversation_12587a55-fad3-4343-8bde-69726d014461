package com.maersk.sd1.seg.controller.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.maersk.sd1.seg.dto.CatalogOptionDto;
import com.maersk.sd1.seg.dto.CurrencyOptionDto;
import lombok.Data;

import java.util.List;

@Data
public class BusinessUnitOptionOutput {

    @JsonProperty("resp_estado")
    private Integer respStatus;

    @JsonProperty("resp_mensaje")
    private String respMessage;

    @JsonProperty("catalog_options")
    private List<CatalogOptionDto> catalogOptions;

    @JsonProperty("currency_options")
    private List<CurrencyOptionDto> currencyOptions;
}