package com.maersk.sd1.seg.controller;

import com.maersk.sd1.seg.dto.ServiceListInput;
import com.maersk.sd1.seg.dto.ServiceListOutput;
import com.maersk.sd1.seg.service.ServiceListService;
import com.maersk.sd1.common.controller.dto.ResponseController;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RequiredArgsConstructor
@RestController
@RequestMapping("/ModuleSEG/module/seg/ADMServicioService")
public class ServiceListController {

    private static final Logger logger = LogManager.getLogger(ServiceListController.class);

    private final ServiceListService serviceListService;

    @PostMapping("/segservicioList")
    public ResponseEntity<ResponseController<ServiceListOutput>> getServices(@RequestBody @Valid ServiceListInput.Root request) {
        try {
            ServiceListInput.Input input = request.getPrefix().getInput();

            // Null field checks
            // pf_page and pf_size are already validated with NotNull in the input, but we can add extra checks if needed.

            ServiceListOutput output = serviceListService.getServicios(input);
            return ResponseEntity.ok(new ResponseController<>(output));
        } catch (Exception e) {
            logger.error("An error occurred while processing servicioList request.", e);
            ServiceListOutput errorOutput = new ServiceListOutput();
            errorOutput.setTotalRegistros(0L);
            errorOutput.setListaServicios(null);
            return ResponseEntity.internalServerError().body(new ResponseController<>(errorOutput));
        }
    }
}
