package com.maersk.sd1.seg.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.seg.dto.NotificationJobProcessRegisterInput;
import com.maersk.sd1.seg.dto.NotificationJobProcessRegisterOutput;
import com.maersk.sd1.seg.service.NotificationJobProcessRegisterService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RequiredArgsConstructor
@RestController
@RequestMapping("/ModuleSEG/module/seg")
public class NotificationJobProcessRegisterController {

    private static final Logger logger = LogManager.getLogger(NotificationJobProcessRegisterController.class);

    private final NotificationJobProcessRegisterService notificationJobProcessRegisterService;

    @PostMapping("/testNotificationJobProcessRegister")
    public ResponseEntity<ResponseController<NotificationJobProcessRegisterOutput>> notificationJobProcessRegister(
            @RequestBody @Valid NotificationJobProcessRegisterInput.Root request
    ) {
        try {

            if(request==null || request.getPrefix()==null || request.getPrefix().getInput()==null){
                logger.error("Request received for notificationJobProcessRegister is null or empty");
                NotificationJobProcessRegisterOutput output = new NotificationJobProcessRegisterOutput();
                output.setRespEstado(0);
                output.setRespMensaje("Request received for notificationJobProcessRegister is null or empty");
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(new ResponseController<>(output));
            }

            logger.info("Request received for notificationJobProcessRegister: {}", request);
            NotificationJobProcessRegisterInput.Input input = request.getPrefix().getInput();

            NotificationJobProcessRegisterOutput output = notificationJobProcessRegisterService.registerNotificationJobProcess(
                    input.getNotificacionJobId(),
                    input.getIndicadorEmailEjecutado(),
                    input.getIndicadorWebEjecutado(),
                    input.getIndicadorAppEjecutado(),
                    input.getUsuarioRegistroId()
            );

            return ResponseEntity.ok(new ResponseController<>(output));
        } catch (Exception e) {
            logger.error("An error occurred while processing the request.", e);
            NotificationJobProcessRegisterOutput output = new NotificationJobProcessRegisterOutput();
            output.setRespEstado(0);
            output.setRespMensaje(e.getMessage());
            output.setRespNewId(0);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(new ResponseController<>(output));
        }
    }
}
