package com.maersk.sd1.seg.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

public class LanguageRegisterInput {

    @Data
    public static class Input {

        @JsonProperty("code")
        @Size(max = 2)
        @NotNull
        private String code;

        @JsonProperty("name")
        @Size(max = 100)
        @NotNull
        private String name;

        @JsonProperty("active")
        @NotNull
        private Character isActive;

        @JsonProperty("registrationUserId")
        @NotNull
        private Integer registrationUserId;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private LanguageRegisterInput.Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("SEG")
        private LanguageRegisterInput.Prefix prefix;
    }
}
