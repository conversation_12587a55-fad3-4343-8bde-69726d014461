package com.maersk.sd1.seg.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

public class ProvidenceMonitorRegisterInput {
    @Data
    public static class Input {
        @JsonProperty("consulta_xml")
        private String consultXml;

        @JsonProperty("respuesta_xml")
        private String responseXml;

        @JsonProperty("usuario_registro_id")
        private Long registeredByUserId;
    }
    @Data
    public static class Prefix {
        @JsonProperty("F")
        private ProvidenceMonitorRegisterInput.Input input;
    }
    @Data
    public static class Root {
        @JsonProperty("SEG")
        private ProvidenceMonitorRegisterInput.Prefix prefix;
    }
}

