package com.maersk.sd1.seg.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

public class LoginCounterUpdateResponseDTO {

    @Data
    public static class Output {

        @JsonProperty("attempts")
        private Integer attempts;

        @JsonProperty("email")
        private String email;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private Output output;
    }

    @Data
    public static class Root {
        @JsonProperty("LoginCounterUpdate")
        private Prefix prefix;

        public Root() {
            this.prefix = new Prefix();
            this.prefix.setOutput(new Output());
        }
    }
}
