package com.maersk.sd1.seg.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

public class RuleDeleteInputDTO {

    @Data
    public static class Input {

        @JsonProperty("regla_id")
        private Integer reglaId;

        @JsonProperty("usuario_modificacion_id")
        private Long usuarioModificacionId;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("SEG")
        private Prefix prefix;

        public Root() {
            this.prefix = new Prefix();
            this.prefix.setInput(new Input());
        }
    }
}
