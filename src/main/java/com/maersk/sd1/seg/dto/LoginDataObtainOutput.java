package com.maersk.sd1.seg.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.maersk.sd1.seg.controller.dto.SystemValidationOutput;
import lombok.Data;
import java.util.List;

@Data
@JsonFormat(shape = JsonFormat.Shape.ARRAY)
public class LoginDataObtainOutput {

    @JsonProperty("business_units")
    private List<BusinessUnitItem> businessUnits;

    @JsonProperty("roles")
    private List<RoleItem> roles;

    @JsonProperty("projects")
    private List<MenuItem> projects;

    @JsonProperty("projects_by_bu")
    private List<BusinessUnitProjectItem> projectsByBusinessUnit;

    @JsonProperty("system_validation")
    private SystemValidationOutput systemValidation;

    @JsonProperty("rules")
    private ReglaListOutputDTO.Output rules;

    @JsonProperty("bus_units_terms")
    private List<Integer> businessUnitsForTerms;

    @JsonProperty("bus_units_faq")
    private List<Integer> businessUnitsForFaq;

    @JsonProperty("sub_units_function")
    private List<BusinessUnitSubFunctionItem> subUnitsFunction;

    @JsonProperty("languages")
    private List<LanguageItem> languages;

    @JsonProperty("system_bu_children")
    private List<BusinessUnitItem> systemBusinessUnitChildren;

    @JsonProperty("has_questions")
    private String hasQuestions;


    @Data
    public static class BusinessUnitItem {
        @JsonProperty("unidad_negocio_id")
        private Integer businessUnitId;

        @JsonProperty("unidad_negocio")
        private String businessUnitName;

        @JsonProperty("alias_unidad_negocio")
        private String businessUnitAlias;

        @JsonProperty("predefinido")
        private Integer predefined;

        @JsonProperty("unidad_negocio_hijo_id")
        private Integer childBusinessUnitId;

        @JsonProperty("sub_unidades")
        private List<SubUnitItem> subUnits;

        @JsonProperty("configuraciones")
        private List<ConfigurationItem> configurations;
    }

    @Data
    public static class SubUnitItem {
        @JsonProperty("unidad_negocio_id")
        private Integer buId;
        @JsonProperty("nombre")
        private String name;
        @JsonProperty("alias_unidad_negocio")
        private String alias;
    }

    @Data
    public static class ConfigurationItem {
        @JsonProperty("tipo_config_id")
        private String configType;

        @JsonProperty("tipo_config_valor")
        private String configValue;
    }

    @Data
    public static class RoleItem {
        @JsonProperty("rol_id")
        private Integer roleId;

        @JsonProperty("unidad_negocio_id")
        private Integer businessUnitId;

        @JsonProperty("rol_code")
        private String roleCode;
    }

    @Data
    public static class MenuItem {
        @JsonProperty("plantilla")
        private String template;
        @JsonProperty("titulo")
        private String title;
        @JsonProperty("icono")
        private String icon;
        @JsonProperty("menu_id")
        private Integer menuId;
        @JsonProperty("orden")
        private Integer order;
    }

    @Data
    public static class BusinessUnitProjectItem {
        @JsonProperty("plantilla")
        private String template;
        @JsonProperty("unidad_negocio_id")
        private Integer businessUnitId;
    }

    @Data
    public static class BusinessUnitSubFunctionItem {
        @JsonProperty("unidad_negocio_id")
        private Integer businessUnitId;
        @JsonProperty("nombre")
        private String name;
        @JsonProperty("unidad_negocio_padre_id")
        private Integer parentBusinessUnitId;
        @JsonProperty("alias_unidad_negocio")
        private String alias;
    }

    @Data
    public static class LanguageItem {
        @JsonProperty("idioma_id")
        private Integer languageId;
        @JsonProperty("codigo")
        private String code;
        @JsonProperty("nombre")
        private String name;
        @JsonProperty("defecto")
        private String defaultFlag;
    }
}
