package com.maersk.sd1.seg.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.maersk.sd1.common.serializer.ObjectArrayListSerializer;
import lombok.*;

import java.util.List;

public class RuleListFullOutput {

    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @Getter
    @Setter
    @ToString
    public static class ListResult {
        private Integer reglaId;
        private String parametros;
        private String id;
        private Long sistemaId;
        private String estado;
        private String fechaRegistro;
        private String fechaModificacion;
        private Long catReglaId;
        private Integer usuarioRegistroId;
        private Integer usuarioModificacionId;
        private String usuarioRegistroNombres;
        private String usuarioRegistroApellidos;
        private String usuarioModificacionNombres;
        private String usuarioModificacionApellidos;
    }

    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    public static class ListCount {
        private Integer total;

        public int getTotalCount() {
            return total;
        }
    }

    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @Data

    public static class Output {
        private List<ListResult> listResult;
        private ListCount listCount;
    }
}
