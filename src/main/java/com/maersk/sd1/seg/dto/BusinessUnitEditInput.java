package com.maersk.sd1.seg.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.experimental.UtilityClass;

import java.util.List;

@UtilityClass
public class BusinessUnitEditInput {

    @Data
    public static class ConfigInput {
        @JsonProperty("tipo_configuracion")
        @NotNull
        private Integer configType;

        @JsonProperty("valor")
        @NotNull
        @Size(max = 100)
        private String value;

        @JsonProperty("estado")
        private Boolean status;
    }

    @Data
    public static class MonedaInput {
        @JsonProperty("moneda_id")
        @NotNull
        private Integer monedaId;

        @JsonProperty("predefinido")
        private Character predefined;
    }

    @Data
    public static class Input {

        @JsonProperty("unidad_negocio_id")
        @NotNull
        private Integer businessUnitId;

        @JsonProperty("alias")
        @NotNull
        @Size(max = 50)
        private String alias;

        @JsonProperty("nombre")
        @NotNull
        @Size(max = 100)
        private String name;

        @JsonProperty("estadoUN")
        @NotNull
        private Boolean status;

        @JsonProperty("configuraciones")
        private List<ConfigInput> configuraciones;

        @JsonProperty("monedas")
        private List<MonedaInput> monedas;

        @JsonProperty("usuario_id")
        @NotNull
        private Integer userId;

        @JsonProperty("unidad_negocio_padre_id")
        private Integer parentBusinessUnitId;

        @JsonProperty("sistema_id")
        private Integer systemId;

        @JsonProperty("idioma_id")
        @NotNull
        private Integer languageId;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("SEG")
        private Prefix prefix;
    }
}
