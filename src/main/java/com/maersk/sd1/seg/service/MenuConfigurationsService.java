package com.maersk.sd1.seg.service;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.common.repository.CatalogRepository;
import com.maersk.sd1.seg.dto.MenuConfigurationsOutput;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

@Service
@RequiredArgsConstructor
public class MenuConfigurationsService {

    private final CatalogRepository catalogRepository;

    @Transactional
    public ResponseEntity<ResponseController<MenuConfigurationsOutput>> menuConfigurationsService() {

        List<MenuConfigurationsOutput.DataItems> menuConfigurationsList= catalogRepository.findMenuConfigurations();

        MenuConfigurationsOutput menuConfigurationsOutput = new MenuConfigurationsOutput();
        if(menuConfigurationsList != null && !menuConfigurationsList.isEmpty()){
            menuConfigurationsOutput.setData(menuConfigurationsList);
        }else{
            menuConfigurationsOutput.setData(Collections.emptyList());
        }

        return ResponseEntity.ok(new ResponseController<>(menuConfigurationsOutput));
    }
}
