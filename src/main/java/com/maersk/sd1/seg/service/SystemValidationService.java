package com.maersk.sd1.seg.service;

import com.maersk.sd1.common.model.*;
import com.maersk.sd1.common.repository.*;
import com.maersk.sd1.seg.controller.dto.SystemValidationInput;
import com.maersk.sd1.seg.controller.dto.SystemValidationOutput;
import com.maersk.sd1.seg.dto.SystemValidateActionDTO;
import com.maersk.sd1.seg.dto.SystemValidateProcessDTO;
import com.maersk.sd1.seg.dto.SystemValidateTemplateDTO;
import com.maersk.sd1.seg.dto.SystemValidateUserDTO;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.SortedMap;
import java.util.TreeMap;

//seg.sistema_validar
@RequiredArgsConstructor
@Service
public class SystemValidationService {

    private static final Logger logger = LogManager.getLogger(SystemValidationService.class.getName());

    private static final Integer CATALOG_STATUS_ID = 20253;

    private static final Character VERSION_STATUS = '1';
    private static final Boolean PRE_DEFINED = true;

    private static final Integer USER_TYPE = 2;
    private static final Integer JOB_TEMPLATE_TYPE = 3;
    private static final Integer ACTION_TYPE = 4;

    private final UserConfigBURepository userConfigBURepository;

    private final VersionRepository versionRepository;

    private final NotificationUserRepository notificationUserRepository;

    private final NotificationJobTemplateRepository notificationJobTemplateRepository;

    private final NotificationActionTemplateRepository notificationActionTemplateRepository;

    public SystemValidationOutput validateSystem(SystemValidationInput.Input input) {

        List<SystemValidateUserDTO> userDetails = null;
        TreeMap<LocalDateTime, SystemValidateProcessDTO> processMap = new TreeMap<>();
        SystemValidationOutput output = new SystemValidationOutput();

        UserConfigBU userConfig = userConfigBURepository.findByIdUserIdAndIdSystemIdAndPredefined(input.getUserId(), input.getSystemId(), PRE_DEFINED);

        Optional<Version> versionDetails = versionRepository.findTopBySystemIdAndStatusOrderByIdDesc(input.getSystemId(), VERSION_STATUS);

        if(input.getUserId()!=null){

            if (userConfig != null && userConfig.getBusinessUnit() != null) {
                userDetails = notificationUserRepository.getUserDetails(input.getUserId(), userConfig.getBusinessUnit().getId());
            } else {
                userDetails = notificationUserRepository.getUserDetails(input.getUserId(), null);
            }

            List<SystemValidateTemplateDTO> templateDetails = notificationJobTemplateRepository.getTemplateDetails(input.getUserId(), CATALOG_STATUS_ID);
            List<SystemValidateActionDTO> actionDetails = notificationActionTemplateRepository.getActionDetails(input.getUserId(), CATALOG_STATUS_ID);

            TreeMap<LocalDateTime, SystemValidateProcessDTO> userMappedDTO = (TreeMap<LocalDateTime, SystemValidateProcessDTO>) mapNotificationUserToDTO( userDetails,processMap, USER_TYPE);
            TreeMap<LocalDateTime, SystemValidateProcessDTO> jobTemplateMappedDTO = mapNotificationJobTemplateToDTO( templateDetails , userMappedDTO, JOB_TEMPLATE_TYPE);
            TreeMap<LocalDateTime, SystemValidateProcessDTO> finalMappedDTO = mapNotificationActionTemplateToDTO( actionDetails , jobTemplateMappedDTO, ACTION_TYPE);

            return mapFinalMapToOutput(finalMappedDTO, output);
        }
        else{
            logger.info("Returning null as the userId is null");
            output.setOutput(null);
        }

        return updateSystemDetailsInOutput(versionDetails, output);
    }

   public SortedMap<LocalDateTime, SystemValidateProcessDTO> mapNotificationUserToDTO(List<SystemValidateUserDTO> userDTOs, SortedMap<LocalDateTime, SystemValidateProcessDTO> resultMap, Integer type) {

        for (SystemValidateUserDTO userDTO : userDTOs) {
            NotificationUser notificationUser = userDTO.getNotificationClass();

            if (notificationUser != null) {
                SystemValidateProcessDTO processDTO = SystemValidateProcessDTO.builder()
                        .notificationId(notificationUser.getNotification().getId())
                        .type(type)
                        .icon(notificationUser.getNotification().getIcon())
                        .title(notificationUser.getNotification().getTitle())
                        .subtitle(notificationUser.getNotification().getSubtitle())
                        .description(notificationUser.getNotification().getDescription())
                        .level(notificationUser.getNotification().getLevel())
                        .onlyAlert(notificationUser.getNotification().getOnlyAlert())
                        .onlyTime(notificationUser.getNotification().getOnlyTime())
                        .expirationDate(notificationUser.getNotification().getExpirationDate())
                        .registrationDate(notificationUser.getNotification().getRegistrationDate())
                        .urlReference(null)
                        .template(null)
                        .read(userDTO.getRead())
                        .build();

                resultMap.put(processDTO.getRegistrationDate(), processDTO);
            }
        }
        return resultMap;
    }

    TreeMap<LocalDateTime, SystemValidateProcessDTO> mapNotificationJobTemplateToDTO(List<SystemValidateTemplateDTO> info2, TreeMap<LocalDateTime, SystemValidateProcessDTO> resultMap, Integer type) {

        for (SystemValidateTemplateDTO jobDTO : info2) {
            NotificationJobTemplate njp = jobDTO.getNotificationJobClass();
            NotificationTemplate npa = jobDTO.getNotificationTemplateClass();

            SystemValidateProcessDTO processDTO = SystemValidateProcessDTO.builder()
                    .notificationId(njp.getId())
                    .type(type)
                    .icon(njp.getIcon())
                    .title(njp.getTitle())
                    .subtitle(njp.getSubtitle())
                    .description(njp.getContent())
                    .level(npa.getLevel())
                    .onlyAlert(npa.getOnlyAlert())
                    .onlyTime(npa.getOnlyTime())
                    .expirationDate(null)
                    .registrationDate(njp.getRegistrationDate())
                    .urlReference(null)
                    .template(null)
                    .read(jobDTO.getRead())
                    .build();

            resultMap.put(njp.getRegistrationDate(), processDTO);
        }

        return resultMap;
    }

    TreeMap<LocalDateTime, SystemValidateProcessDTO> mapNotificationActionTemplateToDTO(List<SystemValidateActionDTO> info3, TreeMap<LocalDateTime, SystemValidateProcessDTO> resultMap, Integer type) {

        for (SystemValidateActionDTO actionDTO : info3) {
            NotificationActionTemplate nacp = actionDTO.getNotificationActionClass();
            NotificationTemplate npa = actionDTO.getNotificationTemplateClass();
            Menu menu = actionDTO.getMenuClass();

            SystemValidateProcessDTO processDTO = SystemValidateProcessDTO.builder()
                    .notificationId(nacp.getId())
                    .type(type)
                    .icon(nacp.getIcon())
                    .title(nacp.getTitle())
                    .subtitle(nacp.getSubtitle())
                    .description(nacp.getContent())
                    .level(npa.getLevel())
                    .onlyAlert(npa.getOnlyAlert())
                    .onlyTime(npa.getOnlyTime())
                    .expirationDate(null)
                    .registrationDate(nacp.getRegistrationDate())
                    .urlReference(nacp.getUrlReference())
                    .template(menu.getTemplate())
                    .read(actionDTO.getRead())
                    .build();


            resultMap.put(nacp.getRegistrationDate(), processDTO);
        }

        return resultMap;
    }

    private SystemValidationOutput mapFinalMapToOutput(TreeMap<LocalDateTime, SystemValidateProcessDTO> finalMap, SystemValidationOutput output) {
        List<SystemValidationOutput.Output> outputList = new ArrayList<>();

        for (SystemValidateProcessDTO processDTO : finalMap.values()) {
            SystemValidationOutput.Output outputEntry = new SystemValidationOutput.Output();
            outputEntry.setNotificationId(processDTO.getNotificationId());
            outputEntry.setType(processDTO.getType());
            outputEntry.setIcon(processDTO.getIcon());
            outputEntry.setTitle(processDTO.getTitle());
            outputEntry.setSubtitle(processDTO.getSubtitle());
            outputEntry.setDescription(processDTO.getDescription());
            outputEntry.setLevel(processDTO.getLevel());
            outputEntry.setOnlyAlert(processDTO.getOnlyAlert());
            outputEntry.setOnlyTime(processDTO.getOnlyTime());
            outputEntry.setExpirationDate(processDTO.getExpirationDate());
            outputEntry.setRegistrationDate(processDTO.getRegistrationDate());
            outputEntry.setUrlReference(processDTO.getUrlReference());
            outputEntry.setTemplate(processDTO.getTemplate());
            outputEntry.setRead(processDTO.getRead());

            outputList.add(outputEntry);
        }

        return output;
    }

    private SystemValidationOutput updateSystemDetailsInOutput(Optional<Version> versionDetails, SystemValidationOutput output) {
        versionDetails.ifPresent(version -> {
            SystemValidationOutput.VersionDetails versionData = new SystemValidationOutput.VersionDetails();
            versionData.setVersion(version.getVersion());
            versionData.setLevel(version.getLevel());
            versionData.setDeploymentDate(version.getDeploymentDate());
            output.setVersionDetails(versionData);
        });
        return output;
    }
}