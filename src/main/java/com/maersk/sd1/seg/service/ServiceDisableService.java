package com.maersk.sd1.seg.service;

import com.maersk.sd1.common.repository.ServiceRepository;
import com.maersk.sd1.seg.dto.ServiceDisableOutputDto;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

@Slf4j
@RequiredArgsConstructor
@Service
public class ServiceDisableService {

    private final ServiceRepository serviceRepository;

    @Transactional
    public ServiceDisableOutputDto disableService(Integer serviceId, Integer userModificationId) {
        ServiceDisableOutputDto output = new ServiceDisableOutputDto();
        try {
            int updatedRows = serviceRepository.disableService(serviceId, userModificationId, LocalDateTime.now());

            if (updatedRows > 0) {
                output.setRespResult(1);
                output.setRespMessage("Record successfully disabled");
            } else {
                output.setRespResult(0);
                output.setRespMessage("Record not found with the given ID");
            }
        } catch (Exception e) {
            log.error("Error disabling service with ID {}", serviceId, e);
            output.setRespResult(0);
            output.setRespMessage(e.getMessage());
        }
        return output;
    }
}
