package com.maersk.sd1.seg.service;

import com.maersk.sd1.common.model.Company;
import com.maersk.sd1.common.model.CompanyUser;
import com.maersk.sd1.common.model.CompanyUserId;
import com.maersk.sd1.common.model.NotificationTemplate;
import com.maersk.sd1.common.model.NotificationTemplateRole;
import com.maersk.sd1.common.model.NotificationTemplateUser;
import com.maersk.sd1.common.model.Person;
import com.maersk.sd1.common.model.User;
import com.maersk.sd1.seg.controller.dto.UserRegisterInput;
import com.maersk.sd1.seg.controller.dto.UserRegisterOutput;
import com.maersk.sd1.common.repository.UserRepository;
import com.maersk.sd1.common.repository.CompanyUserRepository;
import com.maersk.sd1.common.repository.NotificationTemplateRoleRepository;
import com.maersk.sd1.common.repository.NotificationTemplateUserRepository;
import com.maersk.sd1.seg.dto.UserRoleRegisterInput;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@RequiredArgsConstructor
@Service
public class UserRegisterService {

    private static final Logger logger = LogManager.getLogger(UserRegisterService.class);

    private final UserRepository userRepository;

    private final CompanyUserRepository companyUserRepository;

    private final NotificationTemplateRoleRepository notificationTemplateRoleRepository;

    private final NotificationTemplateUserRepository notificationTemplateUserRepository;

    private final UserRoleRegisterService userRoleRegisterService;

    @Transactional
    public UserRegisterOutput registerNewUser(UserRegisterInput.Input input) {
        UserRegisterOutput output = new UserRegisterOutput();
        try {
            String aliasUpper = (input.getAlias() != null) ? input.getAlias().toUpperCase() : null;
            String mailUpper = (input.getMail() != null) ? input.getMail().toUpperCase() : null;

            if (aliasUpper != null && userRepository.existsByAliasIgnoreCase(aliasUpper)) {
                output.setRespStatus(2);
                output.setRespMessage("El Id existe, ingrese otro.");
                return output;
            }

            User savedUser = registerNewUser(input, aliasUpper, mailUpper);

            if (input.getEmpresas() != null && !input.getEmpresas().isEmpty()) {
                registerCompanyUser(input, savedUser);
            }

            UserRoleRegisterInput.Input roleInput = new UserRoleRegisterInput.Input();

            roleInput.setUsuarioId(savedUser.getId());
            roleInput.setRolesXml(input.getRolesXml());
            roleInput.setRolesRunXml(input.getRolesXml());
            roleInput.setUsuariosUn(input.getUsuariosUn());
            roleInput.setUsuariosUnDefecto(input.getUsuariosUnDefecto());

            List<Integer> roleIds = userRoleRegisterService.parseRolesFromXml(input.getRolesXml());

            userRoleRegisterService.registerUserRoles(roleInput);

            if (roleIds != null && !roleIds.isEmpty()) {
                registerNotificationTemplates(savedUser, roleIds);
            }

            output.setRespStatus(1);
            output.setRespMessage("Usuario registrado correctamente");
            return output;
        } catch (Exception ex) {
            logger.error("Error registering user", ex);
            output.setRespStatus(0);
            output.setRespMessage(ex.getMessage());
            return output;
        }
    }

    private User registerNewUser(UserRegisterInput.Input input, String aliasUpper, String mailUpper) {

        User newUser = new User();
        newUser.setAlias(aliasUpper);
        newUser.setMail(mailUpper);
        newUser.setKey(input.getKey());
        newUser.setDateChangeKey(LocalDateTime.now().plusDays(60));
        newUser.setNames(input.getNames());
        newUser.setFirstLastName(input.getFirstLastName());
        newUser.setSecondLastName(input.getSecondLastName());

        if (input.getCompanyId() != null) {
            Company company = new Company();
            company.setId(input.getCompanyId());
            newUser.setCompany(company);
        }

        newUser.setStatus(input.getStatus());

        if (input.getUserRegistrationId() != null) {
            User regUser = new User(input.getUserRegistrationId());
            newUser.setRegistrationUser(regUser);
        }

        if (input.getPersonaId() != null) {
            Person person = new Person();
            person.setId(input.getPersonaId());
            newUser.setPerson(person);
        }

        newUser.setRegistrationDate(LocalDateTime.now());

        User savedUser =  userRepository.save(newUser);
        logger.info("User registered {}", newUser.getId());
        return savedUser;
    }

    private void registerCompanyUser(UserRegisterInput.Input input, User savedUser) {
        for (Integer compId : input.getEmpresas()) {
            CompanyUser companyUser = new CompanyUser();
            CompanyUserId companyUserId = new CompanyUserId();
            companyUserId.setUserId(savedUser.getId());
            companyUserId.setCompanyId(compId);
            companyUser.setId(companyUserId);

            companyUser.setUser(savedUser);
            Company c = new Company();
            c.setId(compId);
            companyUser.setCompany(c);

            companyUserRepository.save(companyUser);
            logger.info("Company user registered for user {}", savedUser.getId());
        }
    }

    private void registerNotificationTemplates(User savedUser, List<Integer> roleIds) {
        List<NotificationTemplateRole> templateRoles = notificationTemplateRoleRepository.findByRoleIdIn(roleIds);
        if (templateRoles != null && !templateRoles.isEmpty()) {
            List<NotificationTemplateUser> ntuToSave = new ArrayList<>();
            for (NotificationTemplateRole ntr : templateRoles) {
                NotificationTemplateUser ntu = new NotificationTemplateUser();
                ntu.setUser(savedUser);
                NotificationTemplate nt = ntr.getNotificationTemplate();
                ntu.setNotificationTemplate(nt);
                ntuToSave.add(ntu);
            }
            notificationTemplateUserRepository.saveAll(ntuToSave);
            logger.info("Notification templates registered for user {}", savedUser.getId());
        }
    }
}