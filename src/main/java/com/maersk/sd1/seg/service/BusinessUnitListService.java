package com.maersk.sd1.seg.service;

import com.maersk.sd1.seg.dto.BusinessUnitListInput;
import com.maersk.sd1.seg.dto.BusinessUnitListOutput;
import com.maersk.sd1.common.repository.BusinessUnitListRepository;
import com.maersk.sd1.seg.controller.dto.BusinessUnitListTempDTO;
import com.maersk.sd1.seg.dto.BusinessUnitListProjection;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

@Service
public class BusinessUnitListService {

    private static final Logger logger = LogManager.getLogger(BusinessUnitListService.class);

    private final BusinessUnitListRepository businessUnitListRepository;

    public BusinessUnitListService(BusinessUnitListRepository businessUnitListRepository) {
        this.businessUnitListRepository = businessUnitListRepository;
    }

    @Transactional(readOnly = true)
    public BusinessUnitListOutput listBusinessUnits(BusinessUnitListInput.Input inputParams) {
        BusinessUnitListOutput output = new BusinessUnitListOutput();
        try {
            Integer parentBusinessUnitId = inputParams.getParentUnit();
            Integer unitType = inputParams.getUnitType();
            String parentIndicator = inputParams.getParentIndicator();


            List<BusinessUnitListProjection> businessUnits = businessUnitListRepository.findBusinessList(parentBusinessUnitId, unitType);

            Long totalRecords = (long) businessUnits.size();

            List<BusinessUnitListTempDTO> businessUnitListTempDTOs = new ArrayList<>();

            for(BusinessUnitListProjection tempDTO : businessUnits) {
                businessUnitListTempDTOs.add(mapToOutputDetail(tempDTO));
            }


            if(parentIndicator != null && parentIndicator.equals("1")) {
                businessUnits.removeIf(businessUnit -> businessUnit.getParentBusinessUnitId() == null);
            }

            output.setResponseStatus(1);
            output.setResponseMessage("Success");
            output.setTotalRecords(totalRecords);
            output.setBusinessUnits(businessUnitListTempDTOs);
            return output;
        } catch (Exception e) {
            logger.error("Error fetching Business Units.", e);
            output.setResponseStatus(0);
            output.setResponseMessage("Error: " + e.getMessage());
            output.setTotalRecords(0L);
            output.setBusinessUnits(new ArrayList<>());
            return output;
        }
    }

    private BusinessUnitListTempDTO mapToOutputDetail(BusinessUnitListProjection tempDTO) {
        return new BusinessUnitListTempDTO(
                tempDTO.getBusinessUnitId(),
                tempDTO.getName(),
                tempDTO.getStatus(),
                tempDTO.getParentBusinessUnitId(),
                tempDTO.getParentBusinessUnitName(),
                tempDTO.getSystemId(),
                tempDTO.getSystemName(),
                tempDTO.getSystemIcon(),
                tempDTO.getConfiguration(),
                tempDTO.getCurrency(),
                tempDTO.getBusinessUnitAlias()
        );
    }

}
