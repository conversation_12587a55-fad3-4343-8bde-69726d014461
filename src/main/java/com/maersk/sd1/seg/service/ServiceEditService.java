package com.maersk.sd1.seg.service;

import com.maersk.sd1.common.model.User;
import com.maersk.sd1.common.repository.ServiceRepository;
import com.maersk.sd1.common.repository.UserRepository;
import com.maersk.sd1.seg.dto.ServiceEditOutput;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

@RequiredArgsConstructor
@Service
public class ServiceEditService {

    private static final Logger logger = LogManager.getLogger(ServiceEditService.class);

    private final ServiceRepository serviceRepository;
    private final UserRepository userRepository;

    @Transactional
    public ServiceEditOutput serviceEdit(Integer serviceId,
                                         String serviceName,
                                         Character serviceProtected,
                                         Character serviceActive,
                                         Integer userModificationId) {

        ServiceEditOutput output = new ServiceEditOutput();
        try {
            logger.info("Starting service edit for serviceId: {}", serviceId);
            com.maersk.sd1.common.model.Service existingService = serviceRepository.findById(serviceId)
                    .orElseThrow(() -> new IllegalArgumentException("Service not found: " + serviceId));

            existingService.setName(serviceName);
            existingService.setProtectedIndicator(serviceProtected);
            existingService.setStatus(serviceActive != null && (serviceActive == '1' || serviceActive == 'Y'));

            User modificationUser = userRepository.findById(userModificationId)
                    .orElseThrow(() -> new IllegalArgumentException("User not found: " + userModificationId));

            existingService.setModificationUser(modificationUser);
            existingService.setModificationDate(LocalDateTime.now());

            com.maersk.sd1.common.model.Service savedService = serviceRepository.save(existingService);

            output.setRespEstado(1);
            output.setRespMensaje("Record successfully updated");
            output.setRespNewId(savedService.getId());

            logger.info("Service edit completed successfully for serviceId: {}", serviceId);

        } catch (Exception e) {
            logger.error("Error during service edit for serviceId: {}", serviceId, e);
            output.setRespEstado(0);
            output.setRespNewId(0);
            output.setRespMensaje(e.getMessage());
        }
        return output;
    }
}
