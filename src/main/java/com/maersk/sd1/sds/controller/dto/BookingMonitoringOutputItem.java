package com.maersk.sd1.sds.controller.dto;

public interface BookingMonitoringOutputItem {

    Integer getEdiId();
    String getBookingNumber();
    String getMessageType();
    String getMessageTypeId();
    Integer getCoparnStatusId();
    String getStatus();
    String getInternalEdiDate();
    String getEdiVessel();
    String getEdiVoyage();
    String getVesselVoyage();
    String getEdiLoadingPort();
    String getLoadingPort();
    String getEdiDischargePort();
    String getDischargePort();
    String getEdiDestinationPort();
    String getDestinationPort();
    String getEdiCustomerAlias();
    String getEdiCustomerName();
    String getCustomerCode();
    String getCustomerName();
    Integer getEdiContainerQuantity();
    String getEdiIsoCode();
    String getContainerType();
    Integer getEdiContainerQuantity2();
    String getEdiIsoCode2();
    String getContainerType2();
    String getEdiCommodity();
    String getCargoType();
    String getEdiImoNumber();
    String getImoNumber();
    Integer getEdiGrossWeight();
    String getEdiTemperature();
    String getColdTreatment();
    String getControlledAtmosphere();
    String getEdiLine();
    String getLine();
    String getLineConfig();
    String getReceptionDate();
    String getProcessedDate();
    String getProcessedComment();
    String getOriginalFileName();
    String getBusinessUnit();
    String getDepot();
    String getFileForwarded();
    String getForwardingDate();
    String getCreateVesselProg();
    String getCreateCustomer();
    Integer getIdVesselProgDet();
    Integer getIdCoparnConfig();
    String getProcessedUser();
}
