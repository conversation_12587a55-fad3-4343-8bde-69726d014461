package com.maersk.sd1.sds.controller.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class DepotObtainOutput {

    @JsonProperty("deposito_id")
    private Integer depotId;

    @JsonProperty("unidad_negocio_id")
    private Integer businessUnitId;

    @JsonProperty("codigo_deposito")
    private String depotCode;

    @JsonProperty("nombre_deposito")
    private String depotName;

    @JsonProperty("direccion_deposito")
    private String depotAddress;

    @JsonProperty("activo")
    private Boolean active;

    @JsonProperty("fecha_registro")
    private LocalDateTime registrationDate;

    @JsonProperty("fecha_modificacion")
    private LocalDateTime modificationDate;

    @JsonProperty("sub_unidad_negocio_id")
    private Integer subBusinessUnitId;

    @JsonProperty("deposito_default")
    private Boolean defaultDepot;

    @JsonProperty("cat_codigo_aduana_id")
    private Integer customsCodeId;

    @JsonProperty("cat_clase_operador_aduana_id")
    private Integer customsOperatorClassId;

    public DepotObtainOutput(
            Integer depotId,
            Integer businessUnitId,
            String depotCode,
            String depotName,
            String depotAddress,
            Boolean active,
            LocalDateTime registrationDate,
            LocalDateTime modificationDate,
            Integer subBusinessUnitId,
            Boolean defaultDepot,
            Integer customsCodeId,
            Integer customsOperatorClassId
    ) {
        this.depotId = depotId;
        this.businessUnitId = businessUnitId;
        this.depotCode = depotCode;
        this.depotName = depotName;
        this.depotAddress = depotAddress;
        this.active = active;
        this.registrationDate = registrationDate;
        this.modificationDate = modificationDate;
        this.subBusinessUnitId = subBusinessUnitId;
        this.defaultDepot = defaultDepot;
        this.customsCodeId = customsCodeId;
        this.customsOperatorClassId = customsOperatorClassId;
    }
}