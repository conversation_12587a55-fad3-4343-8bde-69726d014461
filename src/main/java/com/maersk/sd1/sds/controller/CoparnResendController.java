package com.maersk.sd1.sds.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sds.dto.CoparnResendInput;
import com.maersk.sd1.sds.dto.CoparnResendOutput;
import com.maersk.sd1.sds.service.CoparnResendService;
import jakarta.validation.Valid;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/ModuleSDS/module/sds/SDSCoparnResendServiceImp")
public class CoparnResendController {

    private static final Logger logger = LogManager.getLogger(CoparnResendController.class);

    private final CoparnResendService coparnResendService;
    
    @Autowired
    public CoparnResendController(CoparnResendService coparnResendService) {
        this.coparnResendService = coparnResendService;
    }

    @PostMapping("/servicioCoparnActualizarReenvio")
    public ResponseEntity<ResponseController<com.maersk.sd1.sds.dto.CoparnResendOutput>> servicioCoparnActualizarReenvio(@RequestBody @Valid CoparnResendInput.Root request) {
        try {

            if(request.getPrefix() == null || request.getPrefix().getInput() == null) {
                throw new IllegalArgumentException("Invalid request");
            }

            CoparnResendInput.Input input = request.getPrefix().getInput();
            CoparnResendOutput output = coparnResendService.resendCoparn(input.getSeteoEdiCoparnId(), input.getEdiCoparnId());
            return ResponseEntity.ok(new ResponseController<>(output));
        } catch (Exception e) {
            logger.error("An error occurred while processing the request.", e);
            CoparnResendOutput output = new CoparnResendOutput();
            output.setRespEstado(0);
            output.setRespMensaje(e.toString());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(new ResponseController<>(output));
        }
    }
}