package com.maersk.sd1.sds.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sds.dto.ProductListInput;
import com.maersk.sd1.sds.dto.ProductListOutput;
import com.maersk.sd1.sds.service.ProductListService;
import jakarta.validation.Valid;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/ModuleSDS/module/sds/SDSProductoServiceImp")
public class ProductListController {

    private static final Logger logger = LogManager.getLogger(ProductListController.class);

    private final ProductListService productListService;

    @Autowired
    public ProductListController(ProductListService productListService) {
        this.productListService = productListService;
    }

    @PostMapping("/sdsproductoListar")
    public ResponseEntity<ResponseController<ProductListOutput>> listProducts(
            @RequestBody @Valid ProductListInput.Root request) {
        try {
            ProductListInput.Input input = request.getPrefix().getInput();
            ProductListOutput output = productListService.listProducts(input);
            return ResponseEntity.ok(new ResponseController<>(output));
        } catch (Exception e) {
            logger.error("An error occurred while listing products.", e);
            ProductListOutput output = new ProductListOutput();
            output.setResponseStatus(0);
            output.setResponseMessage(e.getMessage());
            return ResponseEntity.status(500).body(new ResponseController<>(output));
        }
    }
}

