package com.maersk.sd1.sds.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sds.dto.VesselProgrammingListInput;
import com.maersk.sd1.sds.dto.VesselProgrammingListOutput;
import com.maersk.sd1.sds.service.VesselProgrammingListService;
import jakarta.validation.Valid;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.HttpStatus;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/ModuleSDS/module/sds/SDSProgramacionNaveServiceImp")
public class VesselProgrammingListController {

    private static final Logger logger = LogManager.getLogger(VesselProgrammingListController.class.getName());

    private final VesselProgrammingListService vesselProgrammingListService;

    @Autowired
    public VesselProgrammingListController(VesselProgrammingListService vesselProgrammingListService) {
        this.vesselProgrammingListService = vesselProgrammingListService;
    }

    @PostMapping("/sdslistarProgramacionNave")
    public ResponseEntity<ResponseController<VesselProgrammingListOutput>> listVesselProgramming(@RequestBody @Valid VesselProgrammingListInput.Root request) {
        try {
            VesselProgrammingListInput.Prefix prefix = request.getPrefix();
            if (prefix == null || prefix.getInput() == null) {
                throw new IllegalArgumentException("Prefix or input cannot be null");
            }
            VesselProgrammingListInput.Input input = prefix.getInput();
            VesselProgrammingListOutput output = vesselProgrammingListService.listVesselProgramming(input);

            return ResponseEntity.status(HttpStatus.OK).body(new ResponseController<>(output));
        } catch (Exception e) {
            logger.error("An error occurred while processing the request.", e);
            VesselProgrammingListOutput output = new VesselProgrammingListOutput();
            output.setTotalCount(0);
            output.setData(null);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(new ResponseController<>(output));
        }
    }
}