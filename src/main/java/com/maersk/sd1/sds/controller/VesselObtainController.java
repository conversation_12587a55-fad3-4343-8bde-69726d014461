package com.maersk.sd1.sds.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sds.dto.VesselObtainInput;
import com.maersk.sd1.sds.dto.VesselObtainOutput;
import com.maersk.sd1.sds.service.VesselObtainService;
import jakarta.validation.Valid;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/ModuleSDS/module/sds/SDSNaveServiceImp")
public class VesselObtainController {

    private static final Logger logger = LogManager.getLogger(VesselObtainController.class);

    private final VesselObtainService vesselObtainService;

    public VesselObtainController(VesselObtainService vesselObtainService) {
        this.vesselObtainService = vesselObtainService;
    }

    @PostMapping("/sdsnaveObtener")
    public ResponseEntity<ResponseController<VesselObtainOutput>> obtainVessel(
            @RequestBody @Valid VesselObtainInput.Root request) {
        try {
            Integer naveId = request.getPrefix().getInput().getNaveId();
            VesselObtainOutput output = vesselObtainService.getVessel(naveId);
            return ResponseEntity.ok(new ResponseController<>(output));
        } catch (Exception e) {
            logger.error("An error occurred while obtaining vessel.", e);
            VesselObtainOutput output = new VesselObtainOutput();
            output.setRespStatus(0);
            output.setRespMessage(e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(new ResponseController<>(output));
        }
    }
}

