package com.maersk.sd1.sds.controller.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

public class ActivityLogDisableInput {

    private ActivityLogDisableInput() {}

    @Data
    public static class Input {

        @JsonProperty("user_modification_id")
        @NotNull(message = "user_modification_id cannot be null")
        private Integer userModificationId;

        @JsonProperty("activity_log_id")
        @NotNull(message = "activity_log_id cannot be null")
        private Integer activityLogId;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("SDS")
        private Prefix prefix;
    }
}
