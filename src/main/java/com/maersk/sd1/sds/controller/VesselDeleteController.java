package com.maersk.sd1.sds.controller;

import com.maersk.sd1.sds.dto.VesselDeleteInput;
import com.maersk.sd1.sds.dto.VesselDeleteOutput;
import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sds.service.VesselDeleteService;
import jakarta.validation.Valid;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/ModuleSDS/module/sds/SDSNaveServiceImp")
public class VesselDeleteController {

    private static final Logger logger = LogManager.getLogger(VesselDeleteController.class);

    private final VesselDeleteService vesselDeleteService;

    @Autowired
    public VesselDeleteController(VesselDeleteService vesselDeleteService) {
        this.vesselDeleteService = vesselDeleteService;
    }

    @PostMapping("/sdsnaveEliminar")
    public ResponseEntity<ResponseController<VesselDeleteOutput>> deleteVessel(@RequestBody @Valid VesselDeleteInput.Root request) {
        try {

            if(request.getPrefix() == null || request.getPrefix().getInput() == null) {
                return ResponseEntity.badRequest().body(new ResponseController<>(new VesselDeleteOutput()));
            }

            VesselDeleteInput.Input input = request.getPrefix().getInput();
            VesselDeleteOutput output = vesselDeleteService.deleteVessel(
                    input.getVesselId(),
                    input.getUserModificationId(),
                    input.getLanguageId()
            );
            return ResponseEntity.ok(new ResponseController<>(output));
        } catch (Exception e) {
            logger.error("An error occurred while deleting the vessel.", e);
            VesselDeleteOutput output = new VesselDeleteOutput();
            output.setRespEstado(0);
            output.setRespMensaje(e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(new ResponseController<>(output));
        }
    }
}
