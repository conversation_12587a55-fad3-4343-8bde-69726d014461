package com.maersk.sd1.sds.controller.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class SettingEdiCoparnObtainOutputDTO {

    @JsonProperty("resp_estado")
    private Integer respStatus;

    @JsonProperty("resp_mensaje")
    private String respMessage;

    @JsonProperty("seteo_edi_coparn_id")
    private Integer id;

    @JsonProperty("linea_naviera_id")
    private Integer shippingLineId;

    @JsonProperty("cat_canal_recepcion_coparn_id")
    private Integer catCanalRecepcionCoparnId;

    @JsonProperty("cat_modo_procesar_coparn_id")
    private Integer catModoProcesarCoparnId;

    @JsonProperty("edi_coparn_descripcion")
    private String bkEdiDescription;

    @JsonProperty("azure_id")
    private String azureId;

    @JsonProperty("sftp_coparn_id")
    private String bkEdiSftpId;

    @JsonProperty("ftp_coparn_id")
    private String bkEdiFtpId;

    @JsonProperty("carpeta_coparn_ruta")
    private String bkEdiFolderRoute;

    @JsonProperty("extension_archivo_descargar")
    private String downloadFileExtension;

    @JsonProperty("ruta_mover_edi")
    private String ediMoveRoute;

    @JsonProperty("permitir_crear_prog_nave_automatico")
    private Boolean allowCreateAutomaticVesselProgramming;

    @JsonProperty("permitir_crear_cliente_automatico")
    private Boolean allowCreateAutomaticCustomer;

    @JsonProperty("es_historico")
    private Boolean isHistorical;

    @JsonProperty("fecha_debaja")
    private LocalDateTime deactivationDate;

    @JsonProperty("motivo_debaja")
    private String deactivationReason;

    @JsonProperty("activo")
    private Boolean active;

    @JsonProperty("fecha_registro")
    private LocalDateTime registrationDate;

    @JsonProperty("fecha_modificacion")
    private LocalDateTime modificationDate;

    @JsonProperty("unidad_negocio_id")
    private Integer businessUnitId;

    @JsonProperty("cat_bkedi_message_type_id")
    private Integer catBkEdiMessageTypeId;

    @JsonProperty("cat_owner_edi_booking_id")
    private Integer catOwnerEdiBookingId;

    @JsonProperty("filename_mask")
    private String filenameMask;

    @JsonProperty("detalle")
    private List<Details> details;

    @Data
    public static class Details {
        @JsonProperty("sub_unidad_negocio_id")
        private Integer subBusinessUnitId;

        @JsonProperty("aplica_envio_copia_coparn")
        private Boolean bkEdiApplyCopySend;

        @JsonProperty("cat_canal_reenvio_coparn_id")
        private Integer catBkEdiForwardChannelId;

        @JsonProperty("sftp_reenvio_coparn_id")
        private String bkEdiForwardSftpId;

        @JsonProperty("ftp_reenvio_coparn_id")
        private String bkEdiForwardFftpId;

        @JsonProperty("carpeta_reenvio_coparn_ruta")
        private String bkEdiForwardFolderRoute;
    }
}