package com.maersk.sd1.sds.service;

import com.maersk.sd1.common.model.Booking;
import com.maersk.sd1.common.model.BookingDetail;
import com.maersk.sd1.common.model.CargoDocument;
import com.maersk.sd1.common.model.CargoDocumentDetail;
import com.maersk.sd1.common.model.User;
import com.maersk.sd1.sds.dto.BookingDeleteInput;
import com.maersk.sd1.sds.dto.BookingDeleteOutput;
import com.maersk.sd1.common.repository.BookingRepository;
import com.maersk.sd1.common.repository.BookingDetailRepository;
import com.maersk.sd1.common.repository.CargoDocumentRepository;
import com.maersk.sd1.common.repository.CargoDocumentDetailRepository;
import com.maersk.sd1.common.repository.MessageLanguageRepository;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Service
public class BookingDeleteService {

    private static final Logger logger = LogManager.getLogger(BookingDeleteService.class);
    private static final String DELBOOKING = "del-booking";
    private final BookingRepository bookingRepository;
    private final BookingDetailRepository bookingDetailRepository;
    private final CargoDocumentRepository cargoDocumentRepository;
    private final CargoDocumentDetailRepository cargoDocumentDetailRepository;
    private final MessageLanguageRepository messageLanguageRepository;


    @Autowired
    public BookingDeleteService(BookingRepository bookingRepository, BookingDetailRepository bookingDetailRepository,
                                CargoDocumentRepository cargoDocumentRepository, CargoDocumentDetailRepository cargoDocumentDetailRepository,
                                MessageLanguageRepository messageLanguageRepository) {
        this.bookingRepository = bookingRepository;
        this.bookingDetailRepository = bookingDetailRepository;
        this.cargoDocumentRepository = cargoDocumentRepository;
        this.cargoDocumentDetailRepository = cargoDocumentDetailRepository;
        this.messageLanguageRepository = messageLanguageRepository;
    }

    @Transactional
    public BookingDeleteOutput removeBooking(BookingDeleteInput.Input input) {
        BookingDeleteOutput output = new BookingDeleteOutput();
        output.setRespEstado(0);
        output.setRespMensaje("");

        try {
            Optional<Booking> optBooking = bookingRepository.findById(input.getBookingId());
            if (!optBooking.isPresent()) {
                logger.error("Booking not found for ID: {}", input.getBookingId());
                output.setRespEstado(2);
                output.setRespMensaje("Booking not found.");
                return output;
            }
            Booking booking = optBooking.get();

            if (!Boolean.TRUE.equals(booking.getActive())) {
                logger.info("Booking is already inactive, ignoring.");
                output.setRespEstado(1);
                String alreadyRemovedMsg = messageLanguageRepository.fnTranslatedMessage("GENERAL", 7, input.getIdiomaId());
                output.setRespMensaje(alreadyRemovedMsg);
                return output;
            }

            int totalAsignados;

            if (Boolean.TRUE.equals(booking.getApprovedBooking())) {
                Long count = cargoDocumentDetailRepository.countAssignedByBookingId(booking.getId());
                totalAsignados = (count == null ? 0 : count.intValue());
            } else {
                Integer sum = bookingDetailRepository.sumAttendedByBooking(booking.getId());
                totalAsignados = (sum == null ? 0 : sum);
            }

            if (totalAsignados > 0) {
                logger.info("Booking has assigned containers. totalAsignados: {}", totalAsignados);
                output.setRespEstado(2);
                String failMsg = messageLanguageRepository.fnTranslatedMessage("DEL_BOOKING", 1, input.getIdiomaId());
                output.setRespMensaje(failMsg);
                return output;
            }

            booking.setActive(false);
            User modifyingUser = new User();
            modifyingUser.setId(input.getUsuarioModificacionId());

            booking.setModificationUser(modifyingUser);
            booking.setModificationDate(LocalDateTime.now());
            booking.setTraceBooking(DELBOOKING);
            bookingRepository.save(booking);

            List<BookingDetail> bookingDetails = bookingDetailRepository.findAllById(
                    bookingDetailRepository.findAll().stream()
                            .filter(bd -> bd.getBooking().getId().equals(booking.getId()))
                            .map(BookingDetail::getId).toList()
            );
            for (BookingDetail bd : bookingDetails) {
                bd.setActive(false);
                bd.setModificationUser(modifyingUser);
                bd.setModificationDate(LocalDateTime.now());
            }
            bookingDetailRepository.saveAll(bookingDetails);

            if (Boolean.TRUE.equals(booking.getApprovedBooking())) {
                CargoDocument doc = cargoDocumentRepository.findActiveByDocAndVessel(
                        booking.getBookingNumber(), booking.getVesselProgrammingDetail());
                if (doc != null) {
                    doc.setActive(false);
                    doc.setModificationUser(modifyingUser);
                    doc.setModificationDate(LocalDateTime.now());
                    doc.setTraceCargoDocument(DELBOOKING);

                    cargoDocumentRepository.save(doc);

                    List<CargoDocumentDetail> cddList = cargoDocumentDetailRepository.findAllActiveByCargoDocumentId(doc.getId());
                    for (CargoDocumentDetail cdd : cddList) {
                        cdd.setActive(false);
                        cdd.setModificationUser(modifyingUser);
                        cdd.setModificationDate(LocalDateTime.now());

                        cdd.setTraceCargoDocumentDetail(DELBOOKING);
                    }
                    cargoDocumentDetailRepository.saveAll(cddList);
                }
            }
            output.setRespEstado(1);
            String successMsg = messageLanguageRepository.fnTranslatedMessage("GENERAL", 7, input.getIdiomaId());
            output.setRespMensaje(successMsg);

        } catch (Exception e) {
            logger.error("Error removing booking.", e);
            output.setRespEstado(0);
            output.setRespMensaje(e.getMessage());
        }

        return output;
    }
}

