package com.maersk.sd1.sds.service;

import com.maersk.sd1.common.repository.*;
import com.maersk.sd1.sds.dto.DeleteVesselProgrammingDetailOutput;
import com.maersk.sd1.common.model.VesselProgrammingDetail;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@RequiredArgsConstructor
@Service
public class DeleteVesselProgrammingDetailService {

    private static final Logger logger = LogManager.getLogger(DeleteVesselProgrammingDetailService.class);

    private final BookingRepository bookingRepository;
    private final CargoDocumentRepository cargoDocumentRepository;
    private final VesselProgrammingContainerRepository vesselProgrammingContainerRepository;
    private final EirRepository eirRepository;
    private final VesselProgrammingCutoffRepository vesselProgrammingCutoffRepository;
    private final VesselProgrammingDetailRepository vesselProgrammingDetailRepository;
    private final MessageLanguageRepository messageLanguageRepository;

    @Transactional
    public DeleteVesselProgrammingDetailOutput deleteVesselProgrammingDetail(Integer detailId,
                                                                             BigDecimal userId,
                                                                             Integer languageId) {
        DeleteVesselProgrammingDetailOutput output = new DeleteVesselProgrammingDetailOutput();
        output.setRespEstado(0);
        output.setRespMensaje("");
        try {
            logger.info("Starting deletion process for VesselProgrammingDetail with id: {}", detailId);

            VesselProgrammingDetail detailRef = new VesselProgrammingDetail();
            detailRef.setId(detailId);

            boolean isUsed = bookingRepository.countByVesselProgrammingDetailAndActiveTrue(detailRef) > 0 ||
                    cargoDocumentRepository.countByVesselProgrammingDetailAndActiveTrue(detailRef) > 0 ||
                    vesselProgrammingContainerRepository.countByVesselProgrammingDetailAndActiveTrue(detailRef) > 0 ||
                    eirRepository.countByVesselProgrammingDetailAndActiveTrue(detailRef) > 0;

            if (isUsed) {
                logger.info("Detail id {} is used in other records, cannot delete.", detailId);
                output.setRespEstado(2);
                String msg = messageLanguageRepository.fnTranslatedMessage("GENERAL", 6, languageId);
                output.setRespMensaje(msg);
                return output;
            }

            logger.info("Deactivating vessel programming cutoff, detail id: {}", detailId);
            int uid = userId.intValue();

            vesselProgrammingCutoffRepository.deactivateCutoff(
                    detailId,
                    uid,
                    LocalDateTime.now()
            );

            logger.info("Deactivating vessel programming detail, detail id: {}", detailId);
            vesselProgrammingDetailRepository.deactivateDetail(
                    detailId,
                    uid,
                    LocalDateTime.now()
            );

            output.setRespEstado(1);
            String msg = messageLanguageRepository.fnTranslatedMessage("GENERAL", 7, languageId);
            output.setRespMensaje(msg);

        } catch (Exception e) {
            logger.error("Error deleting vessel programming detail", e);
            output.setRespEstado(0);
            output.setRespMensaje(e.getMessage());
        }
        return output;
    }
}
