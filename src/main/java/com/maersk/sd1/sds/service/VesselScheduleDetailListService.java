package com.maersk.sd1.sds.service;

import com.maersk.sd1.common.repository.CatalogLanguageRepository;
import com.maersk.sd1.sds.dto.VesselScheduleDetailListOutput;
import com.maersk.sd1.common.repository.VesselProgrammingDetailRepository;
import com.maersk.sd1.common.repository.VesselProgrammingCutoffRepository;
import com.maersk.sd1.common.model.VesselProgrammingDetail;
import com.maersk.sd1.common.model.VesselProgrammingCutoff;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

@RequiredArgsConstructor
@Service
public class VesselScheduleDetailListService {

    private static final Logger logger = LogManager.getLogger(VesselScheduleDetailListService.class);

    private final VesselProgrammingDetailRepository vesselProgrammingDetailRepository;
    private final VesselProgrammingCutoffRepository vesselProgrammingCutoffRepository;
    private final CatalogLanguageRepository catalogLanguageRepository;

    @Transactional(readOnly = true)
    public VesselScheduleDetailListOutput retrieveVesselProgrammingDetail(Integer detailId, Integer languageId) {
        logger.info("retrieveVesselProgrammingDetail called with detailId={}, languageId={} ", detailId, languageId);

        var detailOpt = vesselProgrammingDetailRepository.findByIdAndActive(detailId, Boolean.TRUE);
        if (detailOpt.isEmpty()) {
            logger.error("No vesselProgrammingDetail found for detailId={} ", detailId);
            throw new IllegalArgumentException("No VesselProgrammingDetail found for the provided detailId.");
        }

        VesselProgrammingDetail detail = detailOpt.get();

        Integer catOperationId = detail.getCatOperation() != null ? detail.getCatOperation().getId() : null;
        String operationName = null;
        if (catOperationId != null) {
            operationName = catalogLanguageRepository.fnCatalogoTraducidoDesLarga(catOperationId, languageId);
        }

        VesselScheduleDetailListOutput output = new VesselScheduleDetailListOutput();
        output.setDetailId(detail.getId());
        output.setCatOperationId(catOperationId);
        output.setOperation(operationName);
        output.setManifestYear(detail.getManifestYear());
        output.setManifestNumber(detail.getManifestNumber());
        output.setBeginningOperation(detail.getBeginningOperation());
        output.setEndingOperation(detail.getEndingOperation());
        output.setManifestCustomsDate(detail.getManifestCustomsDate());
        output.setBeginningReturnAppointment(detail.getBeginningReturnAppointment());
        output.setDryPortCutOffDate(detail.getDryPortCutOffDate());
        output.setReeferPortCutoffDate(detail.getReeferPortCutoffDate());
        output.setDryyDepositCutoffDate(detail.getDryyDepositCutoffDate());
        output.setReeferDepositCutoffDate(detail.getReeferDepositCutoffDate());
        output.setExpoEcuAppointmentsBegginingDate(detail.getExpoEcuAppointmentsBegginingDate());

        List<VesselProgrammingCutoff> cutoffs = vesselProgrammingCutoffRepository.findByVesselProgrammingDetailIdAndActive(detail.getId(), Boolean.TRUE);
        List<VesselScheduleDetailListOutput.ShippingLineCutoffData> cutoffDataList = new ArrayList<>();
        for (VesselProgrammingCutoff c : cutoffs) {
            VesselScheduleDetailListOutput.ShippingLineCutoffData data = new VesselScheduleDetailListOutput.ShippingLineCutoffData();
            if (c.getShippingLine() != null) {
                data.setShippingLineId(c.getShippingLine().getId());
                data.setShippingLineCompany(c.getShippingLine().getShippingLineCompany());
            }
            data.setRetiroDry(c.getDateCutoffRetreatEmptyDry());
            data.setRetiroReefer(c.getDateCutoffRetreatEmptyReefer());
            cutoffDataList.add(data);
        }
        output.setCutoffShippingLines(cutoffDataList);

        logger.info("retrieveVesselProgrammingDetail successfully processed detailId={}.", detailId);
        return output;
    }
}
