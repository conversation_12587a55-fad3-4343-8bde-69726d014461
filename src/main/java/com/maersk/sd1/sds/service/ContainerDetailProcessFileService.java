package com.maersk.sd1.sds.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.maersk.sd1.common.Constants;
import com.maersk.sd1.common.model.*;
import com.maersk.sd1.common.repository.*;
import com.maersk.sd1.sds.dto.ContainerDetailProcessFileInput;
import com.maersk.sd1.sds.dto.SequenceDetail;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import static com.maersk.sd1.common.Constants.EDI_BOOKING_CANCEL;


@Service
public class ContainerDetailProcessFileService {

    private final CatalogRepository catalogRepository;
    private final BookingRepository bookingRepository;
    private final BookingEdiRepository bookingEdiRepository;
    private final BookingDetailRepository bookingDetailRepository;
    private final CargoDocumentRepository cargoDocumentRepository;
    private final BookingBlockCancellationRepository bookingBlockCancellationRepository;
    private final BookingBlockCancellationDetailRepository bookingBlockCancellationDetailRepository;
    private final UserRepository userRepository;
    private final BusinessUnitRepository businessUnitRepository;

    @Autowired
    public ContainerDetailProcessFileService(CatalogRepository catalogRepository,
                                             BookingRepository bookingRepository,
                                             BookingEdiRepository bookingEdiRepository,
                                             @Qualifier("bookingDetailRepository") BookingDetailRepository bookingDetailRepository,
                                             CargoDocumentRepository cargoDocumentRepository,
                                             BookingBlockCancellationRepository bookingBlockCancellationRepository,
                                             BookingBlockCancellationDetailRepository bookingBlockCancellationDetailRepository,
                                             UserRepository userRepository,
                                             BusinessUnitRepository businessUnitRepository) {
        this.catalogRepository = catalogRepository;
        this.bookingRepository = bookingRepository;
        this.bookingEdiRepository = bookingEdiRepository;
        this.bookingDetailRepository = bookingDetailRepository;
        this.cargoDocumentRepository = cargoDocumentRepository;
        this.bookingBlockCancellationRepository = bookingBlockCancellationRepository;
        this.bookingBlockCancellationDetailRepository = bookingBlockCancellationDetailRepository;
        this.userRepository = userRepository;
        this.businessUnitRepository = businessUnitRepository;
    }





    @Transactional
    public void processContainerDetails(ContainerDetailProcessFileInput.Root input) {


        Integer bookingId = null;
        Catalog bookingStatus = null;
        Boolean bookingApproved = false;
        Integer loadDocumentId = null;
        Boolean refrigeratedLoad = false;


        Integer isBkediRejected = catalogRepository.findIdByAlias("48274");
        Integer isCreationSourceForEdiCancel = catalogRepository.findIdByAlias("47771");
        Integer isBkCancelAutoBkedi = catalogRepository.findIdByAlias("sd1_booking_block_cancel_auto_bkedi");
        Integer isBkediDone = catalogRepository.findIdByAlias("48273");
        Integer isBkStatusCancelled = catalogRepository.findIdByAlias("43062");
        Integer isReferenceReplace = catalogRepository.findIdByAlias("sd1_bkedi_reference_replace");
        Integer isCreationSourceForEdi = catalogRepository.findIdByAlias("47739");
        Integer isCreationSourceForEdiCancelAuto = catalogRepository.findIdByAlias("47740");
        Integer isContainerTypeReeferCa = catalogRepository.findIdByAlias("31048");
        Integer isBkStatusActive = catalogRepository.findIdByAlias("43061");
        Integer coparnReferenceCategoryIdModification = isReferenceReplace;
        Integer bookingCreationOriginCategoryId = isCreationSourceForEdi;

        ObjectMapper objectMapper = new ObjectMapper();
        List<SequenceDetail> sequenceDetails2 = new ArrayList<>();

        if (input.getInput().getParameterSequenceDetails() != null && !input.getInput().getParameterSequenceDetails().isEmpty()) {
            try {
                JsonNode jsonNode = objectMapper.readTree(input.getInput().getParameterSequenceDetails());
                if (jsonNode != null && jsonNode.isArray()) {

                    for (JsonNode node : jsonNode) {
                        SequenceDetail detail = new SequenceDetail();
                        detail.setSequenceAlias(node.path("sequence_alias").asText());
                        sequenceDetails2.add(detail);
                    }
                }
            } catch (Exception e) {
                throw new UnexpectedException("An unexpected error occurred", e);
            }
        }


        String code = catalogRepository.findCodeByCatalogId(input.getInput().getContainerTypeId());
        if (code != null && code.equals("true")) {
            refrigeratedLoad = true;
        }

        if (Boolean.TRUE.equals(isNumeric(input.getInput().getTemperature()) && input.getInput().getControlledAtmosphere()) && Boolean.TRUE.equals(refrigeratedLoad)) {
            Double tempValue = Double.parseDouble(input.getInput().getTemperature().replace(',', '.'));
            if (tempValue > 0) {
                input.getInput().setContainerTypeId(isContainerTypeReeferCa);
                if (input.getInput().getContainerType2id() != null) {
                    String code2 = catalogRepository.findCodeByCatalogId(input.getInput().getContainerType2id());
                    if (code2 != null && code2.equals("true")) {
                        input.getInput().setContainerType2id(isContainerTypeReeferCa);
                    }
                }
            }
        }
        try {
            if ("1".equals(input.getInput().getReservationStatus())) {
                Optional<Booking> bookingOpt = bookingRepository.findTopByBookingNumberAndVesselProgrammingDetailIdOrderByBookingIssueDateDesc(
                        input.getInput().getBooking(), input.getInput().getVesselProgrammingDetailId());

                if (bookingOpt.isPresent()) {
                    Booking bookingEntity = bookingOpt.get();
                    bookingId = bookingEntity.getId();
                    bookingStatus = bookingEntity.getCatBookingStatus();
                    bookingApproved = bookingEntity.getApprovedBooking();

                    if (!(isBkStatusActive.equals(bookingStatus.getId()))) {
                        updateBookingEdiStatus(input.getInput().getBookingEdi(), isBkediRejected, input.getInput().getUserRegistrationId(), Constants.BOOKING_NOT_ACTIVE);
                    } else {
                        if (Boolean.FALSE.equals(bookingApproved)) {
                            // call [sds].[aprobar_booking] Store procedure
                            approveBooking(bookingId, input.getInput().getUserRegistrationId(), isCreationSourceForEdiCancel);
                        }

                        loadDocumentId = cargoDocumentRepository.findActiveCargoDocumentByBookingId(bookingId);

                        if (loadDocumentId == null) {
                            updateBookingEdiObservation(input.getInput().getBookingEdi(), input.getInput().getUserRegistrationId(), Constants.BOOKING_NOT_FOUND);
                        } else {
                            BookingBlockCancellation bookingBlockCancellation = new BookingBlockCancellation();
                            BusinessUnit businessUnit = businessUnitRepository.getReferenceById(input.getInput().getBusinessUnitId());
                            bookingBlockCancellation.setBusinessUnit(businessUnit);
                            BusinessUnit subBusinessUnit = businessUnitRepository.getReferenceById(input.getInput().getSubBusinessUnitId());
                            bookingBlockCancellation.setSubBusinessUnit(subBusinessUnit);
                            bookingBlockCancellation.setBlockCancellationDate(LocalDateTime.now());
                            Optional<Catalog> catType = catalogRepository.findById(48167);
                            if (catType.isPresent()) {
                                bookingBlockCancellation.setCatType(catType.get());
                            } else {
                                bookingBlockCancellation.setCatType(new Catalog(48167));
                            }
                            Optional<Catalog> catReason = catalogRepository.findById(48170);
                            if (catReason.isPresent()) {
                                bookingBlockCancellation.setCatReason(catReason.get());
                            } else {
                                bookingBlockCancellation.setCatReason(new Catalog(48170));
                            }

                            bookingBlockCancellation.setComment(Constants.BOOKING_EDI_CANCEL);
                            Optional<User> user = userRepository.findById(input.getInput().getUserRegistrationId());
                            if (user.isPresent()) {
                                bookingBlockCancellation.setRegistrationUser(user.get());
                            } else {
                                bookingBlockCancellation.setRegistrationUser(new User(input.getInput().getUserRegistrationId()));
                            }

                            Optional<Catalog> catOriginCancelBlock = catalogRepository.findById(isBkCancelAutoBkedi);
                            if (catOriginCancelBlock.isPresent()) {
                                bookingBlockCancellation.setCatOriginCancelBlock(catOriginCancelBlock.get());
                            } else {
                                bookingBlockCancellation.setCatOriginCancelBlock(new Catalog(isBkCancelAutoBkedi));
                            }
                            bookingBlockCancellation.setActive(true);
                            bookingBlockCancellation.setRegistrationDate(LocalDateTime.now());
                            bookingBlockCancellation = bookingBlockCancellationRepository.save(bookingBlockCancellation);


                            // Insert into BookingBlockCancellationDetail
                            BookingBlockCancellationDetail bookingBlockCancellationDetail = new BookingBlockCancellationDetail();
                            bookingBlockCancellationDetail.setBookingBlockCancellation(bookingBlockCancellation);
                            Optional<CargoDocument> cargoDocument = Optional.ofNullable(cargoDocumentRepository.findById(loadDocumentId).orElseThrow(() -> new RuntimeException("LoadDocument not found")));

                            if (cargoDocument.isPresent()) {
                                bookingBlockCancellationDetail.setCargoDocument(cargoDocument.get());
                            } else {
                                bookingBlockCancellationDetail.setCargoDocument(new CargoDocument());
                            }
                            bookingBlockCancellationDetail.setReleasedBooking(false);
                            bookingBlockCancellationDetail.setActive(true);
                            bookingBlockCancellationDetailRepository.save(bookingBlockCancellationDetail);


                            // Update Booking
                            if (Boolean.FALSE.equals(bookingApproved)) {
                                bookingCreationOriginCategoryId = isCreationSourceForEdiCancelAuto;
                            }
                            Optional<Catalog> catBookingStatus = catalogRepository.findById(isBkStatusCancelled);
                            if (catBookingStatus.isPresent()) {
                                bookingEntity.setCatBookingStatus(catBookingStatus.get());
                            } else {
                                bookingEntity.setCatBookingStatus(new Catalog(isBkStatusCancelled));
                            }
                            user.ifPresent(bookingEntity::setModificationUser);
                            bookingEntity.setModificationDate(LocalDateTime.now());
                            bookingEntity.setTraceBooking(EDI_BOOKING_CANCEL);
                            Optional<BookingEdi> bookingEdi = bookingEdiRepository.findById(input.getInput().getBookingEdi());
                            bookingEdi.ifPresent(bookingEntity::setBookingEdiReference);
                            bookingRepository.save(bookingEntity);

                            if (cargoDocument.isPresent()) {
                                catBookingStatus.ifPresent(catalog -> cargoDocument.get().setCatDocumentCargoStatus(catalog));
                                user.ifPresent(value -> cargoDocument.get().setModificationUser(value));
                                cargoDocument.get().setModificationDate(LocalDateTime.now());
                                cargoDocument.get().setTraceCargoDocument(EDI_BOOKING_CANCEL);
                                cargoDocumentRepository.save(cargoDocument.get());
                            }
                            updateBookingEdiStatus(input.getInput().getBookingEdi(), isBkediDone, input.getInput().getUserRegistrationId(), Constants.BOOKING_CANCEL);

                        }
                    }
                } else {
                    updateBookingEdiObservation(input.getInput().getBookingEdi(), input.getInput().getUserRegistrationId(), Constants.BOOKING_NOT_FOUND);
                }
            } else if ("2".equals(input.getInput().getReservationStatus())) {

                Optional<Booking> bookingOpt = bookingRepository.findTopByBookingNumberAndVesselProgrammingDetailIdOrderByBookingIssueDateDesc(input.getInput().getBooking(),
                        input.getInput().getVesselProgrammingDetailId());
                if (bookingOpt.isPresent()) {
                    Booking bookingEntity = bookingOpt.get();
                    bookingId = bookingEntity.getId();
                    bookingApproved = bookingEntity.getApprovedBooking();
                    Optional<BookingDetail> bookingDetail;

                    if (Boolean.TRUE.equals(bookingApproved) && bookingId != null) {
                        if ((input.getInput().getContainerDimensionId() != null && input.getInput().getContainerDimensionId() > 0) &&
                                (input.getInput().getContainerTypeId() != null && input.getInput().getContainerTypeId() > 0) &&
                                (input.getInput().getReservedQuantity() != null && input.getInput().getReservedQuantity() > 0)) {

                            if (Boolean.FALSE.equals(bookingDetailRepository.existsByBookingIdAndAttendedQuantityGreaterThanAndActive(
                                    bookingId, 0, true))) {

                                if (sequenceDetails2.isEmpty()) {
                                    bookingDetail = bookingDetailRepository.findByIdAndCatSizeAndCatContainerTypeAndActiveTrueOrderByRegistrationDate(
                                            bookingId, input.getInput().getContainerDimensionId(), input.getInput().getContainerTypeId());

                                    if (bookingDetail.isEmpty()) {

                                        BookingDetail newBookingDetail = new BookingDetail();
                                        Optional<Booking> booking = bookingRepository.findById(bookingId);
                                        booking.ifPresent(newBookingDetail::setBooking);
                                        newBookingDetail.setCatSize(new Catalog(input.getInput().getContainerDimensionId()));
                                        newBookingDetail.setCatContainerType(new Catalog((input.getInput().getContainerTypeId())));
                                        newBookingDetail.setReservationQuantity(input.getInput().getReservedQuantity());
                                        newBookingDetail.setAttendedQuantity(0);
                                        newBookingDetail.setMaximumLoadRequired(input.getInput().getGrossWeightEDI());
                                        newBookingDetail.setCatOriginBookingCreation(new Catalog(bookingCreationOriginCategoryId));
                                        newBookingDetail.setActive(true);
                                        Optional<User> currentUser = userRepository.findById(input.getInput().getUserRegistrationId());
                                        currentUser.ifPresent(newBookingDetail::setRegistrationUser);
                                        newBookingDetail.setRegistrationDate(LocalDateTime.now());
                                        Optional<BookingEdi> bookingEdiReference = bookingEdiRepository.findById(input.getInput().getBookingEdi());
                                        bookingEdiReference.ifPresent(newBookingDetail::setBookingEdiReference);
                                        newBookingDetail.setTraceBkDetail(Constants.BOOKING_EDI_INSERT);

                                        bookingDetailRepository.save(newBookingDetail);

                                    } else {

                                        bookingDetail.get().setReservationQuantity(bookingDetail.get().getReservationQuantity() + input.getInput().getReservedQuantity());
                                        bookingDetail.get().setRegistrationDate(LocalDateTime.now());
                                        Optional<User> currentUser = userRepository.findById(input.getInput().getUserRegistrationId());
                                        currentUser.ifPresent(user -> bookingDetail.get().setModificationUser(user));
                                        Optional<BookingEdi> bookingEdiReference = bookingEdiRepository.findById(input.getInput().getBookingEdi());
                                        bookingEdiReference.ifPresent(bookingEdi -> bookingDetail.get().setBookingEdiReference(bookingEdi));
                                        bookingDetail.get().setTraceBkDetail(Constants.BOOKING_EDI_UPDATE);
                                        bookingDetailRepository.save(bookingDetail.get());

                                    }
                                }
                                if (!sequenceDetails2.isEmpty()) {
                                    for (SequenceDetail detail : sequenceDetails2) {

                                        BookingDetail bookingDetail1 = new BookingDetail();
                                        Optional<Booking> booking = bookingRepository.findById(bookingId);
                                        booking.ifPresent(bookingDetail1::setBooking);
                                        bookingDetail1.setCatSize(new Catalog(input.getInput().getContainerDimensionId()));
                                        bookingDetail1.setCatContainerType(new Catalog((input.getInput().getContainerTypeId())));
                                        bookingDetail1.setReservationQuantity(1);
                                        bookingDetail1.setAttendedQuantity(0);
                                        bookingDetail1.setMaximumLoadRequired(input.getInput().getGrossWeightEDI());
                                        bookingDetail1.setCatOriginBookingCreation(new Catalog(bookingCreationOriginCategoryId));
                                        bookingDetail1.setActive(true);
                                        Optional<User> currentUser1 = userRepository.findById(input.getInput().getUserRegistrationId());
                                        currentUser1.ifPresent(bookingDetail1::setRegistrationUser);
                                        bookingDetail1.setRegistrationDate(LocalDateTime.now());
                                        Optional<BookingEdi> bookingEdiReference1 = bookingEdiRepository.findById(input.getInput().getBookingEdi());
                                        bookingEdiReference1.ifPresent(bookingDetail1::setBookingEdiReference);
                                        bookingDetail1.setTraceBkDetail(Constants.BOOKING_EDI_ADD_SEQUENCE);
                                        bookingDetail1.setSequenceAlias(detail.getSequenceAlias());
                                        bookingDetailRepository.save(bookingDetail1);
                                    }
                                }
                                Optional<Booking> saveBooking = bookingRepository.findById(bookingId);
                                Optional<Catalog> catBkEdiRef = catalogRepository.findById(coparnReferenceCategoryIdModification);
                                catBkEdiRef.ifPresent(catalog -> saveBooking.get().setCatBkEdiReference(catalog));
                                saveBooking.get().setBkEdiReferenceDate(LocalDateTime.now());
                                saveBooking.get().setModificationDate(LocalDateTime.now());
                                Optional<User> currentUser1 = userRepository.findById(input.getInput().getUserRegistrationId());
                                currentUser1.ifPresent(user -> saveBooking.get().setModificationUser(user));
                                saveBooking.get().setTraceBooking(Constants.BOOKING_EDI_ADDITION);
                                Optional<BookingEdi> bookingEdiReference1 = bookingEdiRepository.findById(input.getInput().getBookingEdi());
                                bookingEdiReference1.ifPresent(bookingEdi -> saveBooking.get().setBookingEdiReference(bookingEdi));
                                bookingRepository.save(saveBooking.get());


                                Optional<BookingEdi> saveBookingEdi = bookingEdiRepository.findById(input.getInput().getBookingEdi());

                                if(saveBookingEdi.isPresent()) {
                                    saveBookingEdi.get().setCatBkEdiStatus(new Catalog(48273));
                                    Optional<User> currentUser2 = userRepository.findById(input.getInput().getUserRegistrationId());
                                    currentUser2.ifPresent(user -> saveBookingEdi.get().setProcessedUser(user));
                                    saveBookingEdi.get().setDateProcessedCoparn(LocalDateTime.now());
                                    saveBookingEdi.get().setBkEdiProcessedComment(Constants.RESERVE_ADDED);
                                    saveBookingEdi.get().setBkEdiCommentForProcess(null);
                                    bookingEdiRepository.save(saveBookingEdi.get());
                                }
                                else {
                                    throw new BookingEdiNotFoundException(Constants.BOOKING_EDI_NOT_FOUND);
                                }

                            }
                        }

                    }
                }


            } else {
                Optional<BookingEdi> bookingEdi = bookingEdiRepository.findById(input.getInput().getBookingEdi());
                if(bookingEdi.isPresent()) {
                    bookingEdi.get().setBkEdiProcessedComment(Constants.BOOKING_NOT_FOUND_ADD);
                    bookingEdi.get().setDateProcessedCoparn(LocalDateTime.now());
                    Optional<User> currentUser1 = userRepository.findById(input.getInput().getUserRegistrationId());
                    currentUser1.ifPresent(user -> bookingEdi.get().setProcessedUser(user));
                    bookingEdiRepository.save(bookingEdi.get());
                }
                else {
                    throw new BookingEdiNotFoundException(Constants.BOOKING_EDI_NOT_FOUND);
                }

            }
        } catch (RuntimeException re) {
            if ("Booking EDI not found".equals(re.getMessage())) {
                throw new BookingEdiNotFoundException(Constants.BOOKING_EDI_NOT_FOUND);
            } else {
                throw new UnexpectedRuntimeException("An unexpected runtime error occurred", re);
            }
        } catch (Exception e) {
            throw new UnexpectedException("An unexpected error occurred", e);
        }
    }


    private boolean isNumeric(String str) {
        if (str == null) {
            return false;
        }
        try {
            Double.parseDouble(str.replace(',', '.'));
            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }


    private void updateBookingEdiStatus(Integer bookingId, Integer status, Integer userRegistrationId, String comment) {
        BookingEdi bookingEdi = bookingEdiRepository.findById(bookingId).orElseThrow(() -> new RuntimeException(Constants.BOOKING_EDI_NOT_FOUND));

        if (status != null) {
            Optional<Catalog> catBkEdiStatus = catalogRepository.findById(status);
            catBkEdiStatus.ifPresent(bookingEdi::setCatBkEdiStatus);
        }
        User user = userRepository.getReferenceById(userRegistrationId);
        bookingEdi.setProcessedUser(user);
        bookingEdi.setDateProcessedCoparn(LocalDateTime.now());
        bookingEdi.setBkEdiProcessedComment(comment);
        bookingEdi.setBkEdiCommentForProcess(null);
        bookingEdiRepository.save(bookingEdi);
    }

    private void updateBookingEdiObservation(Integer bookingId, Integer userRegistrationId, String comment) {
        BookingEdi bookingEdi = bookingEdiRepository.findById(bookingId).orElseThrow(() -> new RuntimeException(Constants.BOOKING_EDI_NOT_FOUND));
        bookingEdi.setBkEdiProcessedComment(comment);
        bookingEdi.setDateProcessedCoparn(LocalDateTime.now());
        User user = userRepository.getReferenceById(userRegistrationId);
        bookingEdi.setProcessedUser(user);
        bookingEdiRepository.save(bookingEdi);
    }

    private void approveBooking(Integer bookingId, Integer userRegistrationId, Integer creationSourceForEdiCancel) {
        bookingRepository.approveBooking(bookingId, userRegistrationId, creationSourceForEdiCancel);
    }

    public static class BookingEdiNotFoundException extends RuntimeException {
        public BookingEdiNotFoundException(String message) {
            super(message);
        }
    }

    public static class UnexpectedRuntimeException extends RuntimeException {
        public UnexpectedRuntimeException(String message, Throwable cause) {
            super(message, cause);
        }
    }

    public static class UnexpectedException extends RuntimeException {
        public UnexpectedException(String message, Throwable cause) {
            super(message, cause);
        }
    }


}

