package com.maersk.sd1.sds.service;

import com.maersk.sd1.common.repository.ActivityLogRepository;
import com.maersk.sd1.common.repository.CatalogRepository;

import com.maersk.sd1.sds.controller.dto.ActivityLogDisableOutput;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

@Service
public class ActivityLogDisableService {

    private static final Logger logger = LogManager.getLogger(ActivityLogDisableService.class);

    private final ActivityLogRepository activityLogRepository;

    private final CatalogRepository catalogRepository;

    @Autowired
    public ActivityLogDisableService(ActivityLogRepository activityLogRepository,CatalogRepository catalogRepository)
    {
        this.activityLogRepository = activityLogRepository;
        this.catalogRepository = catalogRepository;
    }

    @Transactional
    public ActivityLogDisableOutput disableActivityLog(Integer userModificationId, Integer activityLogId) {
        ActivityLogDisableOutput output = new ActivityLogDisableOutput();
        try {
            Integer disabledLogStatus = catalogRepository.findDisabledLogStatus();
            if (disabledLogStatus == null) {
                throw new RuntimeException("Catalog status alias 'sd1_aclost_disabled' not found.");
            }

            activityLogRepository.disableActivityLog(
                    userModificationId,
                    LocalDateTime.now(),
                    disabledLogStatus,
                    activityLogId
            );

            output.setRespEstado(1);
            output.setRespMensaje("Disabled correctly");
        } catch (Exception e) {
            logger.error("Error disabling activity log: ", e);
            output.setRespEstado(0);
            output.setRespMensaje(e.getMessage());
        }
        return output;
    }
}
