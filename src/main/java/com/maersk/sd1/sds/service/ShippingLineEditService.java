package com.maersk.sd1.sds.service;

import com.maersk.sd1.common.model.User;
import com.maersk.sd1.common.repository.MessageLanguageRepository;
import com.maersk.sd1.sds.dto.ShippingLineEditInput;
import com.maersk.sd1.sds.dto.ShippingLineEditOutput;
import com.maersk.sd1.common.repository.ShippingLineRepository;
import com.maersk.sd1.common.model.ShippingLine;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Optional;

@Service
public class ShippingLineEditService {

    private static final Logger logger = LogManager.getLogger(ShippingLineEditService.class);

    private final ShippingLineRepository shippingLineRepository;
    private final MessageLanguageRepository messageLanguageRepository;

    @Autowired
    public ShippingLineEditService(ShippingLineRepository shippingLineRepository, MessageLanguageRepository messageLanguageRepository) {
        this.shippingLineRepository = shippingLineRepository;
        this.messageLanguageRepository = messageLanguageRepository;
    }

    @Transactional
    public ShippingLineEditOutput updateShippingLine(ShippingLineEditInput.Input input) {
        ShippingLineEditOutput output = new ShippingLineEditOutput();
        output.setRespEstado(0);
        output.setRespMensaje("");

        try {

            Optional<ShippingLine> optionalShippingLine = shippingLineRepository.findById(input.getLineaNavieraId());
            if (optionalShippingLine.isEmpty()) {
                logger.error("ShippingLine with id {} not found.", input.getLineaNavieraId());
                output.setRespEstado(0);
                output.setRespMensaje("ShippingLine not found");
                return output;
            }

            ShippingLine existingLine = optionalShippingLine.get();
            String oldShippingLineCompany = existingLine.getShippingLineCompany();
            String newShippingLineCompany = (input.getLineaNaviera() != null) ? input.getLineaNaviera() : "";

            boolean changed = (oldShippingLineCompany == null && !newShippingLineCompany.isEmpty())
                    || (oldShippingLineCompany != null && !oldShippingLineCompany.equalsIgnoreCase(newShippingLineCompany));
            if (changed && !newShippingLineCompany.isEmpty()) {
                Optional<ShippingLine> conflictLine = shippingLineRepository
                        .findByShippingLineCompanyIgnoreCaseAndIdNot(newShippingLineCompany, existingLine.getId());
                if (conflictLine.isPresent()) {
                    output.setRespEstado(2);
                    String translatedMsg = messageLanguageRepository.fnTranslatedMessage(
                            "INS_LINEA_NAVIERA", 1, input.getIdiomaId()
                    );
                    translatedMsg = translatedMsg.replace("{IDX}", conflictLine.get().getId().toString());

                    output.setRespMensaje(translatedMsg);
                    return output;
                }
            }

            existingLine.setName(input.getNombre());
            existingLine.setActive(input.getActivo());
            if (existingLine.getModificationUser() == null) {
                existingLine.setModificationUser(new User());
            }
            existingLine.getModificationUser().setId(input.getUsuarioModificacionId());
            existingLine.setShippingLineCompany(input.getLineaNaviera());
            existingLine.setColor(input.getColor());
            existingLine.setModificationDate(LocalDateTime.now());

            shippingLineRepository.save(existingLine);

            output.setRespEstado(1);
            String successMsg = messageLanguageRepository.fnTranslatedMessage(
                    "GENERAL", 10, input.getIdiomaId()
            );
            output.setRespMensaje(successMsg);

        } catch (Exception e) {
            logger.error("Error updating ShippingLine for id {}: {}", input.getLineaNavieraId(), e.getMessage(), e);
            output.setRespEstado(0);
            output.setRespMensaje(e.getMessage());
        }

        return output;
    }
}

