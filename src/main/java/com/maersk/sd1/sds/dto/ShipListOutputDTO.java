package com.maersk.sd1.sds.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

@Data
public class ShipListOutputDTO {

    @JsonProperty("resp_estado")
    private Integer respEstado;

    @JsonProperty("resp_mensaje")
    private String respMensaje;

    @JsonProperty("total_registros")
    private Long totalRecords;

    @JsonProperty("data")
    private List<ShipListRowDTO> data;
}
