package com.maersk.sd1.sds.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class BookingDetailEditOutputDTO {
    @Data
    public static class Output {

        @JsonProperty("response_status")
        private Integer responseStatus;

        @JsonProperty("response_message")
        private String responseMessage;
    }
    @Data
    public static class Prefix {

        @JsonProperty("F")
        private BookingDetailEditOutputDTO.Output output;
    }

    @Data
    public static class Root {

        @JsonProperty("SDS")
        private BookingDetailEditOutputDTO.Prefix prefix;

        public Root() {
            this.prefix = new BookingDetailEditOutputDTO.Prefix();
            this.prefix.setOutput(new BookingDetailEditOutputDTO.Output());
        }
    }
}
