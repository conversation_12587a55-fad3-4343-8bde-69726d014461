package com.maersk.sd1.sds.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class BookingDTO {

    private Integer businessSubUnitId;
    private String bookingNumber;
    private Integer ediCoparnId;
    private String businessSubUnitName;
    private Integer businessUnitId;
    private String businessUnitName;
    private Boolean isCreateAutomaticVesselProgram;
    private Boolean isCreateAutomaticClient;
}
