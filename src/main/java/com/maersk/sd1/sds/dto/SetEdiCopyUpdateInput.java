package com.maersk.sd1.sds.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import jakarta.validation.constraints.PastOrPresent;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Input DTO mirroring the stored procedure parameters for seteo_edi_coparn_editar.
 */
public class SetEdiCopyUpdateInput {

    @Data
    public static class Detail {
        @JsonProperty("sub_unidad_negocio_id")
        @NotNull
        private Long subBusinessUnitId;

        @JsonProperty("aplica_envio_copia_coparn")
        private Boolean bkEdiApplyCopySend;

        @JsonProperty("cat_canal_reenvio_coparn_id")
        private Long catBkEdiForwardChannelId;

        @JsonProperty("ftp_reenvio_coparn_id")
        @Size(max = 100)
        private String bkEdiForwardFftpId;

        @JsonProperty("sftp_reenvio_coparn_id")
        @Size(max = 100)
        private String bkEdiForwardSftpId;

        @JsonProperty("carpeta_reenvio_coparn_ruta")
        @Size(max = 200)
        private String bkEdiForwardFolderRoute;
    }

    @Data
    public static class Input {

        @JsonProperty("seteo_edi_coparn_id")
        @NotNull
        private Integer seteoEdiCoparnId;

        @JsonProperty("linea_naviera_id")
        private Integer lineaNavieraId;

        @JsonProperty("cat_canal_recepcion_coparn_id")
        private Long catCanalRecepcionCoparnId;

        @JsonProperty("cat_modo_procesar_coparn_id")
        private Long catModoProcesarCoparnId;

        @JsonProperty("edi_coparn_descripcion")
        @Size(max = 250)
        private String ediCoparnDescripcion;

        @JsonProperty("azure_id")
        @Size(max = 100)
        private String azureId;

        @JsonProperty("sftp_coparn_id")
        @Size(max = 100)
        private String sftpCoparnId;

        @JsonProperty("ftp_coparn_id")
        @Size(max = 100)
        private String ftpCoparnId;

        @JsonProperty("carpeta_coparn_ruta")
        @Size(max = 200)
        private String carpetaCoparnRuta;

        @JsonProperty("extension_archivo_descargar")
        @Size(max = 10)
        private String extensionArchivoDescargar;

        @JsonProperty("ruta_mover_edi")
        @Size(max = 200)
        private String rutaMoverEdi;

        @JsonProperty("permitir_crear_prog_nave_automatico")
        @NotNull
        private Boolean permitirCrearProgNaveAutomatico;

        @JsonProperty("permitir_crear_cliente_automatico")
        @NotNull
        private Boolean permitirCrearClienteAutomatico;

        @JsonProperty("es_historico")
        @NotNull
        private Boolean esHistorico;

        @JsonProperty("fecha_debaja")
        @PastOrPresent
        private LocalDateTime fechaDebaja;

        @JsonProperty("motivo_debaja")
        @Size(max = 200)
        private String motivoDebaja;

        @JsonProperty("activo")
        @NotNull
        private Boolean activo;

        @JsonProperty("usuario_modificacion_id")
        @NotNull
        private Long usuarioModificacionId;

        @JsonProperty("unidad_negocio_id")
        private Long unidadNegocioId;

        @JsonProperty("detalle")
        private List<Detail> detalle;

        @JsonProperty("cat_bkedi_message_type_id")
        private Long catBkEdiMessageTypeId;

        @JsonProperty("cat_owner_edi_booking_id")
        private Long catOwnerEdiBookingId;

        @JsonProperty("filename_mask")
        @Size(max = 100)
        private String filenameMask;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("SDS")
        private Prefix prefix;
    }
}

