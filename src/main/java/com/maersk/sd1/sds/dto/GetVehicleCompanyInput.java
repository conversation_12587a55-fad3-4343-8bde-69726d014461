package com.maersk.sd1.sds.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class GetVehicleCompanyInput {

    @Data
    public static class Input {
        @JsonProperty("vehicle_id")
        private Integer vehicleId;

        @JsonProperty("vehicle_plate")
        private String vehiclePlate;
    }
    @Data
    public static class Prefix {
        @JsonProperty("F")
        private GetVehicleCompanyInput.Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("SDS")
        private GetVehicleCompanyInput.Prefix prefix;
    }
}
