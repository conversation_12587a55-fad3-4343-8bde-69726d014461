package com.maersk.sd1.sds.dto;

import lombok.Data;
import java.math.BigDecimal;

@Data
public class CargoDocumentDetailsDTO {

    private Integer cargoDocumentId;                // documento_carga_id
    private Integer cargoDocumentDetailId;           // documento_carga_detalle_id
    private Integer containerId;                     // contenedor_id
    private BigDecimal receivedQuantity;             // cantidad_recepcionada
    private Integer manifestedContainerTypeId;    // cat_tipo_contenedor_manifestado_id
    private Integer manifestedSizeCategoryId;     // cat_tamano_manifestado_id
    private Boolean isActive;                        // activo
    private Integer bookingDetailId;                 // booking_detalle_id
    private Integer doNotTouch;                      // no_tocar

}

