package com.maersk.sd1.sds.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class BookingProcessArchiveInputDTO {

    private Integer ediCoparnId;
    private Integer businessUnitId;
    private Integer subBusinessUnitId;
    private Integer vesselProgrammingDetailId;
    private String booking;
    private Integer containerDimensionId;
    private Integer containerTypeId;
    private Integer reservedQuantity;
    private Integer secondaryContainerDimensionId;
    private Integer secondaryContainerTypeId;
    private Integer secondaryReservedQuantity;
    private Integer clientId;
    private String clientRS;
    private String productGroupDescription;
    private Integer productId;
    private Integer portOfLoadingId;
    private Integer portOfDestinationId;
    private Integer portOfDischargeId;
    private String temperature;
    private Integer imoId;
    private Integer lineBkId;
    private Integer grossWeightEdi;
    private Integer secondaryGrossWeightEdi;
    private Boolean coldTreatment;
    private Boolean controlledAtmosphere;
    private Integer userRegistrationId;
    private String paramSequenceDetails;

}
