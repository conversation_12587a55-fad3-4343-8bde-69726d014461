package com.maersk.sd1.sds.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.time.LocalDate;

@Data
public class DepositListInput {

    @Data
    public static class Input {

        @JsonProperty("deposito_id")
        private Integer depositoId;

        @JsonProperty("unidad_negocio_id")
        private Integer unidadNegocioId;

        @JsonProperty("codigo_deposito")
        @Size(max = 10)
        private String codigoDeposito;

        @JsonProperty("nombre_deposito")
        @Size(max = 100)
        private String nombreDeposito;

        @JsonProperty("direccion_deposito")
        @Size(max = 250)
        private String direccionDeposito;

        @JsonProperty("activo")
        private Boolean activo;

        @JsonProperty("fecha_registro_min")
        private LocalDate fechaRegistroMin;

        @JsonProperty("fecha_registro_max")
        private LocalDate fechaRegistroMax;

        @JsonProperty("fecha_modificacion_min")
        private LocalDate fechaModificacionMin;

        @JsonProperty("fecha_modificacion_max")
        private LocalDate fechaModificacionMax;

        @JsonProperty("sub_unidad_negocio_id")
        private Integer subUnidadNegocioId;

        @JsonProperty("deposito_default")
        private Boolean depositoDefault;

        @JsonProperty("cat_codigo_aduana_id")
        private Integer catCodigoAduanaId;

        @JsonProperty("cat_clase_operador_aduana_id")
        private Integer catClaseOperadorAduanaId;

        @JsonProperty("page")
        @Min(value = 1, message = "page must be at least 1")
        private Integer page;

        @JsonProperty("size")
        @Min(value = 1, message = "size must be at least 1")
        private Integer size;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("SDS")
        private Prefix prefix;
    }
}
