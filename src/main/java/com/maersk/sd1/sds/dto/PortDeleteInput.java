package com.maersk.sd1.sds.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class PortDeleteInput {

    @Data
    public static class Input {

        @JsonProperty("port_id")
        @NotNull(message = "port_id cannot be null")
        private Integer portId;

        @JsonProperty("user_modification_id")
        @NotNull(message = "user_modification_id cannot be null")
        private Integer userModificationId;

        @JsonProperty("language_id")
        @NotNull(message = "language_id cannot be null")
        private Integer languageId;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("SDS")
        private Prefix prefix;
    }
}
