package com.maersk.sd1.sds.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.experimental.UtilityClass;

@UtilityClass
public class VesselProgrammingPortDeleteInput {

    @Data
    public static class Input {

        @JsonProperty("programacion_nave_puerto_id")
        @NotNull(message = "programacion_nave_puerto_id cannot be null")
        private Integer vesselProgrammingPortId;

        @JsonProperty("usuario_registro_id")
        @NotNull(message = "usuario_registro_id cannot be null")
        private Long userRegistrationId;

        @JsonProperty("idioma_id")
        @NotNull(message = "idioma_id cannot be null")
        private Integer languageId;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("SDS")
        private Prefix prefix;
    }
}
