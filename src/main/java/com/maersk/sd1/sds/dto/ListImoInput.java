package com.maersk.sd1.sds.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

public class ListImoInput {
    @Data
    public static class Input {
        @JsonProperty("imoId")
        private Integer imoId;

        @JsonProperty("imoCode")
        private String imoCode;

        @JsonProperty("description")
        private String description;

        @JsonProperty("detail")
        private String detail;

        @JsonProperty("isActive")
        private Boolean isActive;

        @JsonProperty("registrationDateMin")
        private String registrationDateMin;

        @JsonProperty("registrationDateMax")
        private String registrationDateMax;

        @JsonProperty("modificationDateMin")
        private String modificationDateMin;

        @JsonProperty("modificationDateMax")
        private String modificationDateMax;

        @JsonProperty("page")
        private Integer page;

        @JsonProperty("size")
        private Integer size;

        @JsonProperty("languageId")
        private Integer languageId;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private ListImoInput.Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("SDS")
        private ListImoInput.Prefix prefix;
    }
}
