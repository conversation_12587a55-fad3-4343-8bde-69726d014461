package com.maersk.sd1.sds.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.time.LocalDate;

/**
 * Input DTO mirroring the fields from the stored procedure parameters.
 * Includes pagination fields as well.
 * Uses the prefix and root structure similar to the sample Input DTO.
 */
public class BookingEdiSettingListInputDTO {

    @Data
    public static class Input {
        @JsonProperty("seteo_edi_coparn_id")
        private Integer seteoEdiCoparnId;

        @JsonProperty("linea_naviera_id")
        private Integer lineaNavieraId;

        @JsonProperty("cat_canal_recepcion_coparn_id")
        private Long catCanalRecepcionCoparnId;

        @JsonProperty("cat_modo_procesar_coparn_id")
        private Long catModoProcesarCoparnId;

        @JsonProperty("edi_coparn_descripcion")
        @Size(max = 250)
        private String ediCoparnDescripcion;

        @JsonProperty("azure_id")
        @Size(max = 100)
        private String azureId;

        @JsonProperty("sftp_coparn_id")
        @Size(max = 100)
        private String sftpCoparnId;

        @JsonProperty("ftp_coparn_id")
        @Size(max = 100)
        private String ftpCoparnId;

        @JsonProperty("carpeta_coparn_ruta")
        @Size(max = 200)
        private String carpetaCoparnRuta;

        @JsonProperty("extension_archivo_descargar")
        @Size(max = 10)
        private String extensionArchivoDescargar;

        @JsonProperty("ruta_mover_edi")
        @Size(max = 200)
        private String rutaMoverEdi;

        @JsonProperty("permitir_crear_prog_nave_automatico")
        private Boolean permitirCrearProgNaveAutomatico;

        @JsonProperty("permitir_crear_cliente_automatico")
        private Boolean permitirCrearClienteAutomatico;

        @JsonProperty("es_historico")
        private Boolean esHistorico;

        @JsonProperty("fecha_debaja_min")
        private LocalDate fechaDeBajaMin;

        @JsonProperty("fecha_debaja_max")
        private LocalDate fechaDeBajaMax;

        @JsonProperty("motivo_debaja")
        @Size(max = 200)
        private String motivoDeBaja;

        @JsonProperty("activo")
        private Boolean activo;

        @JsonProperty("fecha_registro_min")
        private LocalDate fechaRegistroMin;

        @JsonProperty("fecha_registro_max")
        private LocalDate fechaRegistroMax;

        @JsonProperty("fecha_modificacion_min")
        private LocalDate fechaModificacionMin;

        @JsonProperty("fecha_modificacion_max")
        private LocalDate fechaModificacionMax;

        @JsonProperty("unidad_negocio_id")
        private Long unidadNegocioId;

        @JsonProperty("page")
        private Integer page;

        @JsonProperty("size")
        private Integer size;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("SDS")
        private Prefix prefix;
    }
}

