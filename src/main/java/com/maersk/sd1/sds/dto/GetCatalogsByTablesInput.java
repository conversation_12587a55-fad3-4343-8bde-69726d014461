package com.maersk.sd1.sds.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import java.math.BigDecimal;

@Data
public class GetCatalogsByTablesInput {

    @Data
    public static class Input {

        @JsonProperty("business_unit_id")
        @NotNull(message = "business_unit_id cannot be null")
        private BigDecimal businessUnitId;

        @JsonProperty("sub_business_unit_id")
        @NotNull(message = "sub_business_unit_id cannot be null")
        private BigDecimal subBusinessUnitId;

        @JsonProperty("language_id")
        private Integer languageId = 1;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("SDS")
        private Prefix prefix;
    }
}
