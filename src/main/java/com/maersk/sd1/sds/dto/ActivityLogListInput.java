package com.maersk.sd1.sds.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

public class ActivityLogListInput {

    @Data
    public static class Input {
        @JsonProperty("activity_log_id")
        private Integer activityLogId;

        @JsonProperty("activity_alias")
        private String activityAlias;

        @JsonProperty("sub_business_unit_id")
        private Integer subBusinessUnitId;

        @JsonProperty("eir_number")
        private Integer eirNumber;

        @JsonProperty("container_number")
        private String containerNumber;

        @JsonProperty("chassis_number")
        private String chassisNumber;

        @JsonProperty("cat_status_id")
        private Integer catStatusId;

        @JsonProperty("data_input")
        private String dataInput;

        @JsonProperty("data_output")
        private String dataOutput;

        @JsonProperty("user_registration")
        private String userRegistration;

        @JsonProperty("user_modification")
        private String userModification;

        @JsonProperty("current_user_id")
        @NotNull(message = "current_user_id cannot be null")
        private Integer currentUserId;

        @JsonProperty("page")
        private Integer page;

        @JsonProperty("size")
        private Integer size;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("SDS")
        private Prefix prefix;
    }
}
