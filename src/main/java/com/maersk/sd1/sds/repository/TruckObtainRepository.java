package com.maersk.sd1.sds.repository;

import com.maersk.sd1.common.model.Truck;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.Optional;

public interface TruckObtainRepository extends JpaRepository<Truck, Integer> {

    @Query("SELECT t FROM Truck t "
            + " JOIN FETCH t.transportCompany c "
            + " WHERE t.id = :id")
    Optional<Truck> findByIdWithCompany(@Param("id") Integer id);
}
