package com.maersk.sd1.ges.controller;


import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.ges.dto.EmailTemplateListInput;
import com.maersk.sd1.ges.dto.EmailTemplateListOutput;
import com.maersk.sd1.ges.service.EmailTemplateService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Controller to handle requests that replicate the logic of
 * [ges].[email_plantilla_listar].
 */

@RequiredArgsConstructor
@RestController
@RequestMapping("moduleadm/ModuleADM/module/adm/ADMEmailPlantillaService")
public class EmailTemplateController {

    private static final Logger logger = LogManager.getLogger(EmailTemplateController.class);

    private final EmailTemplateService emailTemplateService;

    /**
     * POST endpoint that retrieves email templates by filters and pagination.
     * @param request nested root/prefix/input structure.
     * @return ResponseEntity with the output structure.
     */
    @PostMapping("/gesemailPlantillaListar")
    public ResponseEntity<ResponseController<EmailTemplateListOutput>> listEmailTemplates(
            @RequestBody @Valid EmailTemplateListInput.Root request) {
        try {
            EmailTemplateListInput.Input input = request.getPrefix().getInput();
            EmailTemplateListOutput result = emailTemplateService.listEmailTemplates(input);

            return ResponseEntity.ok(new ResponseController<>(result));
        } catch (Exception e) {
            logger.error("An error occurred while processing the listEmailTemplates request.", e);
            EmailTemplateListOutput errorOutput = new EmailTemplateListOutput();
            errorOutput.setRespEstado(0);
            errorOutput.setRespMensaje(e.toString());
            errorOutput.setTotalRegistros(0L);
            return ResponseEntity.status(500).body(new ResponseController<>(errorOutput));
        }
    }
}

