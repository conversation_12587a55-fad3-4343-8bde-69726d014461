package com.maersk.sd1.ges.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.ges.dto.RolEmpresaUnidadNegocioInput;
import com.maersk.sd1.ges.dto.RolEmpresaUnidadNegocioOutput;
import com.maersk.sd1.ges.service.RolEmpresaUnidadNegocioService;
import jakarta.validation.Valid;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("moduleadm/ModuleADM/module/adm/ADMEmpresaService")
public class RolEmpresaUnidadNegocioController {

    private static final Logger logger = LogManager.getLogger(RolEmpresaUnidadNegocioController.class);

    private final RolEmpresaUnidadNegocioService service;

    public RolEmpresaUnidadNegocioController(RolEmpresaUnidadNegocioService service) {
        this.service = service;
    }

    @PostMapping("/gesrolEmpresaUnidadNegocioListar")
    public ResponseEntity<ResponseController<RolEmpresaUnidadNegocioOutput>> listarRolEmpresaUnidadNegocio(
            @Valid @RequestBody RolEmpresaUnidadNegocioInput.Root request) {
        try {
            Integer languageId = request.getPrefix().getInput().getLanguageId();
            RolEmpresaUnidadNegocioOutput output = service.listarRolEmpresaUnidadNegocio(languageId);
            return ResponseEntity.ok(new ResponseController<>(output));
        } catch (Exception e) {
            logger.error("An error occurred while listing RolEmpresaUnidadNegocio", e);
            RolEmpresaUnidadNegocioOutput errorOutput = new RolEmpresaUnidadNegocioOutput();
            errorOutput.setRespEstado(0);
            errorOutput.setRespMensaje(e.getMessage());
            return ResponseEntity.status(500).body(new ResponseController<>(errorOutput));
        }
    }
}