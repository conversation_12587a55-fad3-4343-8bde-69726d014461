package com.maersk.sd1.ges.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.ges.dto.PersonDeleteInput;
import com.maersk.sd1.ges.dto.PersonDeleteOutput;
import com.maersk.sd1.ges.service.PersonDeleteService;
import jakarta.validation.Valid;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/ModuleADM/module/adm/ADMPersonaServiceImp")
public class PersonDeleteController {

    private static final Logger logger = LogManager.getLogger(PersonDeleteController.class.getName());

    private final PersonDeleteService personDeleteService;

    @Autowired
    public PersonDeleteController(PersonDeleteService personDeleteService) {
        this.personDeleteService = personDeleteService;
    }

    @PostMapping("/gespersonaEliminar")
    public ResponseEntity<ResponseController<PersonDeleteOutput>> sdgPersonDelete(@RequestBody @Valid PersonDeleteInput.Root request) {
        try {
            PersonDeleteInput.Input input = request.getPrefix().getInput();
            PersonDeleteOutput output = personDeleteService.deletePerson(
                    input.getPersonId(),
                    input.getCompanyId(),
                    input.getBusinessUnitId(),
                    input.getUserModificationId()
            );
            return ResponseEntity.ok(new ResponseController<>(output));
        } catch (Exception e) {
            logger.error("An error occurred while processing deletePerson request.", e);
            PersonDeleteOutput output = new PersonDeleteOutput();
            output.setRespEstado(0);
            output.setRespMensaje(e.toString());
            return ResponseEntity.status(500).body(new ResponseController<>(output));
        }
    }
}