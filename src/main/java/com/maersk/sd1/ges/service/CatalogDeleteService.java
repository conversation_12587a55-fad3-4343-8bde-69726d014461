package com.maersk.sd1.ges.service;

import com.maersk.sd1.ges.dto.CatalogDeleteOutput;
import com.maersk.sd1.common.repository.CatalogLanguageRepository;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@Transactional
public class CatalogDeleteService {

    private static final Logger logger = LogManager.getLogger(CatalogDeleteService.class);

    private final CatalogLanguageRepository catalogLanguageRepository;

    @Autowired
    public CatalogDeleteService(CatalogLanguageRepository catalogLanguageRepository) {
        this.catalogLanguageRepository = catalogLanguageRepository;
    }

    public CatalogDeleteOutput deleteCatalog(Integer catalogId) {
        CatalogDeleteOutput output = new CatalogDeleteOutput();
        try {

            catalogLanguageRepository.deleteByCatalogId(catalogId);

            catalogLanguageRepository.deleteCatalogLanguage(catalogId);

            output.setRespEstado(1);
            output.setRespMensaje("Catalogo eliminado correctamente");
        } catch (Exception e) {
            logger.error("Error deleting catalog:", e);
            output.setRespEstado(0);
            output.setRespMensaje(e.getMessage());
        }
        return output;
    }
}
