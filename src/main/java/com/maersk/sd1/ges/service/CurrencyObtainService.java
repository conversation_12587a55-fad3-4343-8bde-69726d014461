package com.maersk.sd1.ges.service;

import com.maersk.sd1.ges.dto.CurrencyObtainInput;
import com.maersk.sd1.ges.dto.CurrencyObtainOutput;
import com.maersk.sd1.ges.repository.CurrencyObtainReposiory;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class CurrencyObtainService {

    private static final Logger logger = LogManager.getLogger(CurrencyObtainService.class);

    private final CurrencyObtainReposiory monedaObtenerRepository;

    @Autowired
    public CurrencyObtainService(CurrencyObtainReposiory monedaObtenerRepository) {
        this.monedaObtenerRepository = monedaObtenerRepository;
    }

    @Transactional(readOnly = true)
    public CurrencyObtainOutput obtenerMoneda(CurrencyObtainInput.Root request) {
        CurrencyObtainOutput output = new CurrencyObtainOutput();
        try {
            Integer monedaId = request.getPrefix().getInput().getMonedaId();
            if (monedaId == null) {
                // Null check (though also enforced by @NotNull in Input DTO)
                logger.error("moneda_id is null");
                output.setRespEstado(0);
                output.setRespMensaje("moneda_id cannot be null");
                return output;
            }

            var optionalProjection = monedaObtenerRepository.findMonedaByMonedaId(monedaId);
            if (optionalProjection.isPresent()) {
                var projection = optionalProjection.get();
                output.setRespEstado(1);
                output.setRespMensaje("Success");
                output.setMonedaId(projection.getId());
                output.setNombre(projection.getName());
                output.setAbreviatura(projection.getAbbreviation());
                output.setSimbolo(projection.getSymbol());
                output.setEstado(projection.getStatus());
                output.setFechaRegistro(projection.getRegistrationDate());
                output.setFechaModificacion(projection.getModificationDate());
                output.setSeparadorMiles(projection.getSeparatorMiles());
                output.setSeparadorDecimales(projection.getSeparatorDecimals());
                output.setPrecision(projection.getPrecision());
                output.setIcu(projection.getIcu());
            } else {
                logger.info("No currency found with id: {}", monedaId);
                output.setRespEstado(0);
                output.setRespMensaje("No currency record found");
            }
        } catch (Exception e) {
            logger.error("Error while obtaining currency", e);
            output.setRespEstado(0);
            output.setRespMensaje(e.getMessage());
        }
        return output;
    }
}
