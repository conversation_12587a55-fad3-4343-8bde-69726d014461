package com.maersk.sd1.ges.service;

import com.maersk.sd1.ges.dto.SystemRuleDeleteOutput;
import com.maersk.sd1.common.repository.SystemRuleRepository;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

@Service
public class SystemRuleDeleteService {

    private static final Logger logger = LogManager.getLogger(SystemRuleDeleteService.class.getName());

    private final SystemRuleRepository systemRuleRepository;

    @Autowired
    public SystemRuleDeleteService(SystemRuleRepository systemRuleRepository) {
        this.systemRuleRepository = systemRuleRepository;
    }

    @Transactional
    public SystemRuleDeleteOutput deleteSystemRule(Integer ruleId, Integer userModificationId) {
        SystemRuleDeleteOutput output = new SystemRuleDeleteOutput();
        try {
            systemRuleRepository.deactivateSystemRule(ruleId, userModificationId, LocalDateTime.now());
            output.setRespEstado(1);
            output.setRespMensaje("Registro eliminado correctamente");
        } catch (Exception e) {
            logger.error("Error while deleting system rule", e);
            output.setRespEstado(0);
            output.setRespMensaje(e.getMessage());
        }
        return output;
    }
}
