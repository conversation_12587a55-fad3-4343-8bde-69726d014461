package com.maersk.sd1.ges.service;

import com.maersk.sd1.common.repository.BusinessUnitConfigRepository;
import com.maersk.sd1.common.model.BusinessUnitConfig;
import com.maersk.sd1.ges.dto.DetailDTO;
import com.maersk.sd1.ges.dto.BusinessPersonalListInput;
import com.maersk.sd1.ges.dto.BusinessPersonalListOutput;
import com.maersk.sd1.ges.repository.BusinessPersonalListRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.time.format.DateTimeParseException;
import java.util.List;

@Service
public class BusinessPersonalListService {

    private static final Logger logger = LogManager.getLogger(BusinessPersonalListService.class);

    private final BusinessPersonalListRepository empresaPersonaListarRepository;
    private final BusinessUnitConfigRepository businessUnitConfigRepository;

    @Autowired
    public BusinessPersonalListService(BusinessPersonalListRepository empresaPersonaListarRepository, BusinessUnitConfigRepository businessUnitConfigRepository) {
        this.empresaPersonaListarRepository = empresaPersonaListarRepository;
        this.businessUnitConfigRepository = businessUnitConfigRepository;
    }

    private static final Integer P_CAT_ZONA_HORARIA = 20251;

    @Transactional(readOnly = true)
    public BusinessPersonalListOutput listarEmpresaPersona(BusinessPersonalListInput.Input input) {
        BusinessPersonalListOutput output = new BusinessPersonalListOutput();
        try {
            Integer unidadNegocioId = input.getBusinessUnitId();
            BusinessUnitConfig businessUnitConfig = null;
            if (unidadNegocioId != null) {
                businessUnitConfig = businessUnitConfigRepository.findActiveConfigByBusinessUnitIdAndType(unidadNegocioId, P_CAT_ZONA_HORARIA);
            }
            int zonaHorariaGmt = 0;
            if (businessUnitConfig != null) {
                try {
                    zonaHorariaGmt = Integer.parseInt(businessUnitConfig.getValue());
                } catch (NumberFormatException ex) {
                    logger.error("Could not parse time zone offset from BusinessUnitConfig.", ex);
                }
            }

            Long totalRecords = empresaPersonaListarRepository.countByFilters(
                    input.getCompanyId(),
                    input.getPersonId(),
                    input.getActive()
            );

            output.setTotalRegistros(totalRecords);

            List<DetailDTO> details = empresaPersonaListarRepository.findByFilters(
                    input.getCompanyId(),
                    input.getPersonId(),
                    input.getActive()
            );


            for (DetailDTO detail : details) {
                if (detail.getFechaRegistro() != null) {
                    detail.setFechaRegistro(detail.getFechaRegistro().plusHours(zonaHorariaGmt));
                }
                if (detail.getFechaModificacion() != null) {
                    detail.setFechaModificacion(detail.getFechaModificacion().plusHours(zonaHorariaGmt));
                }
            }




            output.setDetalle(details);
            output.setRespEstado(1);
            output.setRespMensaje("Success");
        } catch (DateTimeParseException e) {
            logger.error("Date parse error in listarEmpresaPersona", e);
            output.setRespEstado(0);
            output.setRespMensaje(e.getMessage());
        } catch (Exception ex) {
            logger.error("Unexpected error in listarEmpresaPersona", ex);
            output.setRespEstado(0);
            output.setRespMensaje(ex.getMessage());
        }
        return output;
    }
}
