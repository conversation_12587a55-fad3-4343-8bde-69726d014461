package com.maersk.sd1.ges.service;

import com.maersk.sd1.common.repository.BusinessUnitRepository;
import com.maersk.sd1.common.repository.MenuConfigRepository;
import com.maersk.sd1.common.repository.MenuRepository;
import com.maersk.sd1.ges.dto.InitializeOutput;
import com.maersk.sd1.common.model.Menu;
import com.maersk.sd1.common.model.BusinessUnit;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
public class InitializeService {

    private static final Logger logger = LogManager.getLogger(InitializeService.class);

    private final MenuRepository menuRepository;
    private final BusinessUnitRepository businessUnitRepository;
    private final MenuConfigRepository menuConfigRepository;

    @Autowired
    public InitializeService(MenuRepository menuRepository, BusinessUnitRepository businessUnitRepository, MenuConfigRepository menuConfigRepository) {
        this.menuRepository = menuRepository;
        this.businessUnitRepository = businessUnitRepository;
        this.menuConfigRepository = menuConfigRepository;
    }

    @Transactional(readOnly = true)
    public InitializeOutput initializeData(Integer unidadNegocioId, Integer usuarioId) {
        logger.info("Starting initializeData with unidadNegocioId={}, usuarioId={}", unidadNegocioId, usuarioId);
        InitializeOutput output = new InitializeOutput();
        try {
            // 1) List of projects
            List<Menu> menus = menuRepository.findByParentMenuIsNullAndBaseMenuIsNullAndStatusTrueOrderByTitleAsc();
            List<InitializeOutput.ProjectDto> projectDtoList = menus.stream().map(menu -> {
                InitializeOutput.ProjectDto p = new InitializeOutput.ProjectDto();
                p.setMenuId(menu.getId());
                p.setTitle(menu.getTitle());
                return p;
            }).toList();

            // 2) List of Business Units
            List<BusinessUnit> businessUnits = businessUnitRepository.findByStatusTrueAndParentBusinessUnitIsNullOrderByNameAsc();
            List<InitializeOutput.BusinessUnitDto> businessUnitDtoList = businessUnits.stream().map(bu -> {
                InitializeOutput.BusinessUnitDto dto = new InitializeOutput.BusinessUnitDto();
                dto.setId(bu.getId());
                dto.setName(bu.getName());
                dto.setStatus(bu.getStatus());
                return dto;
            }).toList();

            // 3) List of configurations per project
            List<InitializeOutput.ConfigurationDto> configurationList = menuConfigRepository.findDistinctConfigurations(usuarioId);

            // Prepare final output
            output.setProjects(projectDtoList);
            output.setBusinessUnits(businessUnitDtoList);
            output.setConfigurations(configurationList);

            // We can set a simple success state
            output.setRespEstado(1);
            output.setRespMensaje("Successful initialization");
        } catch (Exception e) {
            logger.error("Error in initializeData", e);
            output.setRespEstado(0);
            output.setRespMensaje(e.getMessage());
        }
        return output;
    }
}
