package com.maersk.sd1.ges.service;

import com.maersk.sd1.common.model.*;

import com.maersk.sd1.common.repository.*;
import com.maersk.sd1.ges.dto.EditCompanyInputDTO;
import com.maersk.sd1.ges.dto.EditCompanyOutputDTO;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Service
public class CompanyEditService {

    private static final Logger logger = LogManager.getLogger(CompanyEditService.class);

    private final CompanyRepository companyRepository;

    private final CompanyContactRepository contactRepository;

    private final CompanyAddressRepository addressRepository;

    private final CompanyRoleRepository roleRepository;

    private final CompanyConfigRepository configRepository;

    private final CompanyBusinessLineRepository businessLineRepository;

    private final BusinessUnitRepository businessUnitRepository;

    @Autowired
    public CompanyEditService(CompanyRepository companyRepository,
                              CompanyContactRepository contactRepository,
                              CompanyAddressRepository addressRepository,
                              CompanyRoleRepository roleRepository,
                              CompanyConfigRepository configRepository,
                              CompanyBusinessLineRepository businessLineRepository,
                              BusinessUnitRepository businessUnitRepository)
    {
        this.companyRepository = companyRepository;
        this.contactRepository = contactRepository;
        this.addressRepository = addressRepository;
        this.roleRepository =  roleRepository;
        this.configRepository = configRepository;
        this.businessLineRepository = businessLineRepository;
        this.businessUnitRepository = businessUnitRepository;

    }
    @Transactional
    public EditCompanyOutputDTO editCompany(EditCompanyInputDTO.Input input) {
        EditCompanyOutputDTO output = new EditCompanyOutputDTO();
        try {
            logger.info("Starting company edit process for companyId: {}", input.getCompanyId());

            // 1) Find existing Company
            Optional<Company> optCompany = companyRepository.findById(input.getCompanyId());
            if (optCompany.isEmpty()) {
                throw new RuntimeException("Company not found");
            }
            Company company = optCompany.get();

            // 2) Update Company fields.
            // Convert the integer 1/0 status to boolean.
            Boolean newStatus = input.getStatus() != null && input.getStatus().equals(1);
            Boolean newSuspended = input.getSuspended() != null && input.getSuspended().equals(1);

            company.setDocument(input.getDocument());
            company.setLegalName(input.getLegalName());
            company.setCommercialName(input.getCommercialName());
            company.setAddress(input.getAddress());
            company.setLongitude(input.getLongitude());
            company.setLatitude(input.getLatitude());
            // userModificationId -> modificationUser (we only store the ID in actual scenario we load the user, for now we mock)
            User modificationUser = new User();
            modificationUser.setId(input.getUserModificationId());
            company.setModificationUser(modificationUser);
            company.setModificationDate(LocalDateTime.now());

            if (input.getBusinessUnitId() != null) {
                Optional<BusinessUnit> existingBusinessUnit = businessUnitRepository.findById(input.getBusinessUnitId());
                if (existingBusinessUnit.isPresent()) {
                    BusinessUnit businessUnit = existingBusinessUnit.get();
                    company.setBusinessUnit(businessUnit);  // Set the existing BusinessUnit on the Company
                }
            }
            // catDocumentTypeId -> catDocumentType
            if (input.getCatDocumentTypeId() != null) {
                Catalog docType = new Catalog();
                docType.setId(input.getCatDocumentTypeId());
                company.setCatDocumentType(docType);
            }
            company.setPhone(input.getPhone());
            company.setMail(input.getMail());
            company.setAbbreviation(input.getAbbreviation());
            company.setStatus(newStatus);
            company.setSuspended(newSuspended);

            // 3) Save updated company.
            companyRepository.save(company);

            // 4) Contacts: delete existing => insert new
            contactRepository.deleteByCompanyId(company.getId());
            if (input.getContacts() != null && !input.getContacts().isEmpty()) {
                List<CompanyContact> newContacts = new ArrayList<>();
                for (EditCompanyInputDTO.CompanyContactInput contactInput : input.getContacts()) {
                    CompanyContact cc = new CompanyContact();
                    cc.setId(contactInput.getCompanyContactId() == null ? 0 : contactInput.getCompanyContactId());
                    cc.setCompany(company);
                    cc.setNames(contactInput.getNames());
                    cc.setFirstLastName(contactInput.getFirstLastName());
                    cc.setSecondLastName(contactInput.getSecondLastName());
                    cc.setPhone(contactInput.getPhone());
                    cc.setMail(contactInput.getMail());

                    if (contactInput.getContactTypeId() != null) {
                        Catalog cat = new Catalog();
                        cat.setId(contactInput.getContactTypeId());
                        cc.setCatContactType(cat);
                    }
                    newContacts.add(cc);
                }
                contactRepository.saveAll(newContacts);
            }

            // 5) Addresses: action-based upsert
            if (input.getAddresses() != null) {
                for (EditCompanyInputDTO.CompanyAddressInput addrInput : input.getAddresses()) {
                    if ("U".equalsIgnoreCase(addrInput.getAction())) {
                        // update
                        if (addrInput.getCompanyAddressId() == null) {
                            logger.warn("Address with action=U but no ID, skipping...");
                            continue;
                        }
                        Optional<CompanyAddress> optAddress = addressRepository.findById(addrInput.getCompanyAddressId());
                        if (optAddress.isPresent()) {
                            CompanyAddress address = optAddress.get();
                            address.setEmpresa(company);
                            address.setAddress(addrInput.getAddress());
                            address.setPhone(addrInput.getPhone());
                            address.setMail(addrInput.getMail());
                            address.setLongitude(addrInput.getLongitude());
                            address.setLatitude(addrInput.getLatitude());
                            address.setName(addrInput.getName());
                            if (addrInput.getAddressTypeId() != null) {
                                Catalog cat = new Catalog();
                                cat.setId(addrInput.getAddressTypeId());
                                address.setAddressType(cat);
                            }
                            if (addrInput.getUbigeoId() != null) {
                                Ubigeo ubi = new Ubigeo();
                                ubi.setId(addrInput.getUbigeoId());
                                address.setUbigeo(ubi);
                            }
                            addressRepository.save(address);
                        }
                    } else if ("I".equalsIgnoreCase(addrInput.getAction())) {
                        // insert
                        CompanyAddress address = new CompanyAddress();
                        address.setId(addrInput.getCompanyAddressId() == null ? 0 : addrInput.getCompanyAddressId());
                        address.setEmpresa(company);
                        address.setAddress(addrInput.getAddress());
                        address.setPhone(addrInput.getPhone());
                        address.setMail(addrInput.getMail());
                        address.setLongitude(addrInput.getLongitude());
                        address.setLatitude(addrInput.getLatitude());
                        address.setName(addrInput.getName());
                        if (addrInput.getAddressTypeId() != null) {
                            Catalog cat = new Catalog();
                            cat.setId(addrInput.getAddressTypeId());
                            address.setAddressType(cat);
                        }
                        if (addrInput.getUbigeoId() != null) {
                            Ubigeo ubi = new Ubigeo();
                            ubi.setId(addrInput.getUbigeoId());
                            address.setUbigeo(ubi);
                        }
                        addressRepository.save(address);
                    }
                    else if ("D".equalsIgnoreCase(addrInput.getAction()) && addrInput.getCompanyAddressId() != null) {
                        // delete
                        addressRepository.deleteById(addrInput.getCompanyAddressId());
                    }

                }
            }

            // 6) Roles: delete existing => insert new
            roleRepository.deleteByCompanyId(company.getId());
            if (input.getRoles() != null && !input.getRoles().isEmpty()) {
                List<CompanyRole> newRoles = new ArrayList<>();
                for (EditCompanyInputDTO.CompanyRoleInput roleInput : input.getRoles()) {
                    CompanyRole role = new CompanyRole();
                    role.setId(0); // just for example, or a sequence-based approach.
                    role.setCompany(company);
                    Catalog catRole = new Catalog();
                    catRole.setId(roleInput.getRoleTypeId());
                    role.setCatRoleType(catRole);
                    newRoles.add(role);
                }
                roleRepository.saveAll(newRoles);
            }

            // 7) Config: delete existing => insert new
            configRepository.deleteByCompanyId(company.getId());
            if (input.getConfigurations() != null && !input.getConfigurations().isEmpty()) {
                List<CompanyConfig> configs = new ArrayList<>();
                for (EditCompanyInputDTO.CompanyConfigInput cfg : input.getConfigurations()) {
                    CompanyConfig cc = new CompanyConfig();
                    CompanyConfigId ccId = new CompanyConfigId();
                    ccId.setCompanyId(company.getId());
                    ccId.setTypeConfigurationId(cfg.getConfigurationTypeId());
                    cc.setId(ccId);
                    cc.setCompany(company);
                    Catalog catCfgType = new Catalog();
                    catCfgType.setId(cfg.getConfigurationTypeId());
                    cc.setCatConfigurationType(catCfgType);
                    cc.setValue(cfg.getValue());
                    configs.add(cc);
                }
                configRepository.saveAll(configs);
            }

            // 8) Business lines: delete all => insert from input
            businessLineRepository.deleteByCompanyId(company.getId());
            if (input.getBusinessLineIds() != null && !input.getBusinessLineIds().isEmpty()) {
                List<CompanyBusinessLine> lines = new ArrayList<>();
                for (Integer lineId : input.getBusinessLineIds()) {
                    CompanyBusinessLine cbl = new CompanyBusinessLine();
                    CompanyTurnBusinessId cblId = new CompanyTurnBusinessId();
                    cblId.setCompanyId(company.getId());
                    cblId.setTurnBusinessId(lineId);
                    cbl.setId(cblId);
                    cbl.setCompany(company);
                    BusinessLine bl = new BusinessLine();
                    bl.setId(lineId);
                    cbl.setBusinessLine(bl);
                    lines.add(cbl);
                }
                businessLineRepository.saveAll(lines);
            }

            // If all is successful:
            output.setRespEstado(1);
            output.setRespMensaje("Company edited correctly");
            logger.info("Company edit process completed successfully for companyId: {}", company.getId());
        } catch (Exception e) {
            logger.error("Error while editing company:", e);
            output.setRespEstado(0);
            output.setRespMensaje(e.getMessage());
        }
        return output;
    }
}
