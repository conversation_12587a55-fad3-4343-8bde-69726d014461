package com.maersk.sd1.ges.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

public class ReglaSistemaObtenerInput {

    @Data
    public static class Input {
        @JsonProperty("regla_sistema_id")
        @NotNull
        private Integer reglaSistemaId;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("GES")
        private Prefix prefix;
    }
}