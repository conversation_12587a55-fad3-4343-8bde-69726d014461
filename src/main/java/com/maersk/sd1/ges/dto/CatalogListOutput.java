package com.maersk.sd1.ges.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import java.util.List;

@Data
public class CatalogListOutput {

    @JsonProperty("resp_estado")
    private Integer respEstado;

    @JsonProperty("resp_mensaje")
    private String respMensaje;

    @JsonProperty("total_items")
    private Integer totalItems;

    @JsonProperty("catalog_list")
    private List<CatalogItem> catalogList;

    @Data
    public static class CatalogItem {
        @JsonProperty("catalogo_id")
        private Integer catalogId;

        @JsonProperty("descripcion")
        private String description;

        @JsonProperty("descricion_larga")
        private String longDescription;

        @JsonProperty("estado")
        private Boolean status;

        @JsonProperty("codigo")
        private String code;

        @JsonProperty("alias")
        private String alias;
    }
}
