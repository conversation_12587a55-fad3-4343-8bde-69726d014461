package com.maersk.sd1.ges.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import lombok.Data;
import lombok.experimental.UtilityClass;
import java.time.LocalDate;
import java.time.LocalDateTime;

@UtilityClass
public class AutoreportListInput {

    @Data
    public static class Input {
        @JsonProperty("report_id")
        private Integer reportId;

        @JsonProperty("email_template_id")
        private Integer emailTemplateId;

        @JsonProperty("registration_date_min")
        private LocalDate registrationDateMin;

        @JsonProperty("registration_date_max")
        private LocalDate registrationDateMax;

        @JsonProperty("active")
        private Boolean active;

        @JsonProperty("page")
        @NotNull(message = "page cannot be null")
        @Positive(message = "page must be a positive number")
        private Integer page;

        @JsonProperty("size")
        @NotNull(message = "size cannot be null")
        @Positive(message = "size must be a positive number")
        private Integer size;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("GES")
        private Prefix prefix;
    }
}
