package com.maersk.sd1.ges.dto;


import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Output DTO that captures both the total records and
 * the list of resulting EmailTemplate items.
 */
@Data
public class EmailTemplateListOutput {

    @JsonProperty("resp_estado")
    private Integer respEstado;

    @JsonProperty("resp_mensaje")
    private String respMensaje;

    @JsonProperty("total_registros")
    private Long totalRegistros;

    @JsonProperty("templates")
    private List<EmailTemplateItem> templates;

    /**
     * Nested DTO for each EmailTemplate row returned.
     */
    @Data
    public static class EmailTemplateItem {

        @JsonProperty("email_plantilla_id")
        private Integer emailPlantillaId;

        @JsonProperty("email_plantilla_padre_id")
        private Integer emailPlantillaPadreId;

        @JsonProperty("contenido")
        private String content;

        @JsonProperty("contenido_padre")
        private String parentContent;

        @JsonProperty("destinatario")
        private String recipient;

        @JsonProperty("copia")
        private String copy;

        @JsonProperty("copia_oculta")
        private String copyHidden;

        @JsonProperty("titulo")
        private String title;

        @JsonProperty("estado")
        private Boolean status;

        @JsonProperty("fecha_registro")
        private LocalDateTime registrationDate;

        @JsonProperty("fecha_modificacion")
        private LocalDateTime modificationDate;

        @JsonProperty("descripcion")
        private String description;

        @JsonProperty("envio_usuario")
        private Boolean sentUser;

        @JsonProperty("indicador_habilitado")
        private Boolean indicatorEnabled;

        @JsonProperty("menu_proyecto_id")
        private Integer menuProyectoId;

        @JsonProperty("mena_titulo")
        private String menaTitle;

        @JsonProperty("usuario_registro_id")
        private Integer registrationUserId;

        @JsonProperty("usuario_registro_nombres")
        private String registrationUserNames;

        @JsonProperty("usuario_registro_apellidos")
        private String registrationUserLastnames;

        @JsonProperty("id")
        private String id1;
    }
}

