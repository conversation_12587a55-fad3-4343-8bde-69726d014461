package com.maersk.sd1.sdf.dto;

public interface TransportPlanningDTO {
    Integer getTransportPlanningId();   // transport_planning_id
    Integer getCatReceiptReasonFullId(); // cat_receipt_reason_full_id
    Integer getCatCargoDocumentTypeId(); // cat_cargo_document_type_id
    String getReference();           // reference (documento_carga)
    Long getShippingLineId();        // shipping_line_id
    Long getConsigneeCompanyId();    // consignee_company_id
    String getConsigneeCompany();    // consignee_company
    Long getShipperCompanyId();      // shipper_company_id
    String getShipperCompany();      // shipper_company
    String getComments();            // comments
    String getShippingLineName();    // shipping_line_name
}

