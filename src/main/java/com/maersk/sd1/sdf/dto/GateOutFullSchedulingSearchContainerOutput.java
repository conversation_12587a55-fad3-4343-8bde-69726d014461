package com.maersk.sd1.sdf.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class GateOutFullSchedulingSearchContainerOutput {

    @JsonProperty("result_state")
    private Integer resultState;

    @JsonProperty("result_message")
    private String resultMessage;

    // Additional fields from final SELECT in the SP

    @JsonProperty("equipment_number")
    private String equipmentNumber;

    @JsonProperty("iso_code")
    private String isoCode;

    @JsonProperty("operation_type")
    private String operationType;

    @JsonProperty("vessel_voyage")
    private String vesselVoyage;

    @JsonProperty("reference_document")
    private String referenceDocument;

    @JsonProperty("shipping_line")
    private String shippingLine;

    @JsonProperty("shipper_name")
    private String shipperName;

    @JsonProperty("consignee_name")
    private String consigneeName;

    @JsonProperty("seal1")
    private String seal1;

    @JsonProperty("seal2")
    private String seal2;

    @JsonProperty("seal3")
    private String seal3;

    @JsonProperty("seal4")
    private String seal4;

    @JsonProperty("equipment_id")
    private Integer equipmentId;

    @JsonProperty("documento_carga_detalle_id")
    private Integer cargoDocumentDetailId;
}
