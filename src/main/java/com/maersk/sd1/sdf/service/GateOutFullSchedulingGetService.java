package com.maersk.sd1.sdf.service;

import com.maersk.sd1.common.repository.CatalogLanguageRepository;
import com.maersk.sd1.common.repository.TransportPlanningDetailRepository;
import com.maersk.sd1.common.repository.VesselProgrammingContainerRepository;
import com.maersk.sd1.sdf.dto.GateOutFullSchedulingOutput;
import com.maersk.sd1.common.model.TransportPlanningDetail;
import com.maersk.sd1.common.model.VesselProgrammingContainer;
import com.maersk.sd1.common.model.CargoDocument;
import com.maersk.sd1.common.model.CargoDocumentDetail;
import com.maersk.sd1.common.model.Company;
import com.maersk.sd1.common.model.Container;
import com.maersk.sd1.common.model.ShippingLine;
import com.maersk.sd1.common.model.User;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Service
public class GateOutFullSchedulingGetService {

    private static final Logger logger = LogManager.getLogger(GateOutFullSchedulingGetService.class);

    private final TransportPlanningDetailRepository transportPlanningDetailRepository;
    private final VesselProgrammingContainerRepository vesselProgrammingContainerRepository;
    private final CatalogLanguageRepository catalogLanguageRepository;

    // Constructor injection
    public GateOutFullSchedulingGetService(TransportPlanningDetailRepository transportPlanningDetailRepository,
                                           VesselProgrammingContainerRepository vesselProgrammingContainerRepository,
                                           CatalogLanguageRepository catalogLanguageRepository) {
        this.transportPlanningDetailRepository = transportPlanningDetailRepository;
        this.vesselProgrammingContainerRepository = vesselProgrammingContainerRepository;
        this.catalogLanguageRepository = catalogLanguageRepository;
    }

    @Transactional(readOnly = true)
    public List<GateOutFullSchedulingOutput> getGateOutFullScheduling(Integer businessUnitId,
                                                                      Integer subBusinessUnitId,
                                                                      Integer transportPlanningId,
                                                                      Integer languageId) {
        logger.info("Fetching Gate Out Full Scheduling data for transportPlanningId={}, languageId={}",
                transportPlanningId, languageId);

        List<TransportPlanningDetail> details = transportPlanningDetailRepository
                .findByTransportPlanningIdAndActiveTrueOrderById(transportPlanningId);

        List<GateOutFullSchedulingOutput> outputList = new ArrayList<>();

        for (TransportPlanningDetail tpd : details) {
            GateOutFullSchedulingOutput out = new GateOutFullSchedulingOutput();

            populateBasicFields(out, tpd);
            populateCargoDocDetails(out, tpd, languageId);
            populateUserDetails(out, tpd);
            populateTransportCompany(out, tpd);
            populateSeals(out, tpd);

            outputList.add(out);
        }
        return outputList;
    }

    private void populateBasicFields(GateOutFullSchedulingOutput out, TransportPlanningDetail tpd) {
        if (tpd.getTransportPlanning() != null) {
            out.setTransportPlanningId(tpd.getTransportPlanning().getId());
            out.setComments(tpd.getTransportPlanning().getComments());
        }
        out.setTransportPlanningDetailId(tpd.getId());
        out.setEtd(tpd.getTransportDate());

        if (tpd.getCatTrkPlanningState() != null) {
            out.setCatStateTrkPlanningId(tpd.getCatTrkPlanningState().getId());
        }
    }

    private void populateCargoDocDetails(GateOutFullSchedulingOutput out, TransportPlanningDetail tpd, Integer languageId) {
        CargoDocumentDetail dcd = tpd.getCargoDocumentDetail();
        if (dcd != null) {
            CargoDocument dc = dcd.getCargoDocument();
            if (dc != null) {
                populateContainerDetails(out, dcd);
                populateShippingLineDetails(out, dc);
                populateCompanyDetails(out, dc);
                populateReceiptReason(out, dcd, languageId);
                populateDocumentType(out, dc, languageId);
            }
        }
        if (tpd.getCatTrkPlanningState() != null) {
            out.setStateDescription(fetchCatalogTranslationDesc(tpd.getCatTrkPlanningState().getId(), languageId));
        }
    }

    private void populateContainerDetails(GateOutFullSchedulingOutput out, CargoDocumentDetail dcd) {
        Container container = dcd.getContainer();
        if (container != null) {
            out.setContainerId(container.getId());
            out.setContainerNumber(container.getContainerNumber());
            if (container.getIsoCode() != null) {
                out.setIsoCode(container.getIsoCode().getIsoCode());
            }
        }
    }

    private void populateShippingLineDetails(GateOutFullSchedulingOutput out, CargoDocument dc) {
        ShippingLine shippingLine = dc.getShippingLine();
        if (shippingLine != null) {
            out.setShippingLineId(shippingLine.getId());
            out.setShippingLine(shippingLine.getName());
        }
    }

    private void populateCompanyDetails(GateOutFullSchedulingOutput out, CargoDocument dc) {
        Company shipper = dc.getShipperCompany();
        if (shipper != null) {
            out.setShipperId(shipper.getId());
            out.setShipperName(shipper.getLegalName());
        }
        Company consignee = dc.getConsigneeCompany();
        if (consignee != null) {
            out.setConsigneeId(consignee.getId());
            out.setConsigneeName(consignee.getLegalName());
        }
    }

    private void populateReceiptReason(GateOutFullSchedulingOutput out, CargoDocumentDetail dcd, Integer languageId) {
        if (dcd.getCatFullReceiptReason() != null) {
            out.setOperationId(dcd.getCatFullReceiptReason().getId());
            out.setOperationDescription(fetchCatalogTranslationDescLong(dcd.getCatFullReceiptReason().getId(), languageId));
        }
    }

    private void populateDocumentType(GateOutFullSchedulingOutput out, CargoDocument dc, Integer languageId) {
        if (dc.getCatCargoDocumentType() != null) {
            out.setReferenceType(dc.getCatCargoDocumentType().getId());
            String translated = fetchCatalogTranslationDesc(dc.getCatCargoDocumentType().getId(), languageId);
            out.setReferenceDescription(translated + ": " + dc.getCargoDocument());
        }
    }


    private void populateUserDetails(GateOutFullSchedulingOutput out, TransportPlanningDetail tpd) {
        User regUser = tpd.getRegistrationUser();
        if (regUser != null) {
            out.setUserRegistrationId(regUser.getId());
            out.setUserRegistrationName(regUser.getNames());
            String lastNames = formatUserLastName(regUser);
            out.setUserRegistrationLastname(lastNames);
        }

        out.setRegistrationDate(convertToLocalTime(tpd.getRegistrationDate()));

        User modUser = tpd.getModificationUser();
        if (modUser != null) {
            out.setUserModificationId(modUser.getId());
            out.setUserModificationName(modUser.getNames());
            String modLastNames = formatUserLastName(modUser);
            out.setUserModificationLastname(modLastNames);
        }

        out.setModificationDate(convertToLocalTime(tpd.getModificationDate()));
    }

    private String formatUserLastName(User user) {
        String lastNames = (user.getFirstLastName() == null ? "" : user.getFirstLastName()) +
                (user.getSecondLastName() == null ? "" : (" " + user.getSecondLastName()));
        return lastNames.trim();
    }

    private void populateTransportCompany(GateOutFullSchedulingOutput out, TransportPlanningDetail tpd) {
        Company trucker = tpd.getTruckerCompany();
        if (trucker != null) {
            out.setTransportCompanyId(trucker.getId());
            out.setTransportCompany(trucker.getLegalName());
        }
    }

    private void populateSeals(GateOutFullSchedulingOutput out, TransportPlanningDetail tpd) {
        CargoDocumentDetail dcd = tpd.getCargoDocumentDetail();
        if (dcd != null) {
            CargoDocument dc = dcd.getCargoDocument();
            if (dc != null) {
                Container container = dcd.getContainer();
                if (container != null && dc.getVesselProgrammingDetail() != null) {
                    Optional<VesselProgrammingContainer> pncxOpt = vesselProgrammingContainerRepository
                            .findByContainerIdAndVesselProgrammingDetailId(
                                    container.getId(),
                                    dc.getVesselProgrammingDetail().getId()
                            );

                    if (pncxOpt.isPresent()) {
                        VesselProgrammingContainer pncx = pncxOpt.get();
                        out.setSeal1(pncx.getReceivedSeal1());
                        out.setSeal2(pncx.getReceivedSeal2());
                        out.setSeal3(pncx.getReceivedSeal3());
                        out.setSeal4(pncx.getReceivedSeal4());
                    }
                }
            }
        }
    }

    private LocalDateTime convertToLocalTime(LocalDateTime dateTime) {
        // Assuming you have logic to convert to local time
        if (dateTime == null) {
            return null;
        }
        // Conversion logic here (e.g., based on the business unit's time zone)
        return dateTime; // Placeholder
    }

    private String fetchCatalogTranslationDescLong(Integer catalogId, Integer languageId) {
        return catalogLanguageRepository.fnCatalogTranslationDescLong(catalogId, languageId);
    }

    private String fetchCatalogTranslationDesc(Integer catalogId, Integer languageId) {
        return catalogLanguageRepository.fnCatalogTranslationDesc(catalogId, languageId);
    }
}
