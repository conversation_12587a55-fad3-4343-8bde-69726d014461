package com.maersk.sd1.sdg.service;


import com.fasterxml.jackson.databind.ObjectMapper;
import com.maersk.sd1.common.Parameter;
import com.maersk.sd1.common.model.User;
import com.maersk.sd1.common.repository.CatalogRepository;
import com.maersk.sd1.common.repository.UserRepository;
import com.maersk.sd1.common.model.Eir;
import com.maersk.sd1.sdg.controller.dto.ResponseTruckDepartureRegisterBeforeYard;
import com.maersk.sd1.common.model.MovementInstruction;

import com.maersk.sd1.sdg.controller.dto.SdgTruckDepartureRegisterBeforeYardInput;
import com.maersk.sd1.sdg.repository.SdgEirRepository;
import com.maersk.sd1.sdg.repository.SdgMovementInstructionRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;

@Service
@RequiredArgsConstructor
public class TruckDepartureRegisterBeforeYardService {


    private  ObjectMapper objectMapper;


    private  CatalogRepository catalogRepository;


    private  SdgMovementInstructionRepository movementInstructionRepository;


    private  SdgEirRepository eirRepository;


    private  UserRepository userRepository;


    private  TruckDepartureRegisterCreateimService truckDepartureRegisterCreateimService;

    public ResponseTruckDepartureRegisterBeforeYard registerBeforeYard(SdgTruckDepartureRegisterBeforeYardInput.Root root) throws Exception {

        ResponseTruckDepartureRegisterBeforeYard response = new ResponseTruckDepartureRegisterBeforeYard();

        if (root == null) {
            response.setRespResult(0);
            response.setRespMessage("");
            return response;
        }

        SdgTruckDepartureRegisterBeforeYardInput.Input input = root.getSdg().getInput();


        Integer isGateOut = catalogRepository.findIdByAlias(Parameter.CATALOG_TYPE_GATE_IS_GATEOUT_ALIAS);
        Integer executedMovementInstructionStatus = catalogRepository.findIdByAlias(Parameter.EXECUTED_MOVEMENT_INSTRUCTION_STATUS_ALIAS);
        Integer cancelledMovementInstructionStatus = catalogRepository.findIdByAlias(Parameter.CANCELLED_MOVEMENT_INSTRUCTION_STATUS_ALIAS);
        Integer pendingMovementInstructionStatus = catalogRepository.findIdByAlias(Parameter.PENDING_MOVEMENT_INSTRUCTION_STATUS_ALIAS);
        MovementInstruction i = movementInstructionRepository.
                findByEirAndEstado(input.getEirId(),
                        pendingMovementInstructionStatus);

        Eir eir = eirRepository.findOneById(input.getEirId());

        if (i != null) {
            if (input.getContainerId() == i.getContainer().getId()) {
                //TODO refactor SP instrucccionMovimientoConfirma1
                movementInstructionRepository.instrucccionMovimientoConfirma1(i.getId(),
                        input.getUserRegistrationId());

                response.setRespResult(1);
                response.setRespMessage("Movement Instruction successfully executed");

            } else {

                List<Integer> listNotIn = Arrays.asList(executedMovementInstructionStatus, cancelledMovementInstructionStatus);
                MovementInstruction mov = movementInstructionRepository.findByEirAndNotInEstado(
                        input.getEirId(), listNotIn);

                Integer newStatus;
                if (Objects.equals(mov.getEir().getCatMovement().getId(), isGateOut)) {
                    newStatus = cancelledMovementInstructionStatus;
                } else {
                    newStatus = mov.getCatStatus().getId();
                }

                User user = userRepository.findById(input.getUserRegistrationId()).orElse(null);

                mov.setCatStatus(catalogRepository.findById(newStatus).get());
                mov.setModificationDate(LocalDateTime.now());
                mov.setComment("TRUCK_DEPARTURE_BEFORE_EXECUTION");
                mov.setModificationUser(user);

                movementInstructionRepository.save(mov);

                response = truckDepartureRegisterCreateimService.truckDepartureRegisterCreateim(
                        input.getSubBusinessUnitLocalId(),
                        eir.getId(), input.getContainerId(),
                        input.getUserRegistrationId());

            }
        } else {
            response = truckDepartureRegisterCreateimService.truckDepartureRegisterCreateim(
                    input.getSubBusinessUnitLocalId(),
                    eir.getId(), input.getContainerId(),
                    input.getUserRegistrationId());
        }
        return response;
    }
}
