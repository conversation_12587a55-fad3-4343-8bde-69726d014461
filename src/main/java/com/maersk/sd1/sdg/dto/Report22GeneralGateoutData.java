package com.maersk.sd1.sdg.dto;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import lombok.*;
import lombok.extern.jackson.Jacksonized;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Jacksonized
public class Report22GeneralGateoutData {
    private BigDecimal businessUnitId;
    private BigDecimal subBusinessUnitId;
    private Integer programacionNaveDetalleId;
    private Integer containerId;
    private Integer chassisId;
    private BigDecimal catEmptyFullId;
    private String tipoMov;
    private String local;
    private Integer eirContainerId;
    private LocalDateTime fechaIngresoCamion;
    private LocalDateTime fechaSalidaCamion;
    private String equipmentNumber;
    private String equipmentSizeType;
    private BigDecimal catEquipmentCategory;
    private Integer isocodeId;
    private String isocodeNumber;
    private String ownerPropietario;
    private String reefer;
    private Integer vehiculeId;
    private String plateTruckNumber;
    private BigDecimal transportCompanyId;
    private String transportCompanyCode;
    private String transportCompanyName;
    private String transportCompanyScac;
    private Integer driverId;
    private String driverDoc;
    private String driverName;
    private BigDecimal userRegisterId;
    private String userRegisterName;
    private LocalDateTime fechaRegistro;
    private String seals;
    private String observacion;
    private BigDecimal catCargoDocumentTypeId;
    private String cargoDocumentNumber;
    private String shipperNro;
    private String shipperName;
    private String consigneeNro;
    private String consigneeName;
    private BigDecimal operationTypeId;
    private String operationTypeName;
    private Boolean isShow;
    private Integer usuarioSalidaCamion;
    private Integer productoId;
    private Integer gateInEirNumber;
    private LocalDateTime gateInDate;
    private Integer eirChassisId;
    private String eirChassisNumber;
    private BigDecimal catStructureConditionId;
    private BigDecimal catMachineryConditionId;
    private String refChassisNumber;
    private String operationGroupType;
    private BigDecimal gradeId;
    private Boolean flagChassisPickup;
    private String numeroTwr;
    private String accelerateProgramNumber;

    @Override
    public String toString() {
        return "Report22GeneralGateoutData{" +
                "businessUnitId=" + businessUnitId +
                ", subBusinessUnitId=" + subBusinessUnitId +
                ", programacionNaveDetalleId=" + programacionNaveDetalleId +
                ", containerId=" + containerId +
                ", chassisId=" + chassisId +
                ", catEmptyFullId=" + catEmptyFullId +
                ", tipoMov='" + tipoMov + '\'' +
                ", local='" + local + '\'' +
                ", eirContainerId=" + eirContainerId +
                ", fechaIngresoCamion=" + fechaIngresoCamion +
                ", fechaSalidaCamion=" + fechaSalidaCamion +
                ", equipmentNumber='" + equipmentNumber + '\'' +
                ", equipmentSizeType='" + equipmentSizeType + '\'' +
                ", catEquipmentCategory=" + catEquipmentCategory +
                ", isocodeId=" + isocodeId +
                ", isocodeNumber='" + isocodeNumber + '\'' +
                ", ownerPropietario='" + ownerPropietario + '\'' +
                ", reefer='" + reefer + '\'' +
                ", vehiculeId=" + vehiculeId +
                ", plateTruckNumber='" + plateTruckNumber + '\'' +
                ", transportCompanyId=" + transportCompanyId +
                ", transportCompanyCode='" + transportCompanyCode + '\'' +
                ", transportCompanyName='" + transportCompanyName + '\'' +
                ", transportCompanyScac='" + transportCompanyScac + '\'' +
                ", driverId=" + driverId +
                ", driverDoc='" + driverDoc + '\'' +
                ", driverName='" + driverName + '\'' +
                ", userRegisterId=" + userRegisterId +
                ", userRegisterName='" + userRegisterName + '\'' +
                ", fechaRegistro=" + fechaRegistro +
                ", seals='" + seals + '\'' +
                ", observacion='" + observacion + '\'' +
                ", catCargoDocumentTypeId=" + catCargoDocumentTypeId +
                ", cargoDocumentNumber='" + cargoDocumentNumber + '\'' +
                ", shipperNro='" + shipperNro + '\'' +
                ", shipperName='" + shipperName + '\'' +
                ", consigneeNro='" + consigneeNro + '\'' +
                ", consigneeName='" + consigneeName + '\'' +
                ", operationTypeId=" + operationTypeId +
                ", operationTypeName='" + operationTypeName + '\'' +
                ", isShow=" + isShow +
                ", usuarioSalidaCamion=" + usuarioSalidaCamion +
                ", productoId=" + productoId +
                ", gateInEirNumber=" + gateInEirNumber +
                ", gateInDate=" + gateInDate +
                ", eirChassisId=" + eirChassisId +
                ", eirChassisNumber='" + eirChassisNumber + '\'' +
                ", catStructureConditionId=" + catStructureConditionId +
                ", catMachineryConditionId=" + catMachineryConditionId +
                ", refChassisNumber='" + refChassisNumber + '\'' +
                ", operationGroupType='" + operationGroupType + '\'' +
                ", gradeId=" + gradeId +
                ", flagChassisPickup=" + flagChassisPickup +
                ", numeroTwr='" + numeroTwr + '\'' +
                ", accelerateProgramNumber='" + accelerateProgramNumber + '\'' +
                '}';
    }
}