package com.maersk.sd1.sdg.dto;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import lombok.*;
import lombok.extern.jackson.Jacksonized;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Jacksonized
public class GateInGeneralEquipmentFindV3EquipmentData {
    private Integer id;
    private Integer subBusinessUnitId;
    private Integer equipmentId;
    private Integer cargoDocumentDetailId;
    private Integer catDepotOperId;
    private BigDecimal receivedQuantity;
    private BigDecimal receivedWeight;
    private Integer catCargoConditionId;
    private LocalDateTime registrationDateEquipment;
    private Integer vesselProgrammingDetailId;
    private Integer vesselId;
    private String voyage;
    private String cargoDocument;
    private Integer shippingLineId;
    private Integer catCargoDocumentTypeId;
    private Integer shipperCompanyId;
    private Integer consigneeCompanyId;
    private Integer catReceiptReasonFullId;
    private BigDecimal manifestWeight;
    private Integer manifestWeightUnitId;
    private Integer isoCodeId;
    private String isoCode;
    private String equipmentNumber;
    private Integer catSizeId;
    private Integer catContainerTypeId;
    private Integer transportPlanningDetailId;
    private LocalDateTime transportDate;
    private Integer catStateTrkPlanningId;
    private Integer truckCompanyId;
    private String truckCompanyName;
    private Integer isReefer;
    private Integer isFlat;
    private String operationGroup;
    private Integer catMoveTypeId;
    private Integer isEmptyFull;
    private String imoChecklist;
}
