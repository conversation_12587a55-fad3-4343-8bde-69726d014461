package com.maersk.sd1.sdg.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sdg.controller.dto.GeneralStockInventoryInput;
import com.maersk.sd1.sdg.controller.dto.StockInventoryReportOutputDTO;
import com.maersk.sd1.sdg.service.GeneralStockInventoryReportService;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
@RequestMapping("/ModuleSDG/module/sdg/ReportConsultServiceImp")
public class Report23GeneralStockInventoryController {

    private static final Logger logger = LogManager.getLogger(Report23GeneralStockInventoryController.class.getName());

    private final GeneralStockInventoryReportService generalStockInventoryReportService;

    @PostMapping("/consultReport23")
    public ResponseEntity<ResponseController<Object[]>> getGeneralStockInventoryReport(
            @RequestBody GeneralStockInventoryInput.Root input) {
        try {
            StockInventoryReportOutputDTO generalStockInventoryReportOutput = generalStockInventoryReportService
                    .getGeneralStockInventoryReport(input);
            ResponseController<Object[]> controllerResponse = new ResponseController<>(
                    generalStockInventoryReportOutput.toResultArray());
            return ResponseEntity.ok(controllerResponse);
        } catch (Exception e) {
            logger.error("An error occurred while processing the request.", e);
            return ResponseEntity.status(500).body(new ResponseController<>("An error occurred while processing the request: " + e.getMessage()));
        }
    }
}
