package com.maersk.sd1.sdg.controller.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

public class GateInGeneralEquipmentFindV3Output {

    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    @JsonFormat(shape = JsonFormat.Shape.ARRAY)
    public static class Aps {
        private String equipmentNumber;
        private Integer apsId;
        private LocalDateTime apsDate;
        private String apsDriverFullname;
        private String apsDriverLicenseNumber;
        private String apsTruckLicensePlate;
        private String apsExternalValue;
        private String apsExternalCode;
        private Integer planningDetailId;
        private String eta;
        private Integer truckCompanyId;
        private String truckCompanyName;
    }

    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    @JsonFormat(shape = JsonFormat.Shape.ARRAY)
    public static class Response {
        private Integer isEmptyFullId;
        private String emptyFullAlias;
        private String referenceDocument;
        private String veselDetail;
        private String operationType;
        private String shippingLine;
        private String shipperName;
        private String consigneeName;
        private Integer equipmentId;
        private String equipmentNumber;
        private String equipmentSize;
        private String equipmentType;
        private Integer isoCodeId;
        private String isoCode;
        private BigDecimal manifestWeight;
        private Integer manifestWeightUnitId;
        private Boolean isMandatorySeal1ForFull;
        private Boolean isMandatorySeal2ForFull;
        private Boolean isMandatorySeal3ForFull;
        private Boolean isMandatorySeal4ForFull;
        private Integer catMoveTypeId;
        private Integer vesselProgrammingDetailId;
        private Integer cargoDocumentDetailId;
        private Integer isReefer;
        private Integer isFlat;
        private Integer equipmentSizeId;
        private Integer equipmentTypeId;
        private Integer catOriginId;
        private Integer withContainerEmptyAndFull;
        private String imChecklist;
    }

    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    @JsonFormat(shape = JsonFormat.Shape.ARRAY)
    public static class Result {
        private Integer resultState;
        private String resultMessage;
        private Integer containerSize;
        private Integer containerType;
    }

    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    @JsonFormat(shape = JsonFormat.Shape.ARRAY)
    public static class Output {
        private List<Result> result;
        private List<Response> response;
        private List<Aps> aps;
    }
}
