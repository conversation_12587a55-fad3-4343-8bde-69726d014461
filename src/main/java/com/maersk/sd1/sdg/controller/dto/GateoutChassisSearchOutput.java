package com.maersk.sd1.sdg.controller.dto;

import lombok.Builder;
import lombok.Data;

import java.util.List;

@Builder
@Data
public class GateoutChassisSearchOutput {

    private int statusCode;
    private String message;
    private List<List<Object[]>> chassisData;

    public Object[][][] toResultArray() {
        return chassisData.stream()
                .map(list -> list.toArray(Object[][]::new))
                .toArray(Object[][][]::new);
    }
}