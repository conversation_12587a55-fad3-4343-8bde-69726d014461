package com.maersk.sd1.sdg.controller.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.maersk.sd1.common.serializer.GateInGeneralRegisterV2Serializer;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

public class GateInGeneralRegisterV2Output {

    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    @JsonSerialize(using = GateInGeneralRegisterV2Serializer.class)
    public static class Output {
        private Result result;
        private List<Response> response;
        private Integration integration;
        private Integration apsIntegration;
    }

    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    public static class Result {
        private Integer resultState;
        private String resultMessage;
        private String eirId;
    }

    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    public static class Response {
        private Integer eirId;
        private String equipmentCatEmpty_fullAlias;
        private String equipmentNumber;
        private Integer eirChassisId;
        private String chassisNumber;
        private String statusMessage;
        private String message;
        private Integer emrInspectionId;
        private String yardLocation;
        private String containerNotApplicable;
        private String eirChassisZoneActivityId;
        private String isEmpty;
    }

    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    public static class Integration {
        private String typeProductIntegration;
        private String integrationResult;
    }
}
