package com.maersk.sd1.sdg.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sdg.controller.dto.Report21GeneralGateinOutput;
import com.maersk.sd1.sdg.controller.dto.Report22GeneralGateoutInput;
import com.maersk.sd1.sdg.service.Report21GeneralGateInService;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RequiredArgsConstructor
@RestController
@RequestMapping("/ModuleSDG/module/sdg/ReportConsultServiceImp")
public class Report21GeneralGateInController {

        private static final Logger logger = LogManager.getLogger(Report21GeneralGateInController.class.getName());

        private final Report21GeneralGateInService report21GeneralGateInService;

    @PostMapping("/consultReport21")
        public ResponseEntity<ResponseController<Report21GeneralGateinOutput>> report21GeneralGateoutService(@RequestBody Report22GeneralGateoutInput.Root request) {
            try {
                logger.info("Request received: {}", request);
                return report21GeneralGateInService.report21GeneralGateinService((request));
            } catch (Exception e) {
                logger.error("An error occurred while processing the request.", e);
                return ResponseEntity.status(500).body(new ResponseController<>("An error occurred while processing the request: " + e.getMessage()));
            }
        }
}
