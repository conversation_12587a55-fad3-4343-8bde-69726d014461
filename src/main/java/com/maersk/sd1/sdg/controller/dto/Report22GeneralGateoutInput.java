package com.maersk.sd1.sdg.controller.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

public class Report22GeneralGateoutInput {

    @Data
    public static class Input {

        @JsonProperty("usuario_id")
        private Integer userId;

        @JsonProperty("sub_unidad_negocio_id")
        private Integer subUnidadNegocioId;

        @JsonProperty("idioma_id")
        private Integer idiomaId = 1;

        @JsonProperty("truck_in_date_desde")
        @JsonFormat(pattern = "dd/MM/yyyy")
        private LocalDate truckInDateDesde;

        @JsonProperty("truck_in_date_hasta")
        @JsonFormat(pattern = "dd/MM/yyyy")
        private LocalDate truckInDateHasta;

        @JsonProperty("equipment_category")
        private Integer equipmentCategory;

        @JsonProperty("cat_empty_full_id")
        private Integer catEmptyFullId;

        @JsonProperty("equipment_number")
        private String equipmentNumber;

        @JsonProperty("eir_number")
        private Integer eirNumber;

        @JsonProperty("reference_document_number")
        private String referenceDocumentNumber;

        @JsonProperty("shipping_line_id")
        private Integer shippingLineId;

        @JsonProperty("shipper_name")
        private Integer shipperName;

        @JsonProperty("consignee_name")
        private Integer consigneeName;

        @JsonProperty("trucking_company_scac")
        private Integer truckingCompanyScac;

        @JsonProperty("owner_chassis_name")
        private Integer ownerChassisName;

        @JsonProperty("pagina")
        private Integer pagina;

        @JsonProperty("cantidad")
        private Integer cantidad;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("SDG")
        private Prefix prefix;
    }

}








