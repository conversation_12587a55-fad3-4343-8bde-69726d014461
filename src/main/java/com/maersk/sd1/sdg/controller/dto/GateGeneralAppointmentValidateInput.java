package com.maersk.sd1.sdg.controller.dto;


import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

public class GateGeneralAppointmentValidateInput {
    @Data
    public static class Input {
        @JsonProperty("sub_business_unit_local_id")
        private Integer subBusinessUnitLocalId;

        @JsonProperty("type_process")
        private String typeProcess;

        @JsonProperty("exclude_shipping_line")
        private String excludeShippingLine = null;
    }
    @Data
    public static class Prefix {
        @JsonProperty("F")
        private Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("SDG")
        private Prefix prefix;
    }
}

