package com.maersk.sd1.sdg.controller.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Data
public class TruckArrivalRejectOutput {

    @JsonProperty("count")
    private Long count = 0L;

    @JsonProperty("data")
    private List<DataMap> data =  new ArrayList<>();;

    @Data
    public static class DataMap {
        @JsonProperty("eir_reject_number")
        private String eirRejectNumber;

        @JsonProperty("container_number")
        private String containerNumber;

        @JsonProperty("container_booking")
        private String containerBooking;

        @JsonProperty("empty_full")
        private String emptyFull;

        @JsonProperty("chassis_number")
        private String chassisNumber;

        @JsonProperty("chassis_booking")
        private String chassisBooking;

        @JsonProperty("type_rejection")
        private String typeRejection;

        @JsonProperty("type_movement")
        private String typeMovement;

        @JsonProperty("truck_plate")
        private String truckPlate;

        @JsonProperty("truck_company")
        private String truckCompany;

        @JsonProperty("driver")
        private String driver;

        @JsonProperty("comment")
        private String comment;

        @JsonProperty("registration_date")
        private String registrationDate;

        @JsonProperty("origin_reject")
        private String originReject;

        @JsonProperty("eir_number")
        private String eirNumber;
    }

}
