package com.maersk.sd1.sdy.controller.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.experimental.UtilityClass;

@UtilityClass
public class WorkQueueGetInput {

    @Data
    public static class Input {
        @JsonProperty("cola_trabajo_id")
        @NotNull
        private Integer colaTrabajoId;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("SDY")
        private Prefix prefix;
    }
}
