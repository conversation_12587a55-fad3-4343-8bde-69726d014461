package com.maersk.sd1.sdy.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sdy.dto.PlanificacionInstruccionMovimientoAtenderInput;
import com.maersk.sd1.sdy.dto.PlanificacionInstruccionMovimientoAtenderOutput;
import com.maersk.sd1.sdy.service.PlanificacionInstruccionMovimientoAtenderService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RequiredArgsConstructor
@RestController
@RequestMapping("/ModuleSDY/moduleYard/sdy/SDYPlanificacionServiceImp")
public class PlanificacionInstruccionMovimientoAtenderController {

    private static final Logger logger = LogManager.getLogger(PlanificacionInstruccionMovimientoAtenderController.class);
    private final PlanificacionInstruccionMovimientoAtenderService planificacionService;

    @PostMapping("/sdyatenderPlanificacion")
    public ResponseEntity<ResponseController<PlanificacionInstruccionMovimientoAtenderOutput>> planificacionInstruccionMovimientoAtender(
            @RequestBody @Valid PlanificacionInstruccionMovimientoAtenderInput.Root request) {
        try {

            if(request == null || request.getPrefix() == null || request.getPrefix().getInput() == null) {
                logger.error("Invalid request");
                return ResponseEntity.badRequest().body(new ResponseController<>("Invalid request"));
            }

            logger.info("Request received planificacionInstrucccionMovimientoAtender: {}", request);
            PlanificacionInstruccionMovimientoAtenderOutput output = planificacionService.processMovements(request);
            return ResponseEntity.ok(new ResponseController<>(output));
        } catch (Exception e) {
            logger.error("An error occurred while processing planificacionInstrucccionMovimientoAtender.", e);
            PlanificacionInstruccionMovimientoAtenderOutput errorResponse = new PlanificacionInstruccionMovimientoAtenderOutput();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(new ResponseController<>(errorResponse));
        }
    }
}
