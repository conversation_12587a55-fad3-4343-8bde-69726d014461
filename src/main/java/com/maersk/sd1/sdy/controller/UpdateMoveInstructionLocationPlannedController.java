package com.maersk.sd1.sdy.controller;

import com.maersk.sd1.sdy.dto.UpdateMoveInstructionLocationPlannedInputDTO;
import com.maersk.sd1.sdy.dto.UpdateMoveInstructionLocationPlannedOutputDTO;
import com.maersk.sd1.sdy.service.UpdateMoveInstructionLocationPlannedService;
import com.maersk.sd1.common.controller.dto.ResponseController;
import jakarta.validation.Valid;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/module/sdy/updateMoveInstructionLocationPlanned")
public class UpdateMoveInstructionLocationPlannedController {

    private static final Logger logger = LogManager.getLogger(UpdateMoveInstructionLocationPlannedController.class);

    @Autowired
    private UpdateMoveInstructionLocationPlannedService service;

    @PostMapping("/update")
    public ResponseEntity<ResponseController<UpdateMoveInstructionLocationPlannedOutputDTO>> updateMoveInstructionLocationPlanned(
            @RequestBody @Valid UpdateMoveInstructionLocationPlannedInputDTO request) {
        try {
            UpdateMoveInstructionLocationPlannedOutputDTO result = service.updateMoveInstructionLocationPlanned(List.of(request));
            return ResponseEntity.ok(new ResponseController<>(result));
        } catch (Exception e) {
            logger.error("An error occurred while updating move instruction location planned.", e);
            UpdateMoveInstructionLocationPlannedOutputDTO output = new UpdateMoveInstructionLocationPlannedOutputDTO();
            output.setRespEstado(0);
            output.setRespMensaje(e.getMessage());
            return ResponseEntity.status(500).body(new ResponseController<>(output));
        }
    }
}
