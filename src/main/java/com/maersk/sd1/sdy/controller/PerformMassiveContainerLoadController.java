package com.maersk.sd1.sdy.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sdy.dto.PerformMassiveContainerLoadInput;
import com.maersk.sd1.sdy.dto.PerformMassiveContainerLoadOutput;
import com.maersk.sd1.sdy.service.PerformMassiveContainerLoadService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;

@RestController
@RequestMapping("/modulesdy/ModuleSDY/module/sdy/SDYContainerLoadServiceImp")
@RequiredArgsConstructor
@Slf4j
public class PerformMassiveContainerLoadController {

    private final PerformMassiveContainerLoadService performMassiveContainerLoadService;

    @PostMapping("/sdyperformMassiveContainerLoad")
    public ResponseEntity<ResponseController<PerformMassiveContainerLoadOutput>> performMassiveContainerLoad(
            @Valid @RequestBody PerformMassiveContainerLoadInput.Root root) {

        if(root == null || root.getPrefix() == null || root.getPrefix().getInput() == null) {
            return ResponseEntity.badRequest().body(new ResponseController<>("Invalid request"));
        }
        PerformMassiveContainerLoadInput.Input input = root.getPrefix().getInput();

        log.info("Received request to perform massive container load for subBusinessUnitId: {}",
                input.getSubBusinessUnitLocalId());

        PerformMassiveContainerLoadOutput result = performMassiveContainerLoadService.performMassiveContainerLoad(input);

        return ResponseEntity.ok(new ResponseController<>(result));
    }
}