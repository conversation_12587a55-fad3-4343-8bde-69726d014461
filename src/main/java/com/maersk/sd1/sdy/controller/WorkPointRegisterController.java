package com.maersk.sd1.sdy.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sdy.dto.WorkPointRegisterInput;
import com.maersk.sd1.sdy.dto.WorkPointRegisterOutput;
import com.maersk.sd1.sdy.service.WorkPointRegisterService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RequiredArgsConstructor
@RestController
@RequestMapping("/ModuleSDY/module/sdy/SDYPuntoTrabajoServiceImp")
public class WorkPointRegisterController {

    private static final Logger logger = LogManager.getLogger(WorkPointRegisterController.class);

    private final WorkPointRegisterService workPointRegisterService;

    @PostMapping("/sdypuntoTrabajoRegistrar")
    public ResponseEntity<ResponseController<WorkPointRegisterOutput>> sdgWorkPointRegister(
            @Valid @RequestBody WorkPointRegisterInput.Root request
    ) {
        try {
            logger.info("Request received sdgWorkPointRegister: {}", request);
            WorkPointRegisterInput.Input input = request.getPrefix().getInput();

            WorkPointRegisterOutput output = workPointRegisterService.registerWorkPoint(
                    input.getYardId(),
                    input.getCode(),
                    input.getDescription(),
                    input.getActive(),
                    input.getUserRegistrationId(),
                    input.getLanguageId()
            );

            return ResponseEntity.ok(new ResponseController<>(output));
        } catch (Exception e) {
            logger.error("An error occurred while processing the request.", e);
            WorkPointRegisterOutput output = new WorkPointRegisterOutput();
            output.setRespMensaje(e.getMessage());
            output.setRespEstado(0);
            output.setRespNewId(0);
            return ResponseEntity.status(500).body(new ResponseController<>(output));
        }
    }
}
