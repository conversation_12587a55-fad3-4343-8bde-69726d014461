package com.maersk.sd1.sdy.controller.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.experimental.UtilityClass;

@UtilityClass
public class WorkQueueContainerMovementDeleteInput {

    @Data
    public static class Input {

        @JsonProperty("cola_trabajo_movimiento_id")
        @NotNull(message = "MovementId cannot be null")
        private Integer movementId;

        @JsonProperty("usuario_modificacion_id")
        @NotNull(message = "UserModificationId cannot be null")
        private Integer userModificationId;

        @JsonProperty("idioma_id")
        @NotNull(message = "LanguageId cannot be null")
        private Integer languageId;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("SDY")
        private Prefix prefix;
    }
}