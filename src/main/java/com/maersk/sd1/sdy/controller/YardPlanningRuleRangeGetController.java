package com.maersk.sd1.sdy.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sdy.dto.YardPlanningRuleRangeGetInput;
import com.maersk.sd1.sdy.dto.YardPlanningRuleRangeGetOutput;
import com.maersk.sd1.sdy.service.YardPlanningRuleRangeGetService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

@RequiredArgsConstructor
@RestController
@RequestMapping("/ModuleSDY/module/sdy/SDYRangoReglaPlanificacionPatioServiceImp")
public class YardPlanningRuleRangeGetController {

    private static final Logger logger = LogManager.getLogger(YardPlanningRuleRangeGetController.class.getName());

    private final YardPlanningRuleRangeGetService yardPlanningRuleRangeGetService;

    @PostMapping("/sdyrangoReglaPlanificacionPatioObtener")
    public ResponseEntity<ResponseController<List<YardPlanningRuleRangeGetOutput>>> getYardPlanningRuleRange(@RequestBody @Valid YardPlanningRuleRangeGetInput.Root request) {
        try {
            logger.info("Request received getYardPlanningRuleRange: {}", request);
            Integer rangeId = request.getPrefix().getInput().getRangeId();
            YardPlanningRuleRangeGetOutput output = yardPlanningRuleRangeGetService.getRange(rangeId);
            return ResponseEntity.ok(new ResponseController<>(List.of(output)));
        } catch (Exception e) {
            logger.error("An error occurred while processing the request.", e);
            List<YardPlanningRuleRangeGetOutput> errorOutput = new ArrayList<>();
            return ResponseEntity.status(500).body(new ResponseController<>(errorOutput));
        }
    }
}
