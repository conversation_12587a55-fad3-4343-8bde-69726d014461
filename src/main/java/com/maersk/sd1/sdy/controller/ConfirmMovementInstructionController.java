package com.maersk.sd1.sdy.controller;


import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sdy.dto.MovementInstructionConfirmInput;
import com.maersk.sd1.sdy.dto.MovementInstructionConfirmOutput;
import com.maersk.sd1.sdy.service.ConfirmMovementInstructionService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
@RequestMapping("/ModuleSDY/moduleYard/sdy/SDYPlanificacionServiceImp")
public class ConfirmMovementInstructionController {

    private static final Logger logger = LogManager.getLogger(ConfirmMovementInstructionController.class.getName());

    private final ConfirmMovementInstructionService confirmMovementInstructionService;

    @PostMapping("/sdyaprobarPlanificacion")
    public ResponseEntity<ResponseController<MovementInstructionConfirmOutput>> confirmMovementInstruction(@RequestBody @Valid MovementInstructionConfirmInput.Root request) {
        try {
            if(request==null || request.getPrefix()==null || request.getPrefix().getInput()==null){
                logger.error("Invalid request");
                return ResponseEntity.badRequest().body(new ResponseController<>("Invalid request"));
            }

            logger.info("Request received /confirmMovementInstruction: {}", request);
            MovementInstructionConfirmInput.Input input = request.getPrefix().getInput();
            MovementInstructionConfirmOutput result = confirmMovementInstructionService.confirmMovementInstruction(input);
            return ResponseEntity.ok(new ResponseController<>(result));
        } catch (Exception e) {
            logger.error("An error occurred while processing /confirmMovementInstruction request.", e);
            MovementInstructionConfirmOutput errorOutput = new MovementInstructionConfirmOutput();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(new ResponseController<>(errorOutput));
        }
    }

}
