package com.maersk.sd1.sdy.controller.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.experimental.UtilityClass;

@UtilityClass
public class RuleContainerFilterDisableInput {
    @Data
    public static class Input {
        @JsonProperty("rule_container_filter_id")
        @NotNull
        private Integer ruleContainerFilterId;

        @JsonProperty("user_id")
        @NotNull
        private Integer userId;

        @JsonProperty("business_unit_id")
        private Integer businessUnitId;

        @JsonProperty("language_id")
        private Integer languageId;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private Input input;
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Root {
        @JsonProperty("SDY")
        private Prefix prefix;
    }
}
