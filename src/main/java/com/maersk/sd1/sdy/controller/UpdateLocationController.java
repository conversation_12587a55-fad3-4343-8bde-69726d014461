package com.maersk.sd1.sdy.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sdy.dto.UpdateLocationInputDTO;
import com.maersk.sd1.sdy.dto.UpdateLocationOutputDTO;
import com.maersk.sd1.sdy.service.UpdateLocationService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/ModuleSDY/moduleYard/sdy/SDYOperacionControllerImpl")
@RequiredArgsConstructor
public class UpdateLocationController {

    private static final Logger logger = LogManager.getLogger(UpdateLocationController.class.getName());

    private final UpdateLocationService updateLocationService;

    @PostMapping("/sdyactualizarUbicacion")
    public ResponseEntity<ResponseController<UpdateLocationOutputDTO>> updateLocation(@RequestBody @Valid UpdateLocationInputDTO.Root request) {
        try {
            if(request==null || request.getPrefix()==null || request.getPrefix().getInput()==null){
                logger.error("Invalid request");
                return ResponseEntity.badRequest().body(new ResponseController<>("Invalid request"));
            }

            logger.info("Request received : {}", request);
            UpdateLocationInputDTO.Input input = request.getPrefix().getInput();
            UpdateLocationOutputDTO result = updateLocationService.updateLocation(input);
            return ResponseEntity.ok(new ResponseController<>(result));
        } catch (Exception e) {
            logger.error("An error occurred while processing request.", e);
            UpdateLocationOutputDTO errorOutput = new UpdateLocationOutputDTO();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(new ResponseController<>(errorOutput));
        }
    }

}
