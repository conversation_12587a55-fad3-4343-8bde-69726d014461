package com.maersk.sd1.sdy.controller.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
@JsonFormat(shape = JsonFormat.Shape.ARRAY)
public class RuleContainerFilterDisableOutput {

    @JsonProperty("resp_result")
    private Integer respResult;

    @JsonProperty("resp_message")
    private String respMessage;
}
