package com.maersk.sd1.sdy.controller;


import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sdy.dto.BlockNoVirtualByYardInput;
import com.maersk.sd1.sdy.dto.BlockNoVirtualByYardOutput;
import com.maersk.sd1.sdy.service.BlockNoVirtualByYardService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RequiredArgsConstructor
@RestController
@RequestMapping("/ModuleSDY/module/sdy/SDYBloqueServiceImp")
public class BlockNoVirtualByYardController {

    private static final Logger logger = LogManager.getLogger(BlockNoVirtualByYardController.class);

    private final BlockNoVirtualByYardService blockNoVirtualByYardService;

    @PostMapping("/sdyobtenerBloquesNoVirtualesPorPatio")
    public ResponseEntity<ResponseController<BlockNoVirtualByYardOutput>> getNoVirtualBlocksByYard(
            @RequestBody @Valid BlockNoVirtualByYardInput.Root request) {
        try {
            logger.info("Request received getNoVirtualBlocksByYard: {}", request);
            Integer yardId = request.getPrefix().getInput().getPatioId();
            BlockNoVirtualByYardOutput output = blockNoVirtualByYardService.getBlocksNonVirtualByYard(yardId);
            return ResponseEntity.ok(new ResponseController<>(output));
        } catch (Exception e) {
            logger.error("An error occurred while processing the request.", e);
            BlockNoVirtualByYardOutput output = new BlockNoVirtualByYardOutput();
//            output.setRespEstado(0);
//            output.setRespMensaje(e.toString());
            return ResponseEntity.status(500).body(new ResponseController<>(output));
        }
    }
}

