package com.maersk.sd1.sdy.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sdy.dto.ContainerSearchByCriteriaDetailInput;
import com.maersk.sd1.sdy.dto.ContainerSearchByCriteriaDetailOutput;
import com.maersk.sd1.sdy.service.ContainerSearchByCriteriaDetailService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequiredArgsConstructor
@RequestMapping("/ModuleSDY/module/sdy/SDYContainerInformationServiceImp")
public class ContainerSearchByCriteriaDetailController {

    private static final Logger logger = LogManager.getLogger(ContainerSearchByCriteriaDetailController.class);

    private final ContainerSearchByCriteriaDetailService service;

    @PostMapping("/sdycontainerSearchByCriteriaDetail")
    public ResponseEntity<ResponseController<List<ContainerSearchByCriteriaDetailOutput>>> containerSearchDetail(@RequestBody @Valid ContainerSearchByCriteriaDetailInput.Root request) {
        try {
            ContainerSearchByCriteriaDetailInput.Input input = request.getPrefix().getInput();
            List<ContainerSearchByCriteriaDetailOutput> result = service.containerSearchByCriteria(input);
            return ResponseEntity.ok(new ResponseController<>(result));
        } catch (Exception e) {
            logger.error("An error occurred while processing containerSearchDetail request.", e);
            ContainerSearchByCriteriaDetailOutput output = new ContainerSearchByCriteriaDetailOutput();
            output.setEirId(null);
            output.setContainerLocation(null);
            output.setStructureCondition(null);
            output.setEquipmentRestriction(e.toString());
            output.setBookingPreAllocation(null);
            return ResponseEntity.status(500).body(new ResponseController<>(List.of(output)));
        }
    }
}
