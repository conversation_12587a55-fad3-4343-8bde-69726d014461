package com.maersk.sd1.sdy.dto;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import com.maersk.sd1.business.core.planing.domain.InstruccionMovimiento;
import com.maersk.sd1.business.core.planing.domain.operacion.ActualizarUbicacionRequest;
import com.maersk.sd1.business.core.planing.domain.planificacion.VisitaContenedor;
import com.maersk.sd1.business.core.planing.domain.operacion.ConfirmarMovimientoRequest;
import com.maersk.sd1.business.core.planing.domain.operacion.ObtenerNuevaUbicacionRequest;
import com.maersk.sd1.business.core.planing.domain.operacion.PlanificacionRehandlingRequest;
import com.maersk.sd1.business.core.planing.domain.operacion.PlanificacionUbicacionAsignadaRequest;
import com.maersk.sd1.business.core.planing.domain.planificacion.*;
import com.maersk.sd1.business.core.planing.domain.planificacion.DatosPlanificacion;
import com.maersk.sd1.business.core.planing.domain.planificacion.new_location.NewLocationRequest;
import com.maersk.sd1.business.core.planing.domain.planificacion.update_manual_location.UpdateManualLocationRequest;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@AllArgsConstructor
@Builder
@NoArgsConstructor
@ToString
public class PlanificacionRequestCommand {
    private static String Tipo_Bloque_Virtual = "VIRTUAL";
    private static String Tipo_Bloque_Heap = "HEAP";

    private String usuario_alias;
    private String sub_unidad_negocio_alias;

    private int unidad_negocio_id;
    private int unidad_negocio_padre_id;

    private int patio_id;
    private String patio_codigo;
    private String tipo_operacion;
    private String tipo_movimiento;
    // GI: Gate In, AZ: Actividad en Zona, INSP: Inspecciones, GO: Gate Out
    private String proceso_realizado;

    // Necesarios para que UI pueda procesar la respuesta
    private boolean IsCorrect;
    private String Status;

    private Collection<VisitaContenedor> contenedores = new ArrayList<VisitaContenedor>();

    private Collection<EquipmentDocument> aditional_data = new ArrayList<EquipmentDocument>();

    private Collection<PlannedEquipmentResponse> planned_equipments = new ArrayList<PlannedEquipmentResponse>();

    private int usuario_id;
    private String BusinessMessage;
    private Integer cola_trabajo_id;

    // TODO: Llevar las sigtes propiedades a una clase heredada especifica del tipo
    // de planificacion
    // private Bloque bloque_destino;
    private Integer bloque_destino_id;
    private String bloque_destino_codigo;

    //Utilizados en DESENTIERRO
    private Collection<Integer> rows_skip_range;
    private Integer skip_block_id;
    private Integer father_movement_instruction_id;

    public void ReemplazarContenedor(VisitaContenedor contenedor) throws Exception {
        if (contenedor == null) {
            throw new Exception("El valor utilizado para reemplazar el contenedor actual no puede ser nulo");
        }
        this.contenedores = new ArrayList<VisitaContenedor>(List.of(contenedor));
    }

    public void AddEquipments(List<VisitaContenedor> equipments) {
        equipments.forEach(equipment -> {
            contenedores.add(equipment);
        });
    }

    public static PlanificacionRequestCommand actualizar(PlanificacionRequestCommand request,
                                                                                                                   Collection<InstruccionMovimiento> instrucciones_movimiento,
                                                                                                                   DatosPlanificacion datos) {

        request.getContenedores()
                .stream().filter(c -> instrucciones_movimiento.stream()
                        .anyMatch(i -> i.getContenedor().getNumero_contenedor().equals(c.getNumero_contenedor())))
                .forEach(c -> {
                    InstruccionMovimiento instruccion = ObtenerInstruccion(c.getNumero_contenedor(), instrucciones_movimiento);

                    request.setCola_trabajo_id(instruccion.getCola_trabajo().getCola_trabajo_id());

                    c.setInstruccion_estado_codigo(instruccion.getEstado_codigo());
                    c.setInstruccion_estado_descripcion(instruccion.getEstado_codigo());
                    c.setInstruccion_numero(instruccion.getInstruccion_movimiento_id());

                    if(instruccion.getUbicacion_destino().getBloque().getCat_bloque_codigo() != null
                            && (instruccion.getUbicacion_destino().getBloque().getCat_bloque_codigo().equals(Tipo_Bloque_Virtual)
                            || instruccion.getUbicacion_destino().getBloque().getCat_bloque_codigo().equals(Tipo_Bloque_Heap))) {
                        c.setUbicacion_destino(new UbicacionContenedorValor(
                                instruccion.getUbicacion_destino().getBloque().getCodigo(),
                                instruccion.getUbicacion_destino().getBloque().getCat_bloque_codigo(),
                                null,
                                null,
                                (Integer)null,
                                null,
                                null,
                                null,
                                (Integer)null));

                    } else {
                        c.setUbicacion_destino(new UbicacionContenedorValor(
                                instruccion.getUbicacion_destino().getBloque().getCodigo(),
                                instruccion.getUbicacion_destino().getBloque().getCat_bloque_codigo(),
                                instruccion.getUbicacion_destino().getCelda().getFila(),
                                instruccion.getUbicacion_destino().getCelda().getColumna(),
                                instruccion.getUbicacion_destino().getNivel().getIndice(),
                                instruccion.getUbicacion_destino().getBloque40() != null ? instruccion.getUbicacion_destino().getBloque40().getCodigo() : null,
                                instruccion.getUbicacion_destino().getCelda40() != null ? instruccion.getUbicacion_destino().getCelda40().getFila() : null,
                                instruccion.getUbicacion_destino().getCelda40() != null ? instruccion.getUbicacion_destino().getCelda40().getColumna() : null,
                                instruccion.getUbicacion_destino().getNivel40() != null ? instruccion.getUbicacion_destino().getNivel40().getIndice() : null));
                    }

                    // Devolvemos ubicación origen
                    var contenedor = datos.getEquipmentPlanificationData().stream()
                            .filter(e -> e.getContenedor().getNumero_contenedor().equals(c.getNumero_contenedor()))
                            .findFirst();
                    c.setUbicacion_origen(UbicacionContenedorValor.CrearDesdeUbicacionContenedor(contenedor.get().getContenedor().getUbicacion_actual()));

                    // Clase, Tamaño, Familia del contenedor
                    c.setClase(contenedor.get().getContenedor().getClase().getCodigo());
                    c.setFamilia(contenedor.get().getContenedor().getFamilia_contenedor().getCodigo());
                    c.setTamanio(contenedor.get().getContenedor().getTamanio_contenedor().getCodigo());
                    c.setCondicion(contenedor.get().getContenedor().getUltimo_tipo_actividad_codigo());
                });

        return request;
    }

    private static InstruccionMovimiento ObtenerInstruccion(String numero_contenedor,
                                                            Collection<InstruccionMovimiento> instrucciones) {
        return instrucciones.stream().filter(i -> i.getContenedor().getNumero_contenedor().equals(numero_contenedor))
                .findFirst().orElse(null);
    }

    public static PlanificacionRequestCommand CrearDesde(PlanificacionIngresoRequest request) {
        var plan_request = PlanificacionRequestCommand.builder()
                .unidad_negocio_id(request.getUnidad_negocio_id())
                .patio_codigo(request.getPatio_codigo())
                .tipo_operacion(request.getTipo_operacion())
                .usuario_id(request.getUsuario_id())
                .contenedores(new ArrayList<VisitaContenedor>())
                .proceso_realizado(request.getProceso_realizado())
                .build();
        request.getContenedores().forEach(c -> {
            plan_request.contenedores.add(new VisitaContenedor(c.getNumero_contenedor(), c.getEir_numero()));
        });
        return plan_request;
    }

    public static PlanificacionRequestCommand CrearNewLocationFrom(NewLocationRequest request) {

        var plan_request = PlanificacionRequestCommand.builder()
                //.sub_unidad_negocio_alias(request.getSub_business_unit_alias())
                //.patio_codigo(request.getYard_code())
                .unidad_negocio_id(request.getSub_business_unit_id())
                .usuario_alias(request.getUser_alias())
                .contenedores(new ArrayList<VisitaContenedor>())
                .aditional_data(new ArrayList<EquipmentDocument>())
                .build();

        request.getAditional_data().forEach(aditional_data -> {
            plan_request.contenedores.add(new VisitaContenedor(
                    aditional_data.getEquipment_number(),
                    aditional_data.getEquipment_counter_request(),
                    aditional_data.getMove_instruction_number()));

            var document = aditional_data.getDocument();
            var adictional_data = EquipmentDocument.builder()
                    .document(document)
                    .build();
            plan_request.getAditional_data().add(adictional_data);
        });
        return plan_request;
    }

    public static PlanificacionRequestCommand CrearDesde(PlanningGateInRequest request) {
        var plan_request = PlanificacionRequestCommand.builder()
                .patio_codigo(request.getPatio_codigo())
                .tipo_operacion(request.getTipo_operacion())
                .usuario_id(request.getUsuario_id())
                .contenedores(new ArrayList<VisitaContenedor>())
                .proceso_realizado(request.getProceso_realizado())
                .build();

        plan_request.setUsuario_alias(request.getUsuario_alias());
        plan_request.setSub_unidad_negocio_alias(request.getSub_unidad_negocio_alias());
        plan_request.setAditional_data(request.getAditional_data());

        request.getAditional_data().forEach(aditional_data -> {
            var equipment = aditional_data.getEquipment();
            var document = aditional_data.getDocument();
            plan_request.contenedores.add(new VisitaContenedor(equipment.getNumero_contenedor(), document.getEir_id()));
        });

        return plan_request;
    }

    public static PlanificacionRequestCommand CreateGateFrom(PlanningGateRequest request) {
        var plan_request = PlanificacionRequestCommand.builder()
                .patio_codigo(request.getYard_code())
                .tipo_operacion(request.getOperation_type())
                .contenedores(new ArrayList<VisitaContenedor>())
                .proceso_realizado(request.getProcess_realized())
                .build();

        plan_request.setUsuario_alias(request.getUser_alias());
        plan_request.setSub_unidad_negocio_alias(request.getSub_business_unit_alias());
        plan_request.setAditional_data(request.getAditional_data());

        request.getAditional_data().forEach(aditional_data -> {
            var equipment = aditional_data.getEquipment();
            var document = aditional_data.getDocument();
            plan_request.contenedores.add(new VisitaContenedor(equipment.getNumero_contenedor(), String.valueOf(document.getEir_id())));
        });

        return plan_request;
    }

    public static PlanificacionRequestCommand CrearDesde(PlanificacionOperacionRequestCommand command) {
        return PlanificacionRequestCommand.builder()
                .unidad_negocio_id(command.getUnidad_negocio_id())
                .patio_codigo(command.getPatio_codigo())
                .tipo_operacion(command.getTipo_operacion())
                .usuario_id(command.getUsuario_id())
                .contenedores(command.getContenedores())
                .build();
    }

    public static PlanificacionRequestCommand CrearDesde(PlanificacionRehandlingRequest request) {
        var plan_request = PlanificacionRequestCommand.builder()
                .unidad_negocio_id(request.getUnidad_negocio_id())
                .patio_codigo(request.getPatio_codigo())
                .usuario_id(request.getUsuario_id())
                .tipo_movimiento("REH")
                .contenedores(new ArrayList<VisitaContenedor>())
                .build();

        request.getContenedores().forEach(c -> {
            plan_request.contenedores
                    .add(new VisitaContenedor(c.getNumero_contenedor(), c.getInstruccion_numero(), c.getUbicacion()));
        });

        return plan_request;
    }

    public static PlanificacionRequestCommand CrearDesde(ObtenerNuevaUbicacionRequest request) {
        var plan_request = PlanificacionRequestCommand.builder()
                .unidad_negocio_id(request.getUnidad_negocio_id())
                .patio_codigo(request.getPatio_codigo())
                .usuario_id(request.getUsuario_id())
                .contenedores(new ArrayList<VisitaContenedor>())
                .build();

        request.getContenedores().forEach(c -> {
            plan_request.contenedores.add(new VisitaContenedor(c.getNumero_contenedor(),
                    c.getContador_peticion(),
                    c.getInstruccion_numero()));
        });

        return plan_request;
    }

    public static PlanificacionRequestCommand CrearDesde(PlanificacionUbicacionAsignadaRequest request) {
        var plan_request = PlanificacionRequestCommand.builder()
                .unidad_negocio_id(request.getUnidad_negocio_id())
                .patio_codigo(request.getPatio_codigo())
                .usuario_id(request.getUsuario_id())
                .tipo_movimiento("REH")
                .contenedores(new ArrayList<VisitaContenedor>())
                .cola_trabajo_id(request.getCola_trabajo_id())
                .patio_id(request.getPatio_id())
                .build();

        request.getContenedores().forEach(c -> {
            plan_request.contenedores.add(new VisitaContenedor(c.getNumero_contenedor(), null, c.getUbicacion(), c.getContenedor_id()));
        });

        return plan_request;
    }

    public static PlanificacionRequestCommand CrearDesde(ActualizarUbicacionRequest request) {
        var plan_request = PlanificacionRequestCommand.builder()
                .unidad_negocio_id(request.getUnidad_negocio_id())
                .patio_codigo(request.getPatio_codigo())
                .usuario_id(request.getUsuario_id())
                .contenedores(new ArrayList<VisitaContenedor>())
                .patio_id(request.getPatio_id())
                .build();

        request.getContenedores().forEach(c -> {
            plan_request.contenedores
                    .add(new VisitaContenedor(c.getNumero_contenedor(), c.getInstruccion_numero(), c.getUbicacion()));
        });

        return plan_request;
    }

    public static PlanificacionRequestCommand CreateManualMovementFrom(UpdateManualLocationRequest request) {
        var plan_request = PlanificacionRequestCommand.builder()
                .usuario_alias(request.getUser_alias())
                .contenedores(new ArrayList<VisitaContenedor>())
                .build();

        request.getEquipments().forEach(equipment -> {
            plan_request.contenedores.add(new VisitaContenedor(
                    equipment.getEquipment_number(),
                    equipment.getMove_instruction_id(),
                    equipment.getLocation()));
        });

        return plan_request;
    }

    public static PlanificacionRequestCommand CrearDesde(ConfirmarMovimientoRequest request) {
        var plan_request = PlanificacionRequestCommand.builder()
                .unidad_negocio_id(request.getUnidad_negocio_id())
                .patio_codigo(request.getPatio_codigo())
                .usuario_id(request.getUsuario_id())
                .contenedores(new ArrayList<VisitaContenedor>())
                .build();

        request.getContenedores().forEach(c -> {
            var visita = new VisitaContenedor(c.getNumero_contenedor(), c.getPlaca(), c.getInstruccion_numero());
            visita.setUbicacion_destino(c.getUbicacion());
            plan_request.contenedores.add(visita);
        });

        return plan_request;
    }
}
