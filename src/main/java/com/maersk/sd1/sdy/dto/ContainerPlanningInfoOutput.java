package com.maersk.sd1.sdy.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import java.util.List;

@Data
public class ContainerPlanningInfoOutput {

    @JsonProperty("data")
    @JsonFormat(shape = JsonFormat.Shape.ARRAY)
    private List<ContainerPlanningInfoData> data;

    @Data
    public static class ContainerPlanningInfoData {
        @JsonProperty("numero_contenedor")
        private String numeroContenedor;

        @JsonProperty("contenedor_id")
        private Integer contenedorId;

        @JsonProperty("tamano")
        private Integer tamano;

        @JsonProperty("bloque_id")
        private Integer bloqueId;

        @JsonProperty("bloque_codigo")
        private String bloqueCodigo;

        @JsonProperty("bloque_tipo")
        private String bloqueTipo;

        @JsonProperty("celda_id")
        private Integer celdaId;

        @JsonProperty("fila")
        private String fila;

        @JsonProperty("columna")
        private String columna;

        @JsonProperty("indice_fila")
        private Integer indiceFila;

        @JsonProperty("indice_columna")
        private Integer indiceColumna;

        @JsonProperty("nivel_id")
        private Integer nivelId;

        @JsonProperty("nivel")
        private Integer nivel;
    }
}
