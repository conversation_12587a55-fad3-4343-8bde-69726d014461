package com.maersk.sd1.sdy.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.experimental.UtilityClass;

@UtilityClass
public class MovementInstructionPlanningInput {

    @Data
    public static class Input {

        @JsonProperty("cola_trabajo_id")
        @NotNull(message = "cola_trabajo_id cannot be null")
        private Long colaTrabajoId;

        @JsonProperty("horas_historico")
        @NotNull(message = "horas_historico cannot be null")
        private Integer horasHistorico;

        @JsonProperty("sub_unidad_negocio_local_id")
        private Long subUnidadNegocioLocalId;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("SDY")
        private Prefix prefix;
    }
}