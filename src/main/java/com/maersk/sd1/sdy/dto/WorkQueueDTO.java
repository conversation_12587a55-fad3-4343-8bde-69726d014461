package com.maersk.sd1.sdy.dto;

import com.maersk.sd1.common.model.WorkQueue;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.format.DateTimeFormatter;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class WorkQueueDTO {

    private Integer id;
    private Integer yardId;
    private String code;
    private String description;
    private Boolean active;

    private Integer registrationUserId;
    private String registrationDate;

    private Integer modificationUserId;
    private String modificationDate;

    private Integer userId;

    private Integer catStatusId;
    private String catStatusCode;
    private String catStatusDescription;

    private Integer workPointId;

    private Boolean byDefect;

    public static WorkQueueDTO fromEntity(WorkQueue entity) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss");

        return WorkQueueDTO.builder()
                .id(entity.getId())
                .yardId(entity.getYard() != null ? entity.getYard().getId() : null)
                .code(entity.getCode())
                .description(entity.getDescription())
                .active(entity.getActive())
                .registrationUserId(entity.getRegistrationUser() != null ? entity.getRegistrationUser().getId() : null)
                .registrationDate(entity.getRegistrationDate() != null ? entity.getRegistrationDate().format(formatter) : null)
                .modificationUserId(entity.getModificationUser() != null ? entity.getModificationUser().getId() : null)
                .modificationDate(entity.getModificationDate() != null ? entity.getModificationDate().format(formatter) : null)
                .userId(entity.getUser() != null ? entity.getUser().getId() : null)
                .catStatusId(entity.getCatStatus() != null ? entity.getCatStatus().getId() : null)
                .catStatusCode(entity.getCatStatus() != null ? entity.getCatStatus().getCode() : null)
                .catStatusDescription(entity.getCatStatus() != null ? entity.getCatStatus().getDescription() : null)
                .workPointId(entity.getWorkPoint() != null ? entity.getWorkPoint().getId() : null)
                .byDefect(entity.getByDefect())
                .build();
    }
}
