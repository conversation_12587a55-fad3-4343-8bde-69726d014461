package com.maersk.sd1.sdy.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

@Data
@JsonFormat (shape = JsonFormat.Shape.ARRAY)
public class BlockListOutput {

    @JsonProperty("total_registros")
    private List<List<Long>> totalRecords;

    @JsonProperty("bloques")
    private List<BlockListInfo> blocks;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @JsonFormat (shape = JsonFormat.Shape.ARRAY)
    public static class BlockListInfo {
        @JsonProperty("bloque_id")
        private Integer blockId;

        @JsonProperty("patio_id")
        private Integer yardId;

        @JsonProperty("cat_bloque_id")
        private Integer catBlockTypeId;

        @JsonProperty("codigo")
        private String code;

        @JsonProperty("nombre")
        private String name;

        @JsonProperty("filas")
        private Integer rows;

        @JsonProperty("columnas")
        private Integer columns;

        @JsonProperty("niveles")
        private Integer levels;

        @JsonProperty("tipo_limite_heap")
        private Integer typeHeapLimitId;

        @JsonProperty("cantidad_limite_heap")
        private Integer heapLimitQuantity;

        @JsonProperty("etiqueta_fila_20")
        private String row20Label;

        @JsonProperty("etiqueta_fila_40")
        private String row40Label;

        @JsonProperty("etiquetas_columna")
        private String columnLabels;

        @JsonProperty("configuracion")
        private String configuration;

        @JsonProperty("activo")
        private Boolean active;

        @JsonProperty("fecha_registro")
        @JsonSerialize(using = com.maersk.sd1.common.utils.LocalDateTimeEpochSerializer.class)
        private LocalDateTime registrationDate;

        @JsonProperty("fecha_modificacion")
        @JsonSerialize(using = com.maersk.sd1.common.utils.LocalDateTimeEpochSerializer.class)
        private LocalDateTime modificationDate;

        @JsonProperty("usuario_registro_nombres")
        private String registrationUserNames;

        @JsonProperty("usuario_registro_apellidos")
        private String registrationUserLastNames;

        @JsonProperty("usuario_modificacion_nombres")
        private String modificationUserNames;

        @JsonProperty("usuario_modificacion_apellidos")
        private String modificationUserLastNames;
    }
}
