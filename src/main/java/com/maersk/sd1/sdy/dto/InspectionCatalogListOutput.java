package com.maersk.sd1.sdy.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@JsonFormat(shape = JsonFormat.Shape.ARRAY)
public class InspectionCatalogListOutput {

    @JsonProperty("id")
    private Integer id;

    @JsonProperty("business_unit_id")
    private Integer businessUnitId;

    @JsonProperty("parent_catalog_id")
    private Integer parentCatalogId;

    @JsonProperty("description")
    private String description;

    @JsonProperty("long_description")
    private String longDescription;

    @JsonProperty("status")
    private Boolean status;

    @JsonProperty("registration_user_id")
    private Integer registrationUserId;

    @JsonProperty("registration_date")
    private LocalDateTime registrationDate;

    @JsonProperty("modification_user_id")
    private Integer modificationUserId;

    @JsonProperty("modification_date")
    private LocalDateTime modificationDate;

    @JsonProperty("variable_1")
    private String variable1;

    @JsonProperty("variable_2")
    private String variable2;

    @JsonProperty("variable_3")
    private Integer variable3;

    @JsonProperty("code")
    private String code;

    @JsonProperty("alias")
    private String alias;

    @JsonProperty("general_indicator")
    private Character generalIndicator;
}