package com.maersk.sd1.sdy.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ContainerLocationDetailsDTO {

    private Integer blockId;
    private Integer cellId;
    private Integer levelId;
    private Integer rowIndex;
    private Integer columnIndex;
    private Integer containerLocationId;
    private Integer containerId;
    private String containerNumber;
    private Integer containerSize;
}
