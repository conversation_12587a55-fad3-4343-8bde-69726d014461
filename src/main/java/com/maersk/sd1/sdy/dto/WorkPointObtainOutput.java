package com.maersk.sd1.sdy.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.maersk.sd1.sdy.serializer.WorkPointObtainOutputSerializer;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@JsonSerialize(using = WorkPointObtainOutputSerializer.class)
@JsonFormat(shape = JsonFormat.Shape.ARRAY)
public class WorkPointObtainOutput {

    @JsonProperty("work_point_id")
    private Integer workPointId;

    @JsonProperty("yard_id")
    private Integer yardId;

    @JsonProperty("code")
    private String code;

    @JsonProperty("description")
    private String description;

    @JsonProperty("active")
    private Boolean active;

    @JsonProperty("registration_date")
    private LocalDateTime registrationDate;

    @JsonProperty("modification_date")
    private LocalDateTime modificationDate;
}
