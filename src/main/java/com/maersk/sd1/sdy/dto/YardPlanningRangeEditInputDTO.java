package com.maersk.sd1.sdy.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor(access = lombok.AccessLevel.PRIVATE)
public class YardPlanningRangeEditInputDTO {

    @Data
    public static class Input {
        @JsonProperty("rango_regla_planificacion_patio_id")
        private Integer rangePlanningYardId;

        @JsonProperty("regla_planificacion_patio_id")
        private Integer planningYardRuleId;

        @JsonProperty("bloque_id")
        private Integer blockId;

        @JsonProperty("celda_id")
        private Integer cellId;

        @JsonProperty("nivel_id")
        private Integer levelId;

        @JsonProperty("indice_columna_desde")
        private Integer indexColumnSince;

        @JsonProperty("indice_columna_hasta")
        private Integer indexColumnUntil;

        @JsonProperty("indice_fila_desde")
        private Integer indexRowSince;

        @JsonProperty("indice_fila_hasta")
        private Integer indexRowUntil;

        @JsonProperty("activo")
        private String active;

        @JsonProperty("usuario_modificacion_id")
        private Integer userModificationId;

        @JsonProperty("direccion_stacking")
        private String directionStacking;

        @JsonProperty("indice_limite_stacking")
        private Integer indexLimitStacking;

        @JsonProperty("idioma_id")
        private Integer languageId;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private Input input;
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Root {
        @JsonProperty("SDY")
        private Prefix prefix;
    }
}
