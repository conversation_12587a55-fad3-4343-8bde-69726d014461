package com.maersk.sd1.sdy.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * Output DTO for planificacionRecursoColatrabajoBuscar.
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PlanificacionRecursoColatrabajoBuscarOutput {

    @JsonProperty("cola_trabajo_id")
    private Integer colaTrabajoId;

    @JsonProperty("cola_trabajo_codigo")
    private String colaTrabajoCodigo;

    @JsonProperty("patio_id")
    private Integer patioId;

    @JsonProperty("patio_codigo")
    private String patioCodigo;

    @JsonProperty("recurso_id")
    private Integer recursoId;

    @JsonProperty("recurso_codigo")
    private String recursoCodigo;

    @JsonProperty("recurso_tipo_codigo")
    private String recursoTipoCodigo;

    @JsonProperty("conductor_id")
    private Integer conductorId;

    @JsonProperty("conductor_nombres")
    private String conductorNombres;

    @JsonProperty("conductor_apellidos")
    private String conductorApellidos;

    @JsonProperty("last_job")
    private LocalDateTime lastJob;

    @JsonProperty("estado")
    private String estado;


}
