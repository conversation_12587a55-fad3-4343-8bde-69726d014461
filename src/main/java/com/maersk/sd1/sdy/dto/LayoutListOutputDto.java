package com.maersk.sd1.sdy.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class LayoutListOutputDto {

    @JsonProperty("plano_id")
    private Integer planoId;

    @JsonProperty("nombre")
    private String nombre;

    @JsonProperty("configuracion")
    private String configuracion;

    @JsonProperty("activo")
    private Boolean activo;

    @JsonProperty("map_settings")
    private String mapSettings;

    @JsonProperty("fecha_registro")
    @JsonSerialize(using = com.maersk.sd1.common.utils.LocalDateTimeEpochSerializer.class)
    private LocalDateTime fechaRegistro;

    @JsonProperty("fecha_modificacion")
    @JsonSerialize(using = com.maersk.sd1.common.utils.LocalDateTimeEpochSerializer.class)
    private LocalDateTime fechaModificacion;

    @JsonProperty("usuario_registro_id")
    private Integer usuarioRegistroId;

    @JsonProperty("usuario_modificacion_id")
    private Integer usuarioModificacionId;

    @JsonProperty("usuario_registro_nombres")
    private String usuarioRegistroNombres;

    @JsonProperty("usuario_registro_apellidos")
    private String usuarioRegistroApellidos;

    @JsonProperty("usuario_modificacion_nombres")
    private String usuarioModificacionNombres;

    @JsonProperty("usuario_modificacion_apellidos")
    private String usuarioModificacionApellidos;

    // Constructor to support direct instantiation from a JPQL query
    public LayoutListOutputDto(
            Integer planoId,
            String nombre,
            String configuracion,
            Boolean activo,
            String mapSettings,
            LocalDateTime fechaRegistro,
            LocalDateTime fechaModificacion,
            Integer usuarioRegistroId,
            Integer usuarioModificacionId,
            String usuarioRegistroNombres,
            String usuarioRegistroApellidos,
            String usuarioModificacionNombres,
            String usuarioModificacionApellidos
    ) {
        this.planoId = planoId;
        this.nombre = nombre;
        this.configuracion = configuracion;
        this.activo = activo;
        this.mapSettings = mapSettings;
        this.fechaRegistro = fechaRegistro;
        this.fechaModificacion = fechaModificacion;
        this.usuarioRegistroId = usuarioRegistroId;
        this.usuarioModificacionId = usuarioModificacionId;
        this.usuarioRegistroNombres = usuarioRegistroNombres;
        this.usuarioRegistroApellidos = usuarioRegistroApellidos;
        this.usuarioModificacionNombres = usuarioModificacionNombres;
        this.usuarioModificacionApellidos = usuarioModificacionApellidos;
    }

}