package com.maersk.sd1.sdy.dto;


import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

@Data
public class UpdateMoveInstructionLocationPlannedInputDTO {

        @JsonProperty("instruccion_movimiento_id")
        @NotNull
        private Integer instructionMovementId;

        @JsonProperty("bloque_codigo")
        @NotNull
        @Size(max = 10)
        private String blockCode;

        @JsonProperty("fila")
        @NotNull
        @Size(max = 10)
        private String row;

        @JsonProperty("columna")
        @NotNull
        @Size(max = 10)
        private String column;

        @JsonProperty("nivel_indice")
        @NotNull
        private Integer levelIndex;

        @JsonProperty("bloque40_codigo")
        @Size(max = 10)
        private String block40Code;

        @JsonProperty("fila40")
        @Size(max = 10)
        private String row40;

        @JsonProperty("columna40")
        @Size(max = 10)
        private String column40;

        @JsonProperty("nivel40_indice")
        private Integer level40Index;

        @JsonProperty("usuario_id")
        @NotNull
        private Integer userId;

}
