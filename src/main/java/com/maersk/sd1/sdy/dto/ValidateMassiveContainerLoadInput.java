package com.maersk.sd1.sdy.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.io.IOException;
import java.util.List;

@Data
@NoArgsConstructor(access = lombok.AccessLevel.PRIVATE)
public class ValidateMassiveContainerLoadInput {

    @Data
    public static class Record {
        @JsonProperty("container_number")
        @NotNull
        @Size(max = 20)
        private String containerNumber;

        @JsonProperty("block_code")
        @NotNull
        @Size(max = 100)
        private String blockCode;

        @JsonProperty("cell_row")
        @NotNull
        private Integer rowLabel;

        @JsonProperty("cell_column")
        @NotNull
        private String columnLabel;

        @JsonProperty("level_index")
        @NotNull
        private Integer levelIndex;
    }

    @Data
    public static class Input {

        @JsonProperty("sub_business_unit_local_id")
        private Integer subBusinessUnitLocalId;

        @JsonProperty("language_id")
        private Integer languageId;

        @JsonProperty("records")
        private List<Record> records;

        @JsonProperty("records")
        public void setRecordsFromJson(String recordsJson) {
            if (StringUtils.isNotBlank(recordsJson)) {
                try {
                    ObjectMapper mapper = new ObjectMapper();
                    this.records = mapper.readValue(recordsJson,
                            new TypeReference<>() {
                            });
                } catch (IOException e) {
                    throw new IllegalArgumentException("Invalid format for 'records' field", e);
                }
            }
        }
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("SDY")
        private Prefix prefix;
    }
}
