package com.maersk.sd1.sdy.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
@JsonFormat(shape = JsonFormat.Shape.ARRAY)
public class ReglaPlanificacionPatioDatosObtenerOutput {

    @JsonProperty("patio")
    @JsonFormat(shape = JsonFormat.Shape.ARRAY)
    private List<PatioOutputDto> patio;

    @JsonProperty("bloques")
    @JsonFormat(shape = JsonFormat.Shape.ARRAY)
    private List<BloqueOutputDto> bloques;

    @JsonProperty("total_registros")
    private List<List<Long>> totalRegistros;

    @JsonProperty("rangos")
    @JsonFormat(shape = JsonFormat.Shape.ARRAY)
    private List<RangoOutputDto> rangos;

    @Data
    public static class PatioOutputDto {
        @JsonProperty("patio_id")
        private Integer patioId;

        @JsonProperty("codigo")
        private String codigo;

        @JsonProperty("nombre")
        private String nombre;

        @JsonProperty("unidad_negocio_id")
        private Integer unidadNegocioId;
    }

    @Data
    public static class BloqueOutputDto {
        @JsonProperty("bloque_id")
        private Integer bloqueId;

        @JsonProperty("cat_bloque_id")
        private Integer catBloqueId;

        @JsonProperty("codigo")
        private String codigo;

        @JsonProperty("nombre")
        private String nombre;

        @JsonProperty("filas")
        private Integer filas;

        @JsonProperty("columnas")
        private Integer columnas;

        @JsonProperty("niveles")
        private Integer niveles;

        @JsonProperty("block_direction")
        private Boolean blockDirection;

        @JsonProperty("direccion_stacking")
        private Boolean direccionStacking;
    }

    @Data
    public static class RangoOutputDto {
        @JsonProperty("rango_regla_planificacion_patio_id")
        private Integer rangoReglaPlanificacionPatioId;

        @JsonProperty("bloque_id")
        private Integer bloqueId;

        @JsonProperty("indice_columna_desde")
        private Integer indiceColumnaDesde;

        @JsonProperty("indice_columna_hasta")
        private Integer indiceColumnaHasta;

        @JsonProperty("indice_fila_desde")
        private Integer indiceFilaDesde;

        @JsonProperty("indice_fila_hasta")
        private Integer indiceFilaHasta;

        @JsonProperty("activo")
        private Boolean activo;

        @JsonProperty("usuario_registro_id")
        private Integer usuarioRegistroId;

        @JsonProperty("fecha_registro")
        private LocalDateTime fechaRegistro;

        @JsonProperty("usuario_modificacion_id")
        private Integer usuarioModificacionId;

        @JsonProperty("fecha_modificacion")
        private LocalDateTime fechaModificacion;

        @JsonProperty("direccion_stacking")
        private Boolean direccionStacking;

        @JsonProperty("indice_limite_stacking")
        private Integer indiceLimiteStacking;

        @JsonProperty("usuario_registro_nombres")
        private String usuarioRegistroNombres;

        @JsonProperty("usuario_registro_apellidos")
        private String usuarioRegistroApellidos;

        @JsonProperty("usuario_modificacion_nombres")
        private String usuarioModificacionNombres;

        @JsonProperty("usuario_modificacion_apellidos")
        private String usuarioModificacionApellidos;
    }
}