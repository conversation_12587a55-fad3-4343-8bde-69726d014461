package com.maersk.sd1.sdy.service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import com.maersk.sd1.common.dto.SystemRuleMergedShippingLinesDTO;
import com.maersk.sd1.common.model.*;
import com.maersk.sd1.common.repository.*;
import com.maersk.sd1.sdy.dto.OutgoingContainerPlanningSearchInput;
import com.maersk.sd1.sdy.dto.OutgoingContainerPlanningSearchOutput;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class OutgoingContainerPlanningSearchService {

    private final ContainerRepository containerRepository;
    private final ContainerLocationRepository containerLocationRepository;
    private final BookingRepository bookingRepository;
    private final SystemRuleRepository systemRuleRepository;
    private final BusinessUnitRepository businessUnitRepository;
    private final EirRepository eirRepository;
    private final EirActivityZoneRepository eirActivityZoneRepository;
    private final ObjectMapper objectMapper;

    @Transactional(readOnly = true)
    public OutgoingContainerPlanningSearchOutput searchOutgoingContainerPlanning(OutgoingContainerPlanningSearchInput.Input input) {
        log.info("Starting outgoing container planning search with input: {}", input);

        try {
            boolean yardEnabled = validateYardAvailability(input.getUnidadNegocioId());

            ReferenceContainerLocation referenceLocation = null;
            if (input.getContenedorReferencia() != null && !input.getContenedorReferencia().trim().isEmpty()) {
                referenceLocation = getReferenceContainerLocation(input.getContenedorReferencia());
            }

            Integer shippingLineId = getShippingLineFromBooking(input.getBooking());

            List<SystemRuleMergedShippingLinesDTO.MergedDetail> mergedShippingLineIds = getMergedShippingLineIds();

            List<OutgoingContainerPlanningSearchOutput.PlanificacionContenedoresSalidaBuscarOutputData> containers =
                searchContainersWithCriteria(input, mergedShippingLineIds, referenceLocation, yardEnabled,shippingLineId);

            OutgoingContainerPlanningSearchOutput output = new OutgoingContainerPlanningSearchOutput();
            output.setContenedores(containers);

            log.info("Found {} containers for outgoing planning", containers.size());
            return output;

        } catch (Exception e) {
            log.error("Error in outgoing container planning search", e);
            throw new RuntimeException("Error searching outgoing container planning", e);
        }
    }

    private boolean validateYardAvailability(Integer unidadNegocioId) {
        try {
            Optional<BusinessUnit> businessUnit = businessUnitRepository.findById(unidadNegocioId);
            if (businessUnit.isEmpty()) {
                return true;
            }

            Integer parentBusinessUnitId = businessUnit.get().getParentBusinessUnit() != null ?
                businessUnit.get().getParentBusinessUnit().getId() : unidadNegocioId;

            String rule = systemRuleRepository.findRuleByIdAndBusinessUnitIdAndActiveTrue("sdy_disponibilidad_sde", parentBusinessUnitId);
            if (rule == null) {
                return true;
            }

            JsonNode ruleNode = objectMapper.readTree(rule);
            if (ruleNode.isArray()) {
                for (JsonNode node : ruleNode) {
                    if (node.has("unidad_negocio_id") && node.get("unidad_negocio_id").asInt() == unidadNegocioId) {
                        return node.has("activo") && node.get("activo").asBoolean();
                    }
                }
            }

            return true;
        } catch (Exception e) {
            log.warn("Error validating yard availability, defaulting to enabled", e);
            return true;
        }
    }

    private ReferenceContainerLocation getReferenceContainerLocation(String containerNumber) {
        Optional<ContainerLocation> location = containerLocationRepository
                .findReferenceContainerLocationByContainerNumber(containerNumber);

        if (location.isPresent()) {
            ContainerLocation loc = location.get();
            return ReferenceContainerLocation.builder()
                    .blockCode(loc.getBlock().getCode())
                    .columnIndex(loc.getCell().getColumnIndex())
                    .rowIndex(loc.getCell().getRowIndex())
                    .levelIndex(loc.getLevel().getIndex())
                    .build();
        }

        return null;
    }

    private Integer getShippingLineFromBooking(String bookingNumber) {
        Optional<Booking> booking = bookingRepository.findByBookingNumber(bookingNumber);
        return booking.map(b -> b.getShippingLine().getId()).orElse(null);
    }

    private List<SystemRuleMergedShippingLinesDTO.MergedDetail> getMergedShippingLineIds() {
        try {
            SystemRule maerskFusion = systemRuleRepository.findByAlias("sdy_lineas_grupo_maersk_fs");

            String maerskFusionRule=null;

            if(maerskFusion!=null) {
                maerskFusionRule= maerskFusion.getRule();
            }

            if(maerskFusionRule==null) {
                return List.of();
            }

            return objectMapper.readValue(maerskFusionRule,
                    new TypeReference<>() {
                    });

        } catch (Exception e) {
            throw new RuntimeException("Failed to parse merged shipping lines from system rule JSON", e);
        }
    }

    private List<OutgoingContainerPlanningSearchOutput.PlanificacionContenedoresSalidaBuscarOutputData>
        searchContainersWithCriteria(OutgoingContainerPlanningSearchInput.Input input,
                                   List<SystemRuleMergedShippingLinesDTO.MergedDetail> mergedShippingLineIds,
                                   ReferenceContainerLocation referenceLocation,
                                   boolean yardEnabled,
                                   Integer shippingLineId) {

        List<Integer> mergedShippingLineIdsList = mergedShippingLineIds.stream()
            .map(SystemRuleMergedShippingLinesDTO.MergedDetail::getLineId)
            .collect(Collectors.toList());

        List<Container> containers = findEligibleContainers(input, mergedShippingLineIdsList,shippingLineId);

        List<OutgoingContainerPlanningSearchOutput.PlanificacionContenedoresSalidaBuscarOutputData> result =
            containers.stream()
                .map(container -> convertToOutputData(container, referenceLocation))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        sortResults(result, referenceLocation, yardEnabled);

        if (input.getCantidadRegistros() != null && input.getCantidadRegistros() > 0) {
            result = result.stream()
                .limit(input.getCantidadRegistros())
                .collect(Collectors.toList());
        }

        return result;
    }

    private List<Container> findEligibleContainers(OutgoingContainerPlanningSearchInput.Input input,
                                                  List<Integer> mergedShippingLineIds,Integer shippingLineId) {

        return containerRepository.findEligibleContainersForOutgoingPlanning(
            input.getContenedorTamanoId(),
            input.getContenedorTipoId(),
            mergedShippingLineIds,
            input.getUnidadNegocioId(),
            input.getCargaMaxima(),
            input.getClaseId(),
            input.getTecnologiaId(),
            input.getContenedorOk(),
            input.getContenedorReferencia(), shippingLineId
        );
    }

    private OutgoingContainerPlanningSearchOutput.PlanificacionContenedoresSalidaBuscarOutputData
        convertToOutputData(Container container, ReferenceContainerLocation referenceLocation) {

        List<ContainerLocation> locations = containerLocationRepository
            .findByContainerIdAndActiveTrue(container.getId());

        if (locations.isEmpty()) {
            return null;
        }

        ContainerLocation location = locations.stream()
            .min(Comparator.comparing(cl -> cl.getCell().getRow()))
            .orElse(locations.getFirst());

        Integer daysPermanence = calculateDaysPermanence(container);

        Integer proximityToContainer = calculateProximity(location, referenceLocation);

        OutgoingContainerPlanningSearchOutput.PlanificacionContenedoresSalidaBuscarOutputData data =
            new OutgoingContainerPlanningSearchOutput.PlanificacionContenedoresSalidaBuscarOutputData();

        data.setContenedorId(container.getId());
        data.setNumeroContenedor(container.getContainerNumber());
        data.setTamanoCodigo(container.getCatSize().getDescription());
        data.setTipoCodigo(container.getCatContainerType().getDescription());
        data.setCargaMaxima(container.getMaximunPayload());
        data.setClaseCodigo(container.getCatGrade() != null ? container.getCatGrade().getDescription() : null);
        data.setTipoReeferCodigo(container.getCatReeferType() != null ? container.getCatReeferType().getDescription() : null);
        data.setLineaNaviera(container.getShippingLine().getShippingLineCompany());
        data.setOrigenBloque(location.getBlock().getCode());
        data.setOrigenColumna(location.getCell().getColumn());
        data.setOrigenFila(location.getCell().getRow());
        data.setOrigenNivel(location.getLevel().getIndex());
        data.setCantidadRemovidos(location.getQuantityRemoved());
        data.setDiasPermanencia(daysPermanence);
        data.setCercaniaAContenedor(proximityToContainer);

        String containerStatus = getContainerStatus(container);
        data.setEstadoContenedor(containerStatus);

        return data;
    }

    private Integer calculateDaysPermanence(Container container) {
        try {
            Optional<Eir> latestEir = eirRepository.findTopByContainer_IdOrderByIdDesc(container.getId());
            if (latestEir.isEmpty()) {
                return 0;
            }

            Optional<EirActivityZone> latestActivity = eirActivityZoneRepository
                .findTopByEir_IdOrderByIdDesc(latestEir.get().getId());

            if (latestActivity.isEmpty()) {
                return 0;
            }

            LocalDateTime startDate = latestActivity.get().getStartDate();
            return (int) ChronoUnit.DAYS.between(startDate, LocalDateTime.now());

        } catch (Exception e) {
            log.warn("Error calculating days of permanence for container {}", container.getId(), e);
            return 0;
        }
    }

    private Integer calculateProximity(ContainerLocation location, ReferenceContainerLocation referenceLocation) {
        if (referenceLocation == null) {
            return 0;
        }

        int columnDiff = Math.abs(referenceLocation.getColumnIndex() - location.getCell().getColumnIndex());
        int rowDiff = Math.abs(referenceLocation.getRowIndex() - location.getCell().getRowIndex());

        return (columnDiff * columnDiff) + (rowDiff * rowDiff);
    }

    private String getContainerStatus(Container container) {
        try {
            Optional<Eir> latestEir = eirRepository.findTopByContainer_IdOrderByIdDesc(container.getId());
            if (latestEir.isEmpty()) {
                return null;
            }

            Optional<EirActivityZone> latestActivity = eirActivityZoneRepository
                .findTopByEir_IdOrderByIdDesc(latestEir.get().getId());

            if (latestActivity.isEmpty()) {
                return null;
            }

            EirZone eirZone = latestActivity.get().getEirZone();
            if (eirZone != null && eirZone.getCatContainerZone() != null) {
                return eirZone.getCatContainerZone().getDescription();
            }

            return null;

        } catch (Exception e) {
            log.warn("Error getting container status for container {}", container.getId(), e);
            return null;
        }
    }

    private void sortResults(List<OutgoingContainerPlanningSearchOutput.PlanificacionContenedoresSalidaBuscarOutputData> results,
                           ReferenceContainerLocation referenceLocation, boolean yardEnabled) {

        if (referenceLocation != null && yardEnabled) {
            results.sort(Comparator
                .comparing(OutgoingContainerPlanningSearchOutput.PlanificacionContenedoresSalidaBuscarOutputData::getCercaniaAContenedor)
                .thenComparing(OutgoingContainerPlanningSearchOutput.PlanificacionContenedoresSalidaBuscarOutputData::getCantidadRemovidos)
                .thenComparing(OutgoingContainerPlanningSearchOutput.PlanificacionContenedoresSalidaBuscarOutputData::getDiasPermanencia));
        } else {
            Map<String, Double> blockAverageDays = results.stream()
                .collect(Collectors.groupingBy(
                    OutgoingContainerPlanningSearchOutput.PlanificacionContenedoresSalidaBuscarOutputData::getOrigenBloque,
                    Collectors.averagingInt(OutgoingContainerPlanningSearchOutput.PlanificacionContenedoresSalidaBuscarOutputData::getDiasPermanencia)
                ));

            results.sort(Comparator
                .comparing((OutgoingContainerPlanningSearchOutput.PlanificacionContenedoresSalidaBuscarOutputData data) ->
                    blockAverageDays.getOrDefault(data.getOrigenBloque(), 0.0))
                .thenComparing(OutgoingContainerPlanningSearchOutput.PlanificacionContenedoresSalidaBuscarOutputData::getCantidadRemovidos));
        }
    }

    @lombok.Builder
    @lombok.Data
    private static class ReferenceContainerLocation {
        private String blockCode;
        private Integer columnIndex;
        private Integer rowIndex;
        private Integer levelIndex;
    }
}
