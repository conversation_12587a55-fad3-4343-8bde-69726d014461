package com.maersk.sd1.sdy.service;

import com.maersk.sd1.common.repository.CatalogRepository;
import com.maersk.sd1.sdy.dto.InspectionCatalogListOutput;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Arrays;
import java.util.List;

@Service
@RequiredArgsConstructor
public class InspectionCatalogListService {

    private static final Logger logger = LogManager.getLogger(InspectionCatalogListService.class);

    private static final List<Integer> FIXED_IDS = Arrays.asList(49086, 43171, 48220);

    private final CatalogRepository catalogRepository;

    @Transactional(readOnly = true)
    public List<InspectionCatalogListOutput> getCatalogList() {
        InspectionCatalogListOutput output = new InspectionCatalogListOutput();
        try {
            // Retrieve the catalogs based on the fixed IDs from the stored procedure
            List<InspectionCatalogListOutput> catalogList = catalogRepository.findCatalogListByIdIn(FIXED_IDS);
            return catalogList;
        } catch (Exception e) {
            logger.error("Error retrieving catalogs", e);
        }
        return List.of(output);
    }
}
