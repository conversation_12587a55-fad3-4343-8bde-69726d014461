package com.maersk.sd1.sdy.service;

import com.maersk.sd1.sdy.dto.GroupCodeListInputDTO;
import com.maersk.sd1.sdy.dto.GroupCodeListOutputDTO;
import com.maersk.sd1.sdy.dto.GroupCodeListRecordDTO;
import com.maersk.sd1.common.repository.GroupCodeRepository;
import com.maersk.sd1.common.model.GroupCode;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.validation.Valid;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.List;

@Service
public class GroupCodeListService {

    private static final Logger logger = LogManager.getLogger(GroupCodeListService.class);

    @Autowired
    private GroupCodeRepository groupCodeRepository;

    /**
     * Service method to apply the filter logic from the stored procedure and fetch paged results
     * plus the total record count.
     */
    @Transactional(readOnly = true)
    public GroupCodeListOutputDTO listGroupCodes(@Valid GroupCodeListInputDTO.Input input) {
        logger.info("Entering listGroupCodes with input: {}", input);
        
        // Build the specification
        Specification<GroupCode> spec = buildSpecification(input);
        
        // If page or size not provided, fetch all records
        if (input.getPage() == null || input.getSize() == null || input.getPage() < 1 || input.getSize() < 1) {
            // Count total records first
            long totalCount = groupCodeRepository.count(spec);
            
            // Use Integer.MAX_VALUE or a reasonable large number to fetch all records in one page
            int pageSize = (totalCount > Integer.MAX_VALUE) ? Integer.MAX_VALUE : (int) totalCount;
            // Use at least 1 for page size to avoid errors
            pageSize = Math.max(pageSize, 1);
            
            PageRequest pageable = PageRequest.of(0, pageSize, Sort.by(Sort.Direction.DESC, "id"));
            Page<GroupCode> pageResult = groupCodeRepository.findAll(spec, pageable);
            
            List<GroupCodeListRecordDTO> recordDTOs = new ArrayList<>();
            for (GroupCode gc : pageResult.getContent()) {
                recordDTOs.add(mapEntityToRecordDTO(gc));
            }
            
            GroupCodeListOutputDTO outputDTO = new GroupCodeListOutputDTO();
            outputDTO.setTotalRegistros(List.of(List.of(pageResult.getTotalElements())));
            outputDTO.setRegistros(recordDTOs);
            
            logger.info("Exiting listGroupCodes. Retrieved all {} record(s).", recordDTOs.size());
            return outputDTO;
        }
        
        // Normal pagination when page and size are provided
        int pageNumber = input.getPage() - 1; // Convert to 0-based for PageRequest
        int pageSize = input.getSize();
        
        // Build pageable with sort by id desc
        PageRequest pageable = PageRequest.of(pageNumber, pageSize, Sort.by(Sort.Direction.DESC, "id"));
        Page<GroupCode> pageResult = groupCodeRepository.findAll(spec, pageable);
        
        List<GroupCodeListRecordDTO> recordDTOs = new ArrayList<>();
        for (GroupCode gc : pageResult.getContent()) {
            recordDTOs.add(mapEntityToRecordDTO(gc));
        }
        
        GroupCodeListOutputDTO outputDTO = new GroupCodeListOutputDTO();
        outputDTO.setTotalRegistros(List.of(List.of(pageResult.getTotalElements())));
        outputDTO.setRegistros(recordDTOs);
        
        logger.info("Exiting listGroupCodes. Retrieved {} record(s) from page {}.", recordDTOs.size(), pageNumber + 1);
        return outputDTO;
    }

    /**
     * Builds a dynamic JPA Specification based on the filters.
     */
    private Specification<GroupCode> buildSpecification(GroupCodeListInputDTO.Input input) {
        return (root, query, cb) -> {
            List<jakarta.persistence.criteria.Predicate> predicates = new ArrayList<>();

            // codigo_grupo_id => matches GroupCode.id
            if (input.getCodigoGrupoId() != null) {
                predicates.add(cb.equal(root.get("id"), input.getCodigoGrupoId()));
            }
            // patio_id => matches GroupCode.yard.id
            if (input.getPatioId() != null) {
                predicates.add(cb.equal(root.get("yard").get("id"), input.getPatioId()));
            }
            // codigo => partial match on GroupCode.code
            if (input.getCodigo() != null && !input.getCodigo().isEmpty()) {
                predicates.add(cb.like(root.get("code"), "%" + input.getCodigo() + "%"));
            }
            // nombre => partial match on GroupCode.name
            if (input.getNombre() != null && !input.getNombre().isEmpty()) {
                predicates.add(cb.like(root.get("name"), "%" + input.getNombre() + "%"));
            }
            // activo => exact match on GroupCode.active
            if (input.getActivo() != null) {
                predicates.add(cb.equal(root.get("active"), input.getActivo()));
            }

            // fecha_registro between [fechaRegistroMin, fechaRegistroMax]
            // If both are null, do not filter
            LocalDate frMin = input.getFechaRegistroMin();
            LocalDate frMax = input.getFechaRegistroMax();
            if (frMin != null || frMax != null) {
                if (frMin != null) {
                    // registrationDate >= frMin.atStartOfDay()
                    predicates.add(cb.greaterThanOrEqualTo(root.get("registrationDate"), frMin.atStartOfDay()));
                }
                if (frMax != null) {
                    // registrationDate < frMax plus 1 day
                    LocalDateTime frMaxPlusOne = frMax.plusDays(1).atStartOfDay();
                    predicates.add(cb.lessThan(root.get("registrationDate"), frMaxPlusOne));
                }
            }

            // fecha_modificacion between [fechaModificacionMin, fechaModificacionMax]
            // If both are null, do not filter
            LocalDate fmMin = input.getFechaModificacionMin();
            LocalDate fmMax = input.getFechaModificacionMax();
            if (fmMin != null || fmMax != null) {
                if (fmMin != null) {
                    predicates.add(cb.greaterThanOrEqualTo(root.get("modificationDate"), fmMin.atStartOfDay()));
                }
                if (fmMax != null) {
                    LocalDateTime fmMaxPlusOne = fmMax.plusDays(1).atStartOfDay();
                    predicates.add(cb.lessThan(root.get("modificationDate"), fmMaxPlusOne));
                }
            }

            return cb.and(predicates.toArray(new jakarta.persistence.criteria.Predicate[0]));
        };
    }

    /**
     * Maps the GroupCode entity (including user and yard details) to GroupCodeListRecordDTO.
     */
    private GroupCodeListRecordDTO mapEntityToRecordDTO(GroupCode gc) {
        GroupCodeListRecordDTO dto = new GroupCodeListRecordDTO();
        dto.setCodigoGrupoId(gc.getId());
        dto.setCodigo(gc.getCode());
        dto.setNombre(gc.getName());
        dto.setActivo(gc.getActive());
        dto.setFechaRegistro(gc.getRegistrationDate());
        dto.setFechaModificacion(gc.getModificationDate());

        if (gc.getYard() != null) {
            dto.setPatioId(gc.getYard().getId());
            dto.setPatioNombre(gc.getYard().getName());
        }

        if (gc.getRegistrationUser() != null) {
            dto.setUsuarioRegistroId(gc.getRegistrationUser().getId());
            dto.setUsuarioRegistroNombres(gc.getRegistrationUser().getNames());
            StringBuilder apellidos = new StringBuilder();
            if (gc.getRegistrationUser().getFirstLastName() != null) {
                apellidos.append(gc.getRegistrationUser().getFirstLastName());
            }
            if (gc.getRegistrationUser().getSecondLastName() != null) {
                if (apellidos.length() > 0) {
                    apellidos.append(" ");
                }
                apellidos.append(gc.getRegistrationUser().getSecondLastName());
            }
            dto.setUsuarioRegistroApellidos(apellidos.toString());
        }

        if (gc.getModificationUser() != null) {
            dto.setUsuarioModificacionId(gc.getModificationUser().getId());
            dto.setUsuarioModificacionNombres(gc.getModificationUser().getNames());
            StringBuilder apellidos = new StringBuilder();
            if (gc.getModificationUser().getFirstLastName() != null) {
                apellidos.append(gc.getModificationUser().getFirstLastName());
            }
            if (gc.getModificationUser().getSecondLastName() != null) {
                if (apellidos.length() > 0) {
                    apellidos.append(" ");
                }
                apellidos.append(gc.getModificationUser().getSecondLastName());
            }
            dto.setUsuarioModificacionApellidos(apellidos.toString());
        }

        return dto;
    }
}
