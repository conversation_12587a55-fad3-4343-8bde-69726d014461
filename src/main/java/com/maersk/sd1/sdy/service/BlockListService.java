package com.maersk.sd1.sdy.service;

import com.maersk.sd1.common.repository.BlockRepository;
import com.maersk.sd1.sdy.dto.BlockListInput;
import com.maersk.sd1.sdy.dto.BlockListOutput;
import com.maersk.sd1.sdy.dto.BlockListOutput.BlockListInfo;
import com.maersk.sd1.common.model.Block;
import com.maersk.sd1.common.model.User;
import jakarta.persistence.criteria.Predicate;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@RequiredArgsConstructor
@Service
public class BlockListService {

    private static final Logger logger = LogManager.getLogger(BlockListService.class);

    private final BlockRepository blockRepository;

    public BlockListOutput listBlocks(BlockListInput.Input input) {
        logger.info("Entering listBlocks with input: {}", input);
        Specification<Block> spec = buildSpecificationForBlock(input);

        long total = blockRepository.count(spec);

        Integer pageNumber = (input.getPage() == null) ? 1 : input.getPage();

        int sizeValue;
        if (input.getSize() == null) {
            sizeValue = (total == 0) ? 1 : (int) total;
        } else {
            sizeValue = input.getSize();
        }

        Pageable pageable = PageRequest.of(Math.max(pageNumber - 1, 0), Math.max(sizeValue, 1), Sort.by("id").descending());

        Page<Block> pageBlocks = blockRepository.findAll(spec, pageable);

        List<BlockListInfo> blockListInfos = new ArrayList<>();
        for (Block b : pageBlocks.getContent()) {
            BlockListInfo info = new BlockListInfo();
            info.setBlockId(b.getId());
            if (b.getYard() != null) {
                info.setYardId(b.getYard().getId());
            }
            if (b.getCatBlockType() != null) {
                info.setCatBlockTypeId(b.getCatBlockType().getId());
            }
            info.setCode(b.getCode());
            info.setName(b.getName());
            info.setRows(b.getRows());
            info.setColumns(b.getColumns());
            info.setLevels(b.getLevels());
            if (b.getCatHeapLimitType() != null) {
                info.setTypeHeapLimitId(b.getCatHeapLimitType().getId());
            }
            info.setHeapLimitQuantity(b.getHeapLimitQuantity());
            info.setRow20Label(b.getRow20Label());
            info.setRow40Label(b.getRow40label());
            info.setColumnLabels(b.getColumnLabels());
            info.setConfiguration(b.getConfiguration());
            info.setActive(b.getActive());
            info.setRegistrationDate(b.getRegistrationDate());
            info.setModificationDate(b.getModificationDate());

            if (b.getRegistrationUser() != null) {
                User rUser = b.getRegistrationUser();
                info.setRegistrationUserNames(rUser.getNames());
                String lastNames = rUser.getFirstLastName();
                if (rUser.getSecondLastName() != null) {
                    lastNames += " " + rUser.getSecondLastName();
                }
                info.setRegistrationUserLastNames(lastNames);
            }

            if (b.getModificationUser() != null) {
                User mUser = b.getModificationUser();
                info.setModificationUserNames(mUser.getNames());
                String modLastNames = (mUser.getFirstLastName() != null) ? mUser.getFirstLastName() : "";
                if (mUser.getSecondLastName() != null) {
                    modLastNames += " " + mUser.getSecondLastName();
                }
                info.setModificationUserLastNames(modLastNames);
            }

            blockListInfos.add(info);
        }

        BlockListOutput output = new BlockListOutput();
        output.setTotalRecords(List.of(List.of(total)));
        output.setBlocks(blockListInfos);

        logger.info("Exiting listBlocks with total found: {}", total);
        return output;
    }

    private Specification<Block> buildSpecificationForBlock(BlockListInput.Input input) {
        return (root, query, cb) -> {
            List<Predicate> predicates = new ArrayList<>();

            if (input.getBlockId() != null) {
                predicates.add(cb.equal(root.get("id"), input.getBlockId()));
            }

            if (input.getYardId() != null) {
                predicates.add(cb.equal(root.get("yard").get("id"), input.getYardId()));
            }

            if (input.getCatBlockTypeId() != null) {
                predicates.add(cb.equal(root.get("catBlockType").get("id"), input.getCatBlockTypeId()));
            }

            if (input.getCode() != null && !input.getCode().trim().isEmpty()) {
                predicates.add(cb.like(root.get("code"), "%" + input.getCode().trim() + "%"));
            }

            if (input.getName() != null && !input.getName().trim().isEmpty()) {
                predicates.add(cb.like(root.get("name"), "%" + input.getName().trim() + "%"));
            }

            if (input.getRows() != null) {
                predicates.add(cb.equal(root.get("rows"), input.getRows()));
            }

            if (input.getColumns() != null) {
                predicates.add(cb.equal(root.get("columns"), input.getColumns()));
            }

            if (input.getLevels() != null) {
                predicates.add(cb.equal(root.get("levels"), input.getLevels()));
            }

            if (input.getTypeHeapLimitId() != null) {
                predicates.add(cb.equal(root.get("catHeapLimitType").get("id"), input.getTypeHeapLimitId()));
            }

            if (input.getHeapLimitQuantity() != null) {
                predicates.add(cb.equal(root.get("heapLimitQuantity"), input.getHeapLimitQuantity()));
            }

            if (input.getRow20Label() != null && !input.getRow20Label().trim().isEmpty()) {
                predicates.add(cb.like(root.get("row20Label"), "%" + input.getRow20Label().trim() + "%"));
            }

            if (input.getRow40Label() != null && !input.getRow40Label().trim().isEmpty()) {
                predicates.add(cb.like(root.get("row40label"), "%" + input.getRow40Label().trim() + "%"));
            }

            if (input.getColumnLabels() != null && !input.getColumnLabels().trim().isEmpty()) {
                predicates.add(cb.like(root.get("columnLabels"), "%" + input.getColumnLabels().trim() + "%"));
            }

            if (input.getConfiguration() != null && !input.getConfiguration().trim().isEmpty()) {
                predicates.add(cb.like(root.get("configuration"), "%" + input.getConfiguration().trim() + "%"));
            }

            if (input.getActive() != null) {
                predicates.add(cb.equal(root.get("active"), input.getActive()));
            }

            if (input.getRegistrationDateMin() != null && input.getRegistrationDateMax() != null) {
                LocalDateTime start = input.getRegistrationDateMin().atStartOfDay();
                LocalDateTime end = input.getRegistrationDateMax().plusDays(1).atStartOfDay();
                predicates.add(cb.between(root.get("registrationDate"), start, end));
            }

            if (input.getModificationDateMin() != null && input.getModificationDateMax() != null) {
                LocalDateTime start = input.getModificationDateMin().atStartOfDay();
                LocalDateTime end = input.getModificationDateMax().plusDays(1).atStartOfDay();
                predicates.add(cb.between(root.get("modificationDate"), start, end));
            }

            return cb.and(predicates.toArray(new Predicate[0]));
        };
    }
}
