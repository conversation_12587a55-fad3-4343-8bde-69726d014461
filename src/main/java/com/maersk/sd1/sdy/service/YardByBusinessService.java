package com.maersk.sd1.sdy.service;

import com.maersk.sd1.common.model.Yard;
import com.maersk.sd1.sdy.dto.YardByBusinessOutput;
import com.maersk.sd1.common.repository.YardRepository;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Log4j2
@Service
public class YardByBusinessService {

    @Autowired
    private YardRepository yardRepository;

    public YardByBusinessOutput getYardsByBusinessUnitId(Integer businessUnitId) {
        YardByBusinessOutput output = new YardByBusinessOutput();
        try {
            List<Yard> yardList = yardRepository.findByBusinessUnit_IdAndActiveTrue(businessUnitId);
            List<YardByBusinessOutput.YardDTO> dtoList = yardList.stream().map(y -> {
                YardByBusinessOutput.YardDTO dto = new YardByBusinessOutput.YardDTO();
                dto.setId(y.getId());
                // Carefully handle null layout references
                dto.setLayoutId(y.getLayout() != null ? y.getLayout().getId() : null);
                dto.setCode(y.getCode());
                dto.setName(y.getName());
                dto.setZoom(y.getZoom());
                dto.setLatitude(y.getLatitude());
                dto.setColor(y.getColor());
                dto.setLongitude(y.getLongitude());
                dto.setConfiguration(y.getConfiguration());
                dto.setActive(y.getActive());
                dto.setRegistrationDate(y.getRegistrationDate());
                dto.setModificationDate(y.getModificationDate());
                return dto;
            }).collect(Collectors.toList());

            output.setRespEstado(1);
            output.setRespMensaje("Success");
            output.setPatios(dtoList);
        } catch (Exception e) {
            log.error("Error while fetching yards by business unit.", e);
            output.setRespEstado(0);
            output.setRespMensaje(e.getMessage());
        }
        return output;
    }
}
