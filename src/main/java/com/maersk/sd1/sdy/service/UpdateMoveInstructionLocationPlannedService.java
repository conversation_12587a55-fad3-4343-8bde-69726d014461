package com.maersk.sd1.sdy.service;

import com.maersk.sd1.common.model.*;
import com.maersk.sd1.common.repository.*;
import com.maersk.sd1.sdy.dto.UpdateMoveInstructionLocationPlannedOutputDTO;
import com.maersk.sd1.sdy.dto.UpdateMoveInstructionLocationPlannedInputDTO;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Service
@RequiredArgsConstructor
public class UpdateMoveInstructionLocationPlannedService {

    private static final Logger logger = LogManager.getLogger(UpdateMoveInstructionLocationPlannedService.class);

    private final MovementInstructionRepository movementInstructionRepository;
    private final ContainerLocationRepository containerLocationRepository;
    private final BlockRepository blockRepository;
    private final CellRepository cellRepository;
    private final LevelRepository levelRepository;

    @Transactional
    public UpdateMoveInstructionLocationPlannedOutputDTO updateMoveInstructionLocationPlanned(List<UpdateMoveInstructionLocationPlannedInputDTO> request) {
        UpdateMoveInstructionLocationPlannedOutputDTO response = new UpdateMoveInstructionLocationPlannedOutputDTO();
        try {

            for (UpdateMoveInstructionLocationPlannedInputDTO input : request) {
                processSingleInstruction(input);
            }

            response.setRespEstado(1);
            response.setRespMensaje("Success");
        } catch (Exception e) {
            logger.error("Error while processing updateMoveInstructionLocationPlanned:", e);
            response.setRespEstado(0);
            response.setRespMensaje(e.getMessage());
        }
        return response;
    }

    private void processSingleInstruction(UpdateMoveInstructionLocationPlannedInputDTO input) {
        MovementInstruction movementInstruction = movementInstructionRepository.findById(input.getInstructionMovementId())
                .orElseThrow(() -> new IllegalArgumentException("MovementInstruction not found for ID: " + input.getInstructionMovementId()));

        ContainerLocation normalLocation = findOrCreateContainerLocation(
                movementInstruction,
                input.getBlockCode(),
                input.getRow(),
                input.getColumn(),
                input.getLevelIndex(),
                input.getUserId()
        );

        ContainerLocation block40Location = findContainerLocationOnly(
                input.getBlock40Code(),
                input.getRow40(),
                input.getColumn40(),
                input.getLevel40Index()
        );

        movementInstruction.setDestinationBlock(normalLocation.getBlock());
        movementInstruction.setDestinationCell(normalLocation.getCell());
        movementInstruction.setDestinationLevel(normalLocation.getLevel());

        if (block40Location != null) {
            movementInstruction.setDestination40Block(block40Location.getBlock());
            movementInstruction.setDestination40Cell(block40Location.getCell());
            movementInstruction.setDestination40Level(block40Location.getLevel());
        } else {
            movementInstruction.setDestination40Block(null);
            movementInstruction.setDestination40Cell(null);
            movementInstruction.setDestination40Level(null);
        }

        movementInstruction.setModificationUser(new User(input.getUserId()));
        movementInstruction.setModificationDate(LocalDateTime.now());

        movementInstructionRepository.save(movementInstruction);
    }

    private ContainerLocation findOrCreateContainerLocation(MovementInstruction movementInstruction,
                                                            String blockCode,
                                                            String row,
                                                            String column,
                                                            Integer levelIndex,
                                                            Integer userId) {
        Optional<ContainerLocation> optionalLoc = containerLocationRepository.findLocationByBlockCellLevel(blockCode, row, column, levelIndex);
        if (optionalLoc.isPresent()) {
            return optionalLoc.get();
        }
        Block block = blockRepository.findBlockForInstruction(movementInstruction.getId(), blockCode)
                .orElseThrow(() -> new IllegalArgumentException("Block not found for code " + blockCode + " and instruction " + movementInstruction.getId()));

        Cell cell = cellRepository.findByBlockAndRowColumn(block.getId(), row, column)
                .orElseThrow(() -> new IllegalArgumentException("Cell not found for blockId=" + block.getId() + ", row=" + row + ", column=" + column));

        Level lvl = levelRepository.findByCellAndIndex(cell.getId(), levelIndex)
                .orElseThrow(() -> new IllegalArgumentException("Level not found for cellId=" + cell.getId() + ", levelIndex=" + levelIndex));

        ContainerLocation location = new ContainerLocation();
        location.setId(null);
        location.setBlock(block);
        location.setCell(cell);
        location.setLevel(lvl);
        location.setContainer(null);
        location.setVisitId(null);
        location.setActive(true);
        location.setRegistrationUser(new User(userId));
        location.setRegistrationDate(LocalDateTime.now());
        location.setModificationUser(null);
        location.setModificationDate(null);
        location.setQuantityRemoved(0);

        return containerLocationRepository.save(location);
    }

    private ContainerLocation findContainerLocationOnly(String block40Code, String row40, String column40, Integer level40Index) {
        if (block40Code == null || block40Code.trim().isEmpty() ||
                row40 == null || row40.trim().isEmpty() ||
                column40 == null || column40.trim().isEmpty() ||
                level40Index == null) {
            return null;
        }
        return containerLocationRepository.findLocationByBlockCellLevel(block40Code, row40, column40, level40Index).orElse(null);
    }
}
