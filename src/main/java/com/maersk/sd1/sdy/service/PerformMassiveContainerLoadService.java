package com.maersk.sd1.sdy.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.maersk.sd1.common.model.MovementInstruction;
import com.maersk.sd1.common.model.User;
import com.maersk.sd1.common.model.Container;
import com.maersk.sd1.common.model.Yard;
import com.maersk.sd1.common.model.Catalog;
import com.maersk.sd1.common.model.WorkQueue;
import com.maersk.sd1.common.model.Eir;
import com.maersk.sd1.common.model.Block;
import com.maersk.sd1.common.model.Cell;
import com.maersk.sd1.common.model.Level;
import com.maersk.sd1.common.model.Truck;
import com.maersk.sd1.common.repository.*;
import com.maersk.sd1.sdy.dto.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Service
@RequiredArgsConstructor
@Slf4j
public class PerformMassiveContainerLoadService {

    private static final Logger logger = LogManager.getLogger(PerformMassiveContainerLoadService.class);

    private final YardRepository yardRepository;
    private final CatalogRepository catalogRepository;
    private final BlockRepository blockRepository;
    private final WorkQueueRepository workQueueRepository;
    private final MovementInstructionRepository movementInstructionRepository;
    private final ContainerLocationRepository containerLocationRepository;
    private final EirRepository eirRepository;
    private final ValidateMassiveContainerLoadService validateLoadService;
    private final ValidateMassiveContainerRemovementService validateRemovalService;
    private final ObjectMapper objectMapper;

    @Transactional
    public PerformMassiveContainerLoadOutput performMassiveContainerLoad(PerformMassiveContainerLoadInput.Input input) {
        log.info("Starting massive container load operation for subBusinessUnitId: {}, userId: {}",
                input.getSubBusinessUnitLocalId(), input.getUserId());
        log.debug("Input containers to register: {}", input.getContainersRegister());
        log.debug("Input containers to remove: {}", input.getContainersRemove());

        try {
            // Step 1: Get yard configuration
            log.info("Step 1: Getting yard configuration for subBusinessUnitId: {}", input.getSubBusinessUnitLocalId());
            YardConfigurationDTO yardConfig = getYardConfiguration(input.getSubBusinessUnitLocalId());
            if (yardConfig == null) {
                log.error("Failed to get yard configuration for subBusinessUnitId: {}", input.getSubBusinessUnitLocalId());
                return createErrorResponse("Invalid Yard configuration");
            }
            log.info("Yard configuration retrieved successfully - yardId: {}, workQueueId: {}",
                    yardConfig.getCurrentYardId(), yardConfig.getDefaultWorkQueueId());

            // Step 2: Get catalog configuration
            log.info("Step 2: Getting catalog configuration");
            CatalogConfigurationDTO catalogConfig = getCatalogConfiguration();
            log.debug("Catalog configuration retrieved: {}", catalogConfig);

            // Step 3: Get virtual block configurations
            log.info("Step 3: Getting virtual block configurations for yardId: {}", yardConfig.getCurrentYardId());
            VirtualBlockConfigDTO onTruckConfig = getOnTruckBlockConfig(yardConfig.getCurrentYardId(), catalogConfig.getCatVirtualBlockId());
            VirtualBlockConfigDTO outConfig = getOutBlockConfig(yardConfig.getCurrentYardId(), catalogConfig.getCatVirtualBlockId());

            if (onTruckConfig == null || outConfig == null || yardConfig.getDefaultWorkQueueId() == null) {
                log.error("Invalid yard configuration - onTruckConfig: {}, outConfig: {}, workQueueId: {}",
                        onTruckConfig, outConfig, yardConfig.getDefaultWorkQueueId());
                return createErrorResponse("Invalid Yard configuration");
            }
            log.info("Virtual block configurations retrieved - OnTruck: {}, Out: {}", onTruckConfig, outConfig);

            // Step 4: Validate container operations
            log.info("Step 4: Validating container operations");
            String validationMessage = validateContainerOperations(input);
            if (validationMessage != null) {
                log.warn("Container validation failed: {}", validationMessage);
                return createErrorResponse(validationMessage);
            }
            log.info("Container validation completed successfully");

            // Step 5: Execute container movements
            log.info("Step 5: Executing container movements");
            executeContainerMovements(input, yardConfig, catalogConfig, onTruckConfig, outConfig);

            log.info("Massive container load operation completed successfully for subBusinessUnitId: {}",
                    input.getSubBusinessUnitLocalId());
            return PerformMassiveContainerLoadOutput.builder()
                    .respStatus(1)
                    .respMessage("Executed correctly")
                    .build();

        } catch (Exception e) {
            log.error("Error performing massive container load for subBusinessUnitId: {}, error: {}",
                    input.getSubBusinessUnitLocalId(), e.getMessage(), e);
            return PerformMassiveContainerLoadOutput.builder()
                    .respStatus(0)
                    .respMessage("Error: " + e.getMessage())
                    .build();
        }
    }

    private YardConfigurationDTO getYardConfiguration(Integer subBusinessUnitLocalId) {
        log.debug("Getting yard configuration for subBusinessUnitLocalId: {}", subBusinessUnitLocalId);

        Integer currentYardId = yardRepository.findCurrentYardIdBySubBusinessUnit(subBusinessUnitLocalId);
        if (currentYardId == null) {
            log.warn("No active yard found for subBusinessUnitLocalId: {}", subBusinessUnitLocalId);
            return null;
        }
        log.debug("Found current yard ID: {} for subBusinessUnitLocalId: {}", currentYardId, subBusinessUnitLocalId);

        Integer defaultWorkQueueId = workQueueRepository.findDefaultWorkQueueIdByYard(currentYardId);
        log.debug("Found default work queue ID: {} for yard ID: {}", defaultWorkQueueId, currentYardId);

        YardConfigurationDTO config = YardConfigurationDTO.builder()
                .currentYardId(currentYardId)
                .defaultWorkQueueId(defaultWorkQueueId)
                .build();

        log.debug("Yard configuration created: {}", config);
        return config;
    }

    private CatalogConfigurationDTO getCatalogConfiguration() {
        log.debug("Getting catalog configuration");

        CatalogConfigurationDTO config = CatalogConfigurationDTO.builder()
                .catVirtualBlockId(catalogRepository.findIdByAlias("sd1_block_type_virtual"))
                .movementStatusExecuted(catalogRepository.findIdByAlias("42896"))
                .eirCatOriginPositioningId(catalogRepository.findIdByAlias("43094"))
                .eirCatInstructionMovementLocationId(catalogRepository.findIdByAlias("47833"))
                .eirCatTypeMovementIncomeId(catalogRepository.findIdByAlias("43080"))
                .eirCatInstructionMovementRemovedId(catalogRepository.findIdByAlias("42906"))
                .eirCatTypeMovementExitId(catalogRepository.findIdByAlias("43081"))
                .cancelledMovementInstructionStatus(catalogRepository.findIdByAlias("42900"))
                .build();

        log.debug("Catalog configuration created: {}", config);
        return config;
    }

    private VirtualBlockConfigDTO getOnTruckBlockConfig(Integer yardId, Integer catVirtualBlockId) {
        log.debug("Getting OnTruck virtual block config for yardId: {}, catVirtualBlockId: {}", yardId, catVirtualBlockId);

        Optional<VirtualBlockConfigDTO> config = blockRepository.findOnTruckVirtualBlockConfig(yardId, catVirtualBlockId);
        if (config.isPresent()) {
            log.debug("OnTruck virtual block config found: {}", config.get());
        } else {
            log.warn("OnTruck virtual block config not found for yardId: {}, catVirtualBlockId: {}", yardId, catVirtualBlockId);
        }
        return config.orElse(null);
    }

    private VirtualBlockConfigDTO getOutBlockConfig(Integer yardId, Integer catVirtualBlockId) {
        log.debug("Getting Out virtual block config for yardId: {}, catVirtualBlockId: {}", yardId, catVirtualBlockId);

        Optional<VirtualBlockConfigDTO> config = blockRepository.findOutVirtualBlockConfig(yardId, catVirtualBlockId);
        if (config.isPresent()) {
            log.debug("Out virtual block config found: {}", config.get());
        } else {
            log.warn("Out virtual block config not found for yardId: {}, catVirtualBlockId: {}", yardId, catVirtualBlockId);
        }
        return config.orElse(null);
    }

    private String validateContainerOperations(PerformMassiveContainerLoadInput.Input input) {
        log.debug("Starting container operations validation");

        // Call validation services for loading containers
        if (input.getContainersRegister() != null && !input.getContainersRegister().isEmpty()) {
            log.info("Validating container load operations for {} characters of JSON data",
                    input.getContainersRegister().length());

            ValidateMassiveContainerLoadInput.Input loadInput = new ValidateMassiveContainerLoadInput.Input();
            loadInput.setSubBusinessUnitLocalId(input.getSubBusinessUnitLocalId());
            loadInput.setLanguageId(1); // Default language
            loadInput.setRecordsFromJson(input.getContainersRegister());

            List<ValidateMassiveContainerLoadOutput> loadResults = validateLoadService.validateMassiveContainerLoad(loadInput);
            log.debug("Container load validation returned {} results", loadResults.size());

            long errorCount = loadResults.stream().mapToLong(result -> result.hasValidationErrors() ? 1 : 0).sum();
            if (errorCount > 0) {
                log.warn("Container load validation failed with {} errors out of {} containers", errorCount, loadResults.size());
                return "Invalid movement set (Container load validation failed)";
            }
            log.info("Container load validation passed for all {} containers", loadResults.size());
        }

        if (input.getContainersRemove() != null && !input.getContainersRemove().isEmpty()) {
            log.info("Validating container removal operations");

            // Parse containers JSON to List<String>
            List<String> containerNumbers = parseContainersJson(input.getContainersRemove());
            log.debug("Parsed {} container numbers for removal validation", containerNumbers.size());

            List<ValidateMassiveContainerRemovementOutput> removeResults = validateRemovalService.validateMassiveContainerRemovement(
                    input.getSubBusinessUnitLocalId(), 1, containerNumbers);
            log.debug("Container removal validation returned {} results", removeResults.size());

            long errorCount = removeResults.stream().mapToLong(result -> hasRemovalValidationErrors(result) ? 1 : 0).sum();
            if (errorCount > 0) {
                log.warn("Container removal validation failed with {} errors out of {} containers", errorCount, removeResults.size());
                return "Invalid movement set (Container removal validation failed)";
            }
            log.info("Container removal validation passed for all {} containers", removeResults.size());
        }

        log.debug("All container operations validation completed successfully");
        return null; // No validation errors
    }

    private void executeContainerMovements(PerformMassiveContainerLoadInput.Input input,
                                           YardConfigurationDTO yardConfig,
                                           CatalogConfigurationDTO catalogConfig,
                                           VirtualBlockConfigDTO onTruckConfig,
                                           VirtualBlockConfigDTO outConfig) {

        log.info("Executing container movements for userId: {}", input.getUserId());

        // Step 1: Get validation results for movement instructions
        log.info("Step 1: Creating movement instructions");
        List<MovementInstructionDTO> loadInstructions = createLoadMovementInstructions(
                input.getContainersRegister(), yardConfig, catalogConfig, onTruckConfig);
        log.info("Created {} load movement instructions", loadInstructions.size());

        List<MovementInstructionDTO> removeInstructions = createRemoveMovementInstructions(
                input.getContainersRemove(), yardConfig, catalogConfig, outConfig);
        log.info("Created {} remove movement instructions", removeInstructions.size());

        // Step 2: Cancel pending movement instructions
        log.info("Step 2: Cancelling pending movement instructions");
        cancelPendingMovementInstructions(loadInstructions, catalogConfig.getCancelledMovementInstructionStatus(), input.getUserId());

        // Step 3: Insert new movement instructions
        log.info("Step 3: Inserting new movement instructions");
        insertMovementInstructions(loadInstructions, input.getUserId());
        insertMovementInstructions(removeInstructions, input.getUserId());

        // Step 4: Update container locations
        log.info("Step 4: Updating container locations");
        updateContainerLocations(loadInstructions, removeInstructions, outConfig.getBlockId(), input.getUserId());

        log.info("Container movements execution completed successfully");
    }

    private List<MovementInstructionDTO> createLoadMovementInstructions(String containersJson,
                                                                        YardConfigurationDTO yardConfig,
                                                                        CatalogConfigurationDTO catalogConfig,
                                                                        VirtualBlockConfigDTO onTruckConfig) {
        log.debug("Creating load movement instructions from containers JSON");

        if (containersJson == null || containersJson.isEmpty()) {
            log.debug("No containers JSON provided for load instructions");
            return List.of();
        }

        // Get validation results from the validation service
        log.debug("Preparing validation input for load containers");
        ValidateMassiveContainerLoadInput.Input loadInput = new ValidateMassiveContainerLoadInput.Input();
        loadInput.setSubBusinessUnitLocalId(yardConfig.getCurrentYardId());
        loadInput.setLanguageId(1);
        loadInput.setRecordsFromJson(containersJson);

        List<ValidateMassiveContainerLoadOutput> validationResults = validateLoadService.validateMassiveContainerLoad(loadInput);
        log.debug("Received {} validation results for load containers", validationResults.size());

        List<MovementInstructionDTO> instructions = new ArrayList<>();

        for (ValidateMassiveContainerLoadOutput result : validationResults) {
            if (result.getContainerId() != null) {
                log.debug("Creating load movement instruction for container ID: {}", result.getContainerId());
                MovementInstructionDTO instruction = createLoadMovementInstruction(result, yardConfig, catalogConfig, onTruckConfig);
                instructions.add(instruction);
            } else {
                log.warn("Skipping load instruction creation for container with null ID: {}", result.getContainerNumber());
            }
        }

        log.debug("Created {} load movement instructions", instructions.size());
        return instructions;
    }

    private MovementInstructionDTO createLoadMovementInstruction(ValidateMassiveContainerLoadOutput result,
                                                                 YardConfigurationDTO yardConfig,
                                                                 CatalogConfigurationDTO catalogConfig,
                                                                 VirtualBlockConfigDTO onTruckConfig) {
        log.debug("Creating load movement instruction for container: {} (ID: {})",
                result.getContainerNumber(), result.getContainerId());

        // Parse current location JSON
        LocationInfo currentLocation = parseLocationJson(result.getContainerCurrentLocation());
        LocationInfo destinyLocation = parseLocationJson(result.getContainerDestinyLocation());
        log.debug("Parsed locations - Current: {}, Destiny: {}", currentLocation, destinyLocation);

        // Get truck ID from EIR
        Integer truckId = null;
        if (result.getLastGateInEirId() != null) {
            truckId = eirRepository.findTruckIdByEirId(result.getLastGateInEirId());
            log.debug("Found truck ID: {} for EIR ID: {}", truckId, result.getLastGateInEirId());
        }

        return MovementInstructionDTO.builder()
                .containerId(result.getContainerId())
                .originYardId(yardConfig.getCurrentYardId())
                .originBlockId(currentLocation != null && currentLocation.getLocationId() != null ?
                        currentLocation.getBlockId() : onTruckConfig.getBlockId())
                .originCellId(currentLocation != null && currentLocation.getLocationId() != null ?
                        currentLocation.getCellId() : onTruckConfig.getCellId())
                .originLevelId(currentLocation != null && currentLocation.getLocationId() != null ?
                        currentLocation.getLevelId() : onTruckConfig.getLevelId())
                .origin40YardId(result.getContainerSize() != null && result.getContainerSize() >= 40 ?
                        yardConfig.getCurrentYardId() : null)
                .origin40BlockId(result.getContainerSize() != null && result.getContainerSize() >= 40 &&
                        currentLocation != null && currentLocation.getSecondLocationId() != null ?
                        currentLocation.getSecondBlockId() : null)
                .origin40CellId(result.getContainerSize() != null && result.getContainerSize() >= 40 &&
                        currentLocation != null && currentLocation.getSecondLocationId() != null ?
                        currentLocation.getSecondCellId() : null)
                .origin40LevelId(result.getContainerSize() != null && result.getContainerSize() >= 40 &&
                        currentLocation != null && currentLocation.getSecondLocationId() != null ?
                        currentLocation.getSecondLevelId() : null)
                .destinyYardId(yardConfig.getCurrentYardId())
                .destinyBlockId(destinyLocation != null ? destinyLocation.getBlockId() : null)
                .destinyCellId(destinyLocation != null ? destinyLocation.getCellId() : null)
                .destinyLevelId(destinyLocation != null ? destinyLocation.getLevelId() : null)
                .destiny40YardId(result.getContainerSize() != null && result.getContainerSize() >= 40 ?
                        yardConfig.getCurrentYardId() : null)
                .destiny40BlockId(result.getContainerSize() != null && result.getContainerSize() >= 40 &&
                        destinyLocation != null && destinyLocation.getSecondLocationId() != null ?
                        destinyLocation.getSecondBlockId() : null)
                .destiny40CellId(result.getContainerSize() != null && result.getContainerSize() >= 40 &&
                        destinyLocation != null && destinyLocation.getSecondLocationId() != null ?
                        destinyLocation.getSecondCellId() : null)
                .destiny40LevelId(result.getContainerSize() != null && result.getContainerSize() >= 40 &&
                        destinyLocation != null && destinyLocation.getSecondLocationId() != null ?
                        destinyLocation.getSecondLevelId() : null)
                .proposedDestinyYardId(yardConfig.getCurrentYardId())
                .proposedDestinyBlockId(destinyLocation != null ? destinyLocation.getBlockId() : null)
                .proposedDestinyCellId(destinyLocation != null ? destinyLocation.getCellId() : null)
                .proposedDestinyLevelId(destinyLocation != null ? destinyLocation.getLevelId() : null)
                .stateId(catalogConfig.getMovementStatusExecuted())
                .yardId(yardConfig.getCurrentYardId())
                .catId(catalogConfig.getEirCatOriginPositioningId())
                .workQueueId(yardConfig.getDefaultWorkQueueId())
                .operationCatId(catalogConfig.getEirCatInstructionMovementLocationId())
                .truckId(truckId)
                .active(true)
                .registrationUserId(null) // Will be set when calling
                .movementSequence(1)
                .movementTypeCatId(catalogConfig.getEirCatTypeMovementIncomeId())
                .requiresTruck(false)
                .imminent(false)
                .eirId(result.getLastGateInEirId())
                .comment("MOVED_FROM_LOAD_CONTAINER")
                .build();
    }

    private List<MovementInstructionDTO> createRemoveMovementInstructions(String containersJson,
                                                                         YardConfigurationDTO yardConfig,
                                                                         CatalogConfigurationDTO catalogConfig,
                                                                         VirtualBlockConfigDTO outConfig) {
        if (containersJson == null || containersJson.isEmpty()) {
            return List.of();
        }

        // Get validation results from the validation service
        List<String> containerNumbers = parseContainersJson(containersJson);

        List<ValidateMassiveContainerRemovementOutput> validationResults = validateRemovalService.validateMassiveContainerRemovement(
                yardConfig.getCurrentYardId(), 1, containerNumbers);

        List<MovementInstructionDTO> instructions = new ArrayList<>();

        for (ValidateMassiveContainerRemovementOutput result : validationResults) {
            if (result.getContainerId() != null) {
                MovementInstructionDTO instruction = createRemoveMovementInstruction(result, yardConfig, catalogConfig, outConfig);
                instructions.add(instruction);
            }
        }

        return instructions;
    }

    private MovementInstructionDTO createRemoveMovementInstruction(ValidateMassiveContainerRemovementOutput result,
                                                                 YardConfigurationDTO yardConfig,
                                                                 CatalogConfigurationDTO catalogConfig,
                                                                 VirtualBlockConfigDTO outConfig) {
        // Parse current location JSON
        LocationInfo currentLocation = parseLocationJson(result.getContainerCurrentLocation());

        // Get truck ID from EIR
        Integer truckId = null;
        if (result.getContainerGateoutEir() != null) {
            truckId = eirRepository.findTruckIdByEirId(result.getContainerGateoutEir());
        }

        return MovementInstructionDTO.builder()
                .containerId(result.getContainerId())
                .originYardId(yardConfig.getCurrentYardId())
                .originBlockId(currentLocation != null ? currentLocation.getBlockId() : null)
                .originCellId(currentLocation != null ? currentLocation.getCellId() : null)
                .originLevelId(currentLocation != null ? currentLocation.getLevelId() : null)
                .origin40YardId(currentLocation != null && currentLocation.getSecondLocationId() != null ?
                        yardConfig.getCurrentYardId() : null)
                .origin40BlockId(currentLocation != null ? currentLocation.getSecondBlockId() : null)
                .origin40CellId(currentLocation != null ? currentLocation.getSecondCellId() : null)
                .origin40LevelId(currentLocation != null ? currentLocation.getSecondLevelId() : null)
                .destinyYardId(yardConfig.getCurrentYardId())
                .destinyBlockId(outConfig.getBlockId())
                .destinyCellId(outConfig.getCellId())
                .destinyLevelId(outConfig.getLevelId())
                .destiny40YardId(currentLocation != null && currentLocation.getSecondLocationId() != null ?
                        yardConfig.getCurrentYardId() : null)
                .destiny40BlockId(currentLocation != null && currentLocation.getSecondLocationId() != null ?
                        outConfig.getBlockId() : null)
                .destiny40CellId(currentLocation != null && currentLocation.getSecondLocationId() != null ?
                        outConfig.getCellId() : null)
                .destiny40LevelId(currentLocation != null && currentLocation.getSecondLocationId() != null ?
                        outConfig.getLevelId() : null)
                .proposedDestinyYardId(yardConfig.getCurrentYardId())
                .proposedDestinyBlockId(outConfig.getBlockId())
                .proposedDestinyCellId(outConfig.getCellId())
                .proposedDestinyLevelId(outConfig.getLevelId())
                .stateId(catalogConfig.getMovementStatusExecuted())
                .yardId(yardConfig.getCurrentYardId())
                .catId(catalogConfig.getEirCatOriginPositioningId())
                .workQueueId(yardConfig.getDefaultWorkQueueId())
                .operationCatId(catalogConfig.getEirCatInstructionMovementRemovedId())
                .truckId(truckId)
                .active(true)
                .registrationUserId(null) // Will be set when calling
                .movementSequence(1)
                .movementTypeCatId(catalogConfig.getEirCatTypeMovementExitId())
                .requiresTruck(false)
                .imminent(false)
                .eirId(result.getContainerGateoutEir())
                .comment("MOVED_FROM_LOAD_CONTAINER")
                .build();
    }

    private void cancelPendingMovementInstructions(List<MovementInstructionDTO> instructions,
                                                   Integer cancelledStatusId,
                                                   Integer userId) {
        log.debug("Cancelling pending movement instructions for {} containers", instructions.size());

        List<Integer> instructionIds = instructions.stream()
                .map(MovementInstructionDTO::getContainerId)
                .toList();

        if (!instructionIds.isEmpty()) {
            log.info("Cancelling {} pending movement instructions with status ID: {}", instructionIds.size(), cancelledStatusId);
            int cancelledCount = movementInstructionRepository.cancelPendingMovementInstructions(instructionIds, cancelledStatusId, userId);
            log.info("Successfully cancelled {} movement instructions", cancelledCount);
        } else {
            log.debug("No movement instructions to cancel");
        }
    }

    private void insertMovementInstructions(List<MovementInstructionDTO> instructions, Integer userId) {
        log.info("Inserting {} movement instructions", instructions.size());

        for (MovementInstructionDTO dto : instructions) {
            dto.setRegistrationUserId(userId);
            MovementInstruction entity = convertToMovementInstructionEntity(dto);
            movementInstructionRepository.save(entity);
        }
    }

    private MovementInstruction convertToMovementInstructionEntity(MovementInstructionDTO dto) {
        MovementInstruction entity = new MovementInstruction();

        // Set container
        if (dto.getContainerId() != null) {
            Container container = new Container();
            container.setId(dto.getContainerId());
            entity.setContainer(container);
        }

        // Set origin yard
        if (dto.getOriginYardId() != null) {
            Yard originYard = new Yard();
            originYard.setId(dto.getOriginYardId());
            entity.setOriginYard(originYard);
        }

        // Set destination yard
        if (dto.getDestinyYardId() != null) {
            Yard destinationYard = new Yard();
            destinationYard.setId(dto.getDestinyYardId());
            entity.setDestinationYard(destinationYard);
        }

        // Set proposed destination yard
        if (dto.getProposedDestinyYardId() != null) {
            Yard proposedDestinationYard = new Yard();
            proposedDestinationYard.setId(dto.getProposedDestinyYardId());
            entity.setProposedDestinationYard(proposedDestinationYard);
        }

        // Set catalog status
        if (dto.getStateId() != null) {
            Catalog state = new Catalog();
            state.setId(dto.getStateId());
            entity.setCatStatus(state);
        }

        // Set yard
        if (dto.getYardId() != null) {
            Yard yard = new Yard();
            yard.setId(dto.getYardId());
            entity.setYard(yard);
        }

        // Set catalog
        if (dto.getCatId() != null) {
            Catalog cat = new Catalog();
            cat.setId(dto.getCatId());
            entity.setCat(cat);
        }

        // Set work queue
        if (dto.getWorkQueueId() != null) {
            WorkQueue workQueue = new WorkQueue();
            workQueue.setId(dto.getWorkQueueId());
            entity.setWorkQueue(workQueue);
        }

        // Set operation catalog
        if (dto.getOperationCatId() != null) {
            Catalog operationCat = new Catalog();
            operationCat.setId(dto.getOperationCatId());
            entity.setCatOperation(operationCat);
        }

        // Set EIR
        if (dto.getEirId() != null) {
            Eir eir = new Eir();
            eir.setId(dto.getEirId());
            entity.setEir(eir);
        }

        // Set registration user
        if (dto.getRegistrationUserId() != null) {
            User registrationUser = new User();
            registrationUser.setId(dto.getRegistrationUserId());
            entity.setRegistrationUser(registrationUser);
        }

        // Set origin block
        if (dto.getOriginBlockId() != null) {
            Block originBlock = new Block();
            originBlock.setId(dto.getOriginBlockId());
            entity.setOriginBlock(originBlock);
        }
        // Set origin cell
        if (dto.getOriginCellId() != null) {
            Cell originCell = new Cell();
            originCell.setId(dto.getOriginCellId());
            entity.setOriginCell(originCell);
        }
        // Set origin level
        if (dto.getOriginLevelId() != null) {
            Level originLevel = new Level();
            originLevel.setId(dto.getOriginLevelId());
            entity.setOriginLevel(originLevel);
        }
        // Set origin 40 yard
        if (dto.getOrigin40YardId() != null) {
            Yard origin40Yard = new Yard();
            origin40Yard.setId(dto.getOrigin40YardId());
            entity.setOrigin4OYard(origin40Yard);
        }
        // Set origin 40 block
        if (dto.getOrigin40BlockId() != null) {
            Block origin40Block = new Block();
            origin40Block.setId(dto.getOrigin40BlockId());
            entity.setOrigin40Block(origin40Block);
        }
        // Set origin 40 cell
        if (dto.getOrigin40CellId() != null) {
            Cell origin40Cell = new Cell();
            origin40Cell.setId(dto.getOrigin40CellId());
            entity.setOrigin40Cell(origin40Cell);
        }
        // Set origin 40 level
        if (dto.getOrigin40LevelId() != null) {
            Level origin40Level = new Level();
            origin40Level.setId(dto.getOrigin40LevelId());
            entity.setOrigin40Level(origin40Level);
        }
        // Set destination block
        if (dto.getDestinyBlockId() != null) {
            Block destinationBlock = new Block();
            destinationBlock.setId(dto.getDestinyBlockId());
            entity.setDestinationBlock(destinationBlock);
        }
        // Set destination cell
        if (dto.getDestinyCellId() != null) {
            Cell destinationCell = new Cell();
            destinationCell.setId(dto.getDestinyCellId());
            entity.setDestinationCell(destinationCell);
        }
        // Set destination level
        if (dto.getDestinyLevelId() != null) {
            Level destinationLevel = new Level();
            destinationLevel.setId(dto.getDestinyLevelId());
            entity.setDestinationLevel(destinationLevel);
        }
        // Set destination 40 yard
        if (dto.getDestiny40YardId() != null) {
            Yard destination40Yard = new Yard();
            destination40Yard.setId(dto.getDestiny40YardId());
            entity.setDestination40Yard(destination40Yard);
        }
        // Set destination 40 block
        if (dto.getDestiny40BlockId() != null) {
            Block destination40Block = new Block();
            destination40Block.setId(dto.getDestiny40BlockId());
            entity.setDestination40Block(destination40Block);
        }
        // Set destination 40 cell
        if (dto.getDestiny40CellId() != null) {
            Cell destination40Cell = new Cell();
            destination40Cell.setId(dto.getDestiny40CellId());
            entity.setDestination40Cell(destination40Cell);
        }
        // Set destination 40 level
        if (dto.getDestiny40LevelId() != null) {
            Level destination40Level = new Level();
            destination40Level.setId(dto.getDestiny40LevelId());
            entity.setDestination40Level(destination40Level);
        }
        // Set proposed destination block
        if (dto.getProposedDestinyBlockId() != null) {
            Block proposedDestinationBlock = new Block();
            proposedDestinationBlock.setId(dto.getProposedDestinyBlockId());
            entity.setProposedDestinationBlock(proposedDestinationBlock);
        }
        // Set proposed destination cell
        if (dto.getProposedDestinyCellId() != null) {
            Cell proposedDestinationCell = new Cell();
            proposedDestinationCell.setId(dto.getProposedDestinyCellId());
            entity.setProposedDestinationCell(proposedDestinationCell);
        }
        // Set proposed destination level
        if (dto.getProposedDestinyLevelId() != null) {
            Level proposedDestinationLevel = new Level();
            proposedDestinationLevel.setId(dto.getProposedDestinyLevelId());
            entity.setProposedDestinationLevel(proposedDestinationLevel);
        }
        // Set truck (entity reference)
        if (dto.getTruckId() != null) {
            Truck truck = new Truck();
            truck.setId(dto.getTruckId());
            entity.setTruck(truck);
        }
        // Set movement sequence
        entity.setSequence(dto.getMovementSequence());
        // Set movement type (entity reference)
        if (dto.getMovementTypeCatId() != null) {
            Catalog catMovementType = new Catalog();
            catMovementType.setId(dto.getMovementTypeCatId());
            entity.setCatMovementType(catMovementType);
        }
        // Set imminent
        entity.setIsImminent(dto.getImminent());

        // Set simple fields
        entity.setActive(dto.getActive());

        entity.setRequiresTruck(dto.getRequiresTruck());
        entity.setComment(dto.getComment());
        entity.setRegistrationDate(LocalDateTime.now());

        return entity;
    }

    private void updateContainerLocations(List<MovementInstructionDTO> loadInstructions,
                                          List<MovementInstructionDTO> removeInstructions,
                                          Integer outBlockId,
                                          Integer userId) {
        log.info("Updating container locations for {} load and {} remove instructions",
                loadInstructions.size(), removeInstructions.size());

        // Remove containers from origin locations
        log.debug("Removing containers from origin locations for load instructions");
        for (MovementInstructionDTO instruction : loadInstructions) {
            log.debug("Removing container {} from origin location: block={}, cell={}, level={}",
                    instruction.getContainerId(), instruction.getOriginBlockId(),
                    instruction.getOriginCellId(), instruction.getOriginLevelId());

            int removedCount = containerLocationRepository.removeContainersFromOriginLocations(
                    instruction.getOriginBlockId(), instruction.getOriginCellId(), instruction.getOriginLevelId(),
                    instruction.getOrigin40BlockId(), instruction.getOrigin40CellId(), instruction.getOrigin40LevelId(),
                    userId);
            log.debug("Removed {} containers from origin locations", removedCount);
        }

        log.debug("Removing containers from origin locations for remove instructions");
        for (MovementInstructionDTO instruction : removeInstructions) {
            log.debug("Removing container {} from origin location: block={}, cell={}, level={}",
                    instruction.getContainerId(), instruction.getOriginBlockId(),
                    instruction.getOriginCellId(), instruction.getOriginLevelId());

            int removedCount = containerLocationRepository.removeContainersFromOriginLocations(
                    instruction.getOriginBlockId(), instruction.getOriginCellId(), instruction.getOriginLevelId(),
                    instruction.getOrigin40BlockId(), instruction.getOrigin40CellId(), instruction.getOrigin40LevelId(),
                    userId);
            log.debug("Removed {} containers from origin locations", removedCount);
        }

        // Place containers on destiny locations (excluding Out block)
        log.debug("Placing containers on destiny locations for load instructions");
        for (MovementInstructionDTO instruction : loadInstructions) {
            log.debug("Placing container {} at destiny location: block={}, cell={}, level={}",
                    instruction.getContainerId(), instruction.getDestinyBlockId(),
                    instruction.getDestinyCellId(), instruction.getDestinyLevelId());

            int placedCount = containerLocationRepository.placeContainerOnDestinyLocation(
                    instruction.getContainerId(),
                    instruction.getDestinyBlockId(), instruction.getDestinyCellId(), instruction.getDestinyLevelId(),
                    instruction.getDestiny40BlockId(), instruction.getDestiny40CellId(), instruction.getDestiny40LevelId(),
                    outBlockId, userId);
            log.debug("Placed {} containers at destiny locations", placedCount);
        }

        log.info("Container location updates completed successfully");
    }

    private LocationInfo parseLocationJson(String locationJson) {
        if (locationJson == null || locationJson.isEmpty()) {
            log.debug("No location JSON to parse");
            return null;
        }

        log.debug("Parsing location JSON: {}", locationJson);

        try {
            JsonNode jsonNode = objectMapper.readTree(locationJson);

            LocationInfo.LocationInfoBuilder builder = LocationInfo.builder();

            // Parse first location
            if (jsonNode.has("location_id")) {
                builder.locationId(jsonNode.get("location_id").asInt());
            }
            if (jsonNode.has("block_id")) {
                builder.blockId(jsonNode.get("block_id").asInt());
            }
            if (jsonNode.has("cell_id")) {
                builder.cellId(jsonNode.get("cell_id").asInt());
            }
            if (jsonNode.has("level_id")) {
                builder.levelId(jsonNode.get("level_id").asInt());
            }

            // Parse second location (for 40ft containers)
            if (jsonNode.has("second_location_id")) {
                builder.secondLocationId(jsonNode.get("second_location_id").asInt());
            }
            if (jsonNode.has("second_block_id")) {
                builder.secondBlockId(jsonNode.get("second_block_id").asInt());
            }
            if (jsonNode.has("second_cell_id")) {
                builder.secondCellId(jsonNode.get("second_cell_id").asInt());
            }
            if (jsonNode.has("second_level_id")) {
                builder.secondLevelId(jsonNode.get("second_level_id").asInt());
            }

            LocationInfo locationInfo = builder.build();
            log.debug("Successfully parsed location info: {}", locationInfo);
            return locationInfo;

        } catch (Exception e) {
            log.warn("Failed to parse location JSON: {}", locationJson, e);
            return null;
        }
    }

    private PerformMassiveContainerLoadOutput createErrorResponse(String message) {
        log.warn("Creating error response: {}", message);
        return PerformMassiveContainerLoadOutput.builder()
                .respStatus(2)
                .respMessage(message)
                .build();
    }

    private List<String> parseContainersJson(String containersJson) {
        if (containersJson == null || containersJson.isEmpty()) {
            log.debug("No containers JSON to parse");
            return List.of();
        }

        log.debug("Parsing containers JSON with {} characters", containersJson.length());

        try {
            List<String> containerNumbers = objectMapper.readValue(containersJson,
                    objectMapper.getTypeFactory().constructCollectionType(List.class, String.class));
            log.debug("Successfully parsed {} container numbers", containerNumbers.size());
            return containerNumbers;
        } catch (Exception e) {
            log.warn("Failed to parse containers JSON: {}", containersJson, e);
            return List.of();
        }
    }

    private boolean hasRemovalValidationErrors(ValidateMassiveContainerRemovementOutput output) {
        boolean hasErrors = Boolean.TRUE.equals(output.getInvalidContainer()) ||
                Boolean.TRUE.equals(output.getOutsideDepot()) ||
                Boolean.TRUE.equals(output.getInStock());

        if (hasErrors) {
            log.debug("Container {} has validation errors - invalid: {}, outsideDepot: {}, inStock: {}",
                    output.getContainerNumber(), output.getInvalidContainer(),
                    output.getOutsideDepot(), output.getInStock());
        }

        return hasErrors;
    }
}