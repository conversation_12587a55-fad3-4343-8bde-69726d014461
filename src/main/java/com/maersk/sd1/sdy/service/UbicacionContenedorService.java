package com.maersk.sd1.sdy.service;

import com.maersk.sd1.sdy.dto.UbicacionContenedorInputDTO;
import com.maersk.sd1.sdy.dto.UbicacionContenedorOutputDTO;
import com.maersk.sd1.sdy.dto.UbicacionContenedorRowDTO;
import com.maersk.sd1.common.repository.UbigeoRepository;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
@RequiredArgsConstructor
public class UbicacionContenedorService {

    private static final Logger logger = LogManager.getLogger(UbicacionContenedorService.class);

    private final UbigeoRepository ubicacionContenedorRepository;

    @Transactional(readOnly = true)
    public UbicacionContenedorOutputDTO obtenerUbicacionContenedores(UbicacionContenedorInputDTO.Input input) {
        UbicacionContenedorOutputDTO response = new UbicacionContenedorOutputDTO();
        try {
            Integer patioId = input.getPatioId();
            String ubicaciones = input.getUbicaciones();
            logger.info("Fetching container locations for patio: {} with ubicaciones: {}", patioId, ubicaciones);

            List<UbicacionContenedorRowDTO> rows = ubicacionContenedorRepository.findUbicacionesContenedor(patioId , ubicaciones);

//            response.setRespEstado(1);
//            response.setRespMensaje("Success");
            response.setData(rows);
        } catch (Exception e) {
            logger.error("Error in Service: ", e);
//            response.setRespEstado(0);
//            response.setRespMensaje(e.getMessage());
            response.setData(null);
        }
        return response;
    }
}

