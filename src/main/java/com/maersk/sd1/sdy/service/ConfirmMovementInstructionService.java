package com.maersk.sd1.sdy.service;

import com.maersk.sd1.sdy.dto.MovementInstructionConfirmInput;
import com.maersk.sd1.sdy.dto.MovementInstructionConfirmOutput;
import com.maersk.sd1.sdy.dto.PlanificacionCantRemInputDto;
import com.maersk.sd1.sdy.dto.UpdateRemovedQuantityInput;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

@Service
@RequiredArgsConstructor
public class ConfirmMovementInstructionService {

    private static final Logger logger = LogManager.getLogger(ConfirmMovementInstructionService.class);

    private final MovementInstructionConfirmaService movementInstructionConfirmaService;
    private final UpdateRemovedQuantityService updateRemovedQuantityService;

    @Transactional
    public MovementInstructionConfirmOutput confirmMovementInstruction(MovementInstructionConfirmInput.Input input){

        MovementInstructionConfirmOutput output = new MovementInstructionConfirmOutput();

        try{
            logger.info("Confirming movement instruction with input: {}", input);

            output = movementInstructionConfirmaService.confirmMovementInstruction(input.getMovementInstructionId(),input.getUserId());


            if ("SUCCESS".equals(output.getRespEstado())) {
                UpdateRemovedQuantityInput.Input updateInput = getInput(input, output);

                updateRemovedQuantityService.updateRemoveQuantity(updateInput);
            }
        }
        catch (Exception e){
            logger.error("Error confirming movement instruction: ", e);
            output.setRespMensaje("Error confirming movement instruction: " + e.getMessage());
            output.setRespEstado("ERROR");
        }

        return output;
    }

    private static UpdateRemovedQuantityInput.Input getInput(MovementInstructionConfirmInput.Input input, MovementInstructionConfirmOutput output) {
        List<PlanificacionCantRemInputDto.BloqueFila> bloqueFilaList = new ArrayList<>();

        PlanificacionCantRemInputDto.BloqueFila origen = new PlanificacionCantRemInputDto.BloqueFila();
        origen.setBloqueId(output.getOrigenBloqueId());
        origen.setFila(output.getOrigenFila());
        bloqueFilaList.add(origen);

        PlanificacionCantRemInputDto.BloqueFila destino = new PlanificacionCantRemInputDto.BloqueFila();
        destino.setBloqueId(output.getDestinoBloqueId());
        destino.setFila(output.getDestinoFila());
        bloqueFilaList.add(destino);

        UpdateRemovedQuantityInput.Input updateInput = new UpdateRemovedQuantityInput.Input();

        updateInput.setBloquesFilas(bloqueFilaList);
        updateInput.setUserId(input.getUserId());
        return updateInput;
    }

}
