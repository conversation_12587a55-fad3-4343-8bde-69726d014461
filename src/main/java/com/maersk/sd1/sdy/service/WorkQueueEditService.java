package com.maersk.sd1.sdy.service;

import com.maersk.sd1.common.model.Catalog;
import com.maersk.sd1.common.model.User;
import com.maersk.sd1.common.model.WorkPoint;
import com.maersk.sd1.common.model.WorkQueue;
import com.maersk.sd1.common.model.Yard;
import com.maersk.sd1.common.repository.*;
import com.maersk.sd1.sdy.controller.dto.WorkQueueEditInput;
import com.maersk.sd1.sdy.controller.dto.WorkQueueEditOutput;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

/**
 * Service class for editing an existing WorkQueue record.
 * Translates the logic from the stored procedure sdy.cola_trabajo_editar.
 */
@RequiredArgsConstructor
@Service
public class WorkQueueEditService {

    private static final Logger logger = LogManager.getLogger(WorkQueueEditService.class.getName());

    private final WorkPointRepository workPointRepository;
    private final UserRepository userRepository;
    private final YardRepository yardRepository;
    private final CatalogRepository catalogRepository;
    private final WorkQueueRepository workQueueRepository;
    private final MessageLanguageRepository messageLanguageRepository;

    /**
     * Edits a WorkQueue (similar to sdy.cola_trabajo_editar)
     *
     * @param input Request input data.
     * @return WorkQueueEditOutput containing response message and status.
     */
    @Transactional
    public WorkQueueEditOutput editWorkQueue(WorkQueueEditInput.Input input) {
        WorkQueueEditOutput output = new WorkQueueEditOutput();
        try {
            // 1. Retrieve existing WorkQueue.
            WorkQueue foundQueue = workQueueRepository.findById(input.getColaTrabajoId())
                    .orElseThrow(() -> {
                        logger.error("WorkQueue not found with id: {}", input.getColaTrabajoId());
                        return new IllegalArgumentException("WorkQueue not found.");
                    });

            if(input.getEstadoId() != null){
                Catalog catalog = catalogRepository.findById(input.getEstadoId()).orElse(null);
                foundQueue.setCatStatus(catalog);

            }

            if(input.getUsuarioId() != null){
                User user = userRepository.findById(input.getUsuarioId()).orElse(null);
                foundQueue.setUser(user);
            }

            if(input.getUsuarioModificacionId() != null){
                User user = userRepository.findById(input.getUsuarioModificacionId()).orElse(null);
                foundQueue.setModificationUser(user);
            }

            if(input.getPuntoTrabajoId() != null){
                WorkPoint workPoint = workPointRepository.findById(input.getPuntoTrabajoId()).orElse(null);
                foundQueue.setWorkPoint(workPoint);
            }

            if(input.getPatioId() != null){
                Yard yard = yardRepository.findById(input.getPatioId()).orElse(null);
                foundQueue.setYard(yard);
            }

            // 2. Update fields.
            foundQueue.setCode(input.getCodigo());
            foundQueue.setDescription(input.getDescripcion());
            foundQueue.setActive(input.getActivo());
            foundQueue.setModificationDate(LocalDateTime.now());

            // 3. Save the entity (this triggers the DB update).
            workQueueRepository.save(foundQueue);

            // 4. Set successful response.
            output.setRespEstado(1);
            // Mimics: SET @resp_mensaje = ges.fn_MensajeTraducido('GENERAL', 10 , @idioma_id)
            String translatedMsg = messageLanguageRepository.fnTranslatedMessage("GENERAL", 10, input.getIdiomaId());   
            output.setRespMensaje(translatedMsg);
        } catch (Exception ex) {
            // If any exception arises, log it and set response to error.
            logger.error("Error while editing WorkQueue:", ex);
            output.setRespEstado(0);
            output.setRespMensaje(ex.getMessage());
            // Transaction will roll back due to the exception.
        }
        return output;
    }
}
