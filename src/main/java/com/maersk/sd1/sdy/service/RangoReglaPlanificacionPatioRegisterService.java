package com.maersk.sd1.sdy.service;

import com.maersk.sd1.common.model.*;
import com.maersk.sd1.common.repository.*;
import com.maersk.sd1.sdy.dto.RangoReglaPlanificacionPatioRegisterInputDTO;
import com.maersk.sd1.sdy.dto.RangoReglaPlanificacionPatioRegisterOutputDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;
import java.util.HashSet;


@Service
@RequiredArgsConstructor
@Log4j2
public class RangoReglaPlanificacionPatioRegisterService {
    private final UserRepository userRepository;

    private final YardPlanningRuleRangeRepository yardPlanningRuleRangeRepository;
    private final YardPlanningRuleRepository yardPlanningRuleRepository;
    private final BlockRepository blockRepository;
    private final BlockRestrictionRepository blockRestrictionRepository;
    private final ContainerLocationRepository containerLocationRepository;
    private final StockEmptyRepository stockEmptyRepository;
    private final StockFullRepository stockFullRepository;
    private final MessageLanguageRepository messageLanguageRepository;

    @Transactional
    public RangoReglaPlanificacionPatioRegisterOutputDTO registerRange(RangoReglaPlanificacionPatioRegisterInputDTO.Input input) {
        RangoReglaPlanificacionPatioRegisterOutputDTO output = new RangoReglaPlanificacionPatioRegisterOutputDTO();
        output.setRespEstado(0);
        output.setRespMensaje("");
        output.setRespNewId(0);

        try {
            YardPlanningRule yardPlanningRule = yardPlanningRuleRepository.findById(input.getReglaPlanificacionPatioId())
                    .orElseThrow(() -> new IllegalArgumentException("Invalid regla_planificacion_patio_id"));

            Integer catEmptyFullId = yardPlanningRule.getCatEmptyFull() == null ? null : yardPlanningRule.getCatEmptyFull().getId();
            Integer catSizeId = yardPlanningRule.getCatContainerSize() == null ? null : yardPlanningRule.getCatContainerSize().getId();

            List<String> existingRanges = yardPlanningRuleRangeRepository.findConflictingRangeDifferentTypeOrSize(
                    input.getBloqueId(),
                    catEmptyFullId,
                    catSizeId,
                    input.getIndiceFilaDesde(),
                    input.getIndiceFilaHasta(),
                    input.getIndiceColumnaDesde(),
                    input.getIndiceColumnaHasta()
            );
            if (!existingRanges.isEmpty()) {
                output.setRespEstado(0);
                output.setRespMensaje("There is already a range assigned with a different type or size to the same area " + existingRanges.get(0));
                return output;
            }

            String containerDiffSize = this.findAnyContainerDifferentSize(
                    input.getBloqueId(),
                    input.getIndiceFilaDesde(),
                    input.getIndiceFilaHasta(),
                    input.getIndiceColumnaDesde(),
                    input.getIndiceColumnaHasta(),
                    catSizeId
            );
            if (containerDiffSize != null) {
                output.setRespEstado(0);
                output.setRespMensaje("There are some containers that have a different size from the one of the rule in the selected area - " + containerDiffSize);
                return output;
            }

            String containerDiffType = this.findAnyContainerDifferentEmptyFull(
                    input.getBloqueId(),
                    input.getIndiceFilaDesde(),
                    input.getIndiceFilaHasta(),
                    input.getIndiceColumnaDesde(),
                    input.getIndiceColumnaHasta(),
                    catEmptyFullId
            );
            if (containerDiffType != null) {
                output.setRespEstado(0);
                output.setRespMensaje("There are some containers that have a different type from the one of the rule in the selected area - " + containerDiffType);
                return output;
            }

            Block block = blockRepository.findById(input.getBloqueId())
                    .orElseThrow(() -> new IllegalArgumentException("Invalid bloque_id"));
            int levelQuantity = block.getLevels();

            List<BlockRestriction> activeRestrictions = blockRestrictionRepository.findByBlockIdAndActiveTrue(input.getBloqueId());

            Set<String> fullyRestrictedCells = findFullyRestrictedCells(block, activeRestrictions, levelQuantity);

            List<String> restrictedCellsInRange = fullyRestrictedCells.stream()
                    .map(cellRC -> {
                        // each cellRC is something like "row:col"
                        String[] parts = cellRC.split(":");
                        int rowIdx = Integer.parseInt(parts[0]);
                        int colIdx = Integer.parseInt(parts[1]);
                        if (rowIdx >= input.getIndiceFilaDesde() && rowIdx <= input.getIndiceFilaHasta()
                                && colIdx >= input.getIndiceColumnaDesde() && colIdx <= input.getIndiceColumnaHasta()) {
                            return "(x: " + rowIdx + "- y: " + colIdx + ") ";
                        } else {
                            return null;
                        }
                    })
                    .filter(val -> val != null)
                    .toList();

            if (!restrictedCellsInRange.isEmpty()) {
                output.setRespEstado(0);
                String joined = String.join(",", restrictedCellsInRange);
                output.setRespMensaje("There are some cells restricted in the range - " + joined);
                return output;
            }

            YardPlanningRuleRange newRange = new YardPlanningRuleRange();
            newRange.setYardPlanningRule(yardPlanningRule);
            newRange.setBlock(block);
            newRange.setCell(input.getCeldaId() == null ? null : new com.maersk.sd1.common.model.Cell(input.getCeldaId()));
            newRange.setLevel(input.getNivelId() == null ? null : new com.maersk.sd1.common.model.Level(input.getNivelId()));
            newRange.setIndexColumnSince(input.getIndiceColumnaDesde());
            newRange.setIndexColumnUntil(input.getIndiceColumnaHasta());
            newRange.setIndexRowSince(input.getIndiceFilaDesde());
            newRange.setIndexRowUntil(input.getIndiceFilaHasta());
            newRange.setActive(input.getActivo());
            newRange.setRegistrationUser(userRepository.findById(input.getUsuarioRegistroId())
                    .orElse(null));
            newRange.setRegistrationDate(LocalDateTime.now());
            newRange.setDirectionStacking(input.getDireccionStacking() == null ? Boolean.TRUE : input.getDireccionStacking());
            newRange.setIndexLimitStacking(input.getIndiceLimiteStacking());

            YardPlanningRuleRange savedRange = yardPlanningRuleRangeRepository.save(newRange);

            String successMessage = messageLanguageRepository.fnTranslatedMessage("GENERAL", 9, input.getIdiomaId());

            output.setRespEstado(1);
            output.setRespMensaje(successMessage);
            output.setRespNewId(savedRange.getId());
        } catch (Exception ex) {
            log.error("Error registering YardPlanningRuleRange", ex);
            output.setRespEstado(0);
            output.setRespMensaje(ex.getMessage());
            output.setRespNewId(0);
        }

        return output;
    }

    /**
     * PARTIAL LOGIC for checking containers with different size.
     * Returns the containerNumber if found, or null if none.
     */
    private String findAnyContainerDifferentSize(Integer blockId,
                                                 Integer rowFrom, Integer rowTo,
                                                 Integer colFrom, Integer colTo,
                                                 Integer catSizeId) {
        if (catSizeId == null) {
            return null;
        }
        List<ContainerLocation> locations = containerLocationRepository.findActiveWithBlockBetweenRowsCols(blockId, rowFrom, rowTo, colFrom, colTo);
        for (ContainerLocation loc : locations) {
            if (loc.getContainer() != null && loc.getContainer().getCatSize() != null) {
                Integer existingCatSizeId = loc.getContainer().getCatSize().getId();
                if (!existingCatSizeId.equals(catSizeId)) {
                    // Return the first container number found
                    return loc.getContainer().getContainerNumber();
                }
            }
        }
        return null;
    }

    private String findAnyContainerDifferentEmptyFull(Integer blockId,
                                                      Integer rowFrom, Integer rowTo,
                                                      Integer colFrom, Integer colTo,
                                                      Integer catEmptyFullId) {
        if (catEmptyFullId == null) {
            return null;
        }

        List<ContainerLocation> locations = containerLocationRepository.findActiveWithBlockBetweenRowsCols(blockId, rowFrom, rowTo, colFrom, colTo);

        String containerNumberFound = null;
        for (ContainerLocation loc : locations) {
            if (loc.getContainer() == null) {
                continue;
            }
            Integer containerId = loc.getContainer().getId();

            List<StockEmpty> empties = stockEmptyRepository.findByContainerIdAndInStockTrue(containerId);
            if (!empties.isEmpty()) {
                for (StockEmpty se : empties) {
                    if (se.getGateInEir() != null && se.getGateInEir().getCatEmptyFull() != null) {
                        Integer eirCat = se.getGateInEir().getCatEmptyFull().getId();
                        if (!eirCat.equals(catEmptyFullId)) {
                            containerNumberFound = loc.getContainer().getContainerNumber();
                            break;
                        }
                    }
                }
                if (containerNumberFound != null) {
                    return containerNumberFound;
                }
            }

            List<StockFull> fulls = stockFullRepository.findByContainerIdAndInStockTrue(containerId);
            if (!fulls.isEmpty()) {
                for (StockFull sf : fulls) {
                    if (sf.getGateInEir() != null && sf.getGateInEir().getCatEmptyFull() != null) {
                        Integer eirCat = sf.getGateInEir().getCatEmptyFull().getId();
                        if (!eirCat.equals(catEmptyFullId)) {
                            containerNumberFound = loc.getContainer().getContainerNumber();
                            break;
                        }
                    }
                }
                if (containerNumberFound != null) {
                    return containerNumberFound;
                }
            }
        }
        return null;
    }

    private Set<String> findFullyRestrictedCells(Block block, List<BlockRestriction> restrictions, int totalLevels) {

        java.util.Map<String, Integer> restrictedCounts = new java.util.HashMap<>();
        for (BlockRestriction br : restrictions) {
            int rowStart = br.getRowIndexFrom();
            int rowEnd = br.getRowIndexTo();
            int colStart = br.getColumnIndexFrom();
            int colEnd = br.getColumnIndexTo();
            int lvlStart = br.getLevelIndexFrom();
            int lvlEnd = br.getLevelIndexTo();

            for (int rw = rowStart; rw <= rowEnd; rw++) {
                for (int cl = colStart; cl <= colEnd; cl++) {
                    String key = rw + ":" + cl;

                    int currentCount = restrictedCounts.getOrDefault(key, 0);
                    int restrictedLevelsHere = (lvlEnd - lvlStart + 1);
                    restrictedCounts.put(key, currentCount + restrictedLevelsHere);
                }
            }
        }

        Set<String> fullyRestricted = new HashSet<>();
        for (java.util.Map.Entry<String, Integer> entry : restrictedCounts.entrySet()) {
            if (entry.getValue() == totalLevels) {
                fullyRestricted.add(entry.getKey());
            }
        }

        return fullyRestricted;
    }
}