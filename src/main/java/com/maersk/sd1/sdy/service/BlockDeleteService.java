package com.maersk.sd1.sdy.service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.maersk.sd1.common.model.Block;
import com.maersk.sd1.common.model.Layout;
import com.maersk.sd1.common.model.User;
import com.maersk.sd1.sdy.dto.BlockDeleteOutput;
import com.maersk.sd1.common.repository.BlockRepository;
import com.maersk.sd1.common.repository.BlockRestrictionRepository;
import com.maersk.sd1.common.repository.CellRepository;
import com.maersk.sd1.common.repository.ContainerLocationRepository;
import com.maersk.sd1.common.repository.LayoutRepository;
import com.maersk.sd1.common.repository.LevelRepository;
import com.maersk.sd1.common.repository.MessageLanguageRepository;
import com.maersk.sd1.common.repository.UserRepository;
import java.time.LocalDateTime;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@RequiredArgsConstructor
@Service
public class BlockDeleteService {

    private static final Logger logger = LogManager.getLogger(BlockDeleteService.class);

    private final BlockRepository blockRepository;
    private final CellRepository cellRepository;
    private final LevelRepository levelRepository;
    private final ContainerLocationRepository containerLocationRepository;
    private final BlockRestrictionRepository blockRestrictionRepository;
    private final LayoutRepository layoutRepository;
    private final MessageLanguageRepository messageLanguageRepository;
    private final UserRepository userRepository;

    @Transactional
    public BlockDeleteOutput blockDelete(Integer blockId, Integer userModificationId, Integer languageId) {
        BlockDeleteOutput output = new BlockDeleteOutput();
        LocalDateTime now = LocalDateTime.now();
        try {
            logger.info("Starting blockDelete with blockId={}, userModificationId={}, languageId={}", blockId, userModificationId, languageId);

            Optional<Block> optionalBlock = blockRepository.findById(blockId);
            if (optionalBlock.isEmpty()) {
                logger.error("Block not found for blockId={}", blockId);
                output.setRespEstado(0);
                output.setRespMensaje("Block not found");
                return output;
            }
            Block block = optionalBlock.get();

            User modUser = null;
            if (userModificationId != null) {
                modUser = userRepository.findById(userModificationId).orElseGet(() -> {
                    User u = new User();
                    u.setId(userModificationId);
                    return u;
                });
            } else {
                modUser = null;
            }

            blockRepository.deactivateBlock(blockId, modUser, now);

            cellRepository.deactivateCellsByBlockId(blockId, modUser, now);

            levelRepository.deactivateLevelsByBlockId(blockId, modUser, now);

            containerLocationRepository.deactivateContainerLocationsByBlockId(blockId, modUser, now);

            blockRestrictionRepository.deactivateBlockRestrictionsByBlockId(blockId, modUser, now);

            Layout yardLayout = block.getYard().getLayout();
            if (yardLayout != null) {
                ObjectMapper mapper = new ObjectMapper();
                String currentConfig = yardLayout.getConfiguration();
                if (currentConfig != null && !currentConfig.isBlank()) {
                    List<Map<String, Object>> polygons = mapper.readValue(currentConfig, new TypeReference<>() {});
                    Iterator<Map<String, Object>> iterator = polygons.iterator();
                    while (iterator.hasNext()) {
                        Map<String, Object> polygon = iterator.next();
                        Object bloqueIdObj = polygon.get("bloque_id");
                        if (bloqueIdObj != null && bloqueIdObj instanceof Number) {
                            Number polygonBlockId = (Number) bloqueIdObj;
                            if (polygonBlockId.intValue() == blockId) {
                                iterator.remove();
                            }
                        }
                    }
                    String updatedConfig = mapper.writeValueAsString(polygons);
                    yardLayout.setConfiguration(updatedConfig);
                    layoutRepository.save(yardLayout);
                }
            }


            String translatedMessage = messageLanguageRepository.fnTranslatedMessage("GENERAL", 7, languageId);

            if (translatedMessage == null) {
                translatedMessage = "Successfully deactivated block.";
            }

            output.setRespEstado(1);
            output.setRespMensaje(translatedMessage);
            return output;
        } catch (Exception e) {
            logger.error("Error while deactivating block:", e);
            output.setRespEstado(0);
            output.setRespMensaje(e.getMessage());
            return output;
        }
    }
}
