package com.maersk.sd1.sdy.service;

import com.maersk.sd1.common.model.*;
import com.maersk.sd1.common.repository.BlockRepository;
import com.maersk.sd1.common.repository.LayoutRepository;
import com.maersk.sd1.common.repository.MessageLanguageRepository;
import com.maersk.sd1.common.repository.CatalogRepository;
import com.maersk.sd1.sdy.dto.BlockEditInput;
import com.maersk.sd1.sdy.dto.BlockEditOutput;
import jakarta.persistence.EntityManager;
import jakarta.persistence.StoredProcedureQuery;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class BlockEditServiceTest {

    @InjectMocks
    private BlockEditService blockEditService;

    @Mock
    private BlockRepository blockRepository;
    @Mock
    private LayoutRepository layoutRepository;
    @Mock
    private MessageLanguageRepository messageLanguageRepository;
    @Mock
    private CatalogRepository catalogRepository;
    @Mock
    private EntityManager entityManager;
    @Mock
    private StoredProcedureQuery storedProcedureQuery;

    private BlockEditInput.Input input;
    private Block block;

    @BeforeEach
    void setUp() {
        input = new BlockEditInput.Input();
        input.setBlockId(1);
        input.setYardId(1);
        input.setCatBlockId(2);
        input.setCode("B001");
        input.setName("Block 001");
        input.setRows(10);
        input.setColumns(5);
        input.setLevels(3);
        input.setRow20Label("A");
        input.setRow40Label("B");
        input.setColumnLabels("A,B,C,D,E");
        input.setConfiguration("");
        input.setActive(true);
        input.setUserModificationId(99);
        input.setNewRowQuantity(10);
        input.setNewColumnQuantity(5);
        input.setNewLevelQuantity(3);
        input.setCantidadLimiteHeap(10);
        input.setLanguageId(1);

        block = new Block();
        block.setId(1);
        block.setRows(8);
        block.setColumns(4);
        block.setLevels(2);
        Catalog cat = new Catalog();
        cat.setAlias("sd1_block_type_stack");
        block.setCatBlockType(cat);
        block.setYard(new Yard(1));
    }

    @Test
    void Given_ValidInput_When_EditBlock_Then_ReturnSuccessOutput() {
        when(catalogRepository.findIdByAlias(anyString())).thenReturn(2);
        when(blockRepository.findById(anyInt())).thenReturn(Optional.of(block));
        when(entityManager.createStoredProcedureQuery(anyString())).thenReturn(storedProcedureQuery);

        when(storedProcedureQuery.registerStoredProcedureParameter(anyString(), any(), any()))
                .thenReturn(storedProcedureQuery);
        when(storedProcedureQuery.setParameter(anyString(), any()))
                .thenReturn(storedProcedureQuery);
        when(storedProcedureQuery.getOutputParameterValue("P_OUT_resp_status"))
                .thenReturn(1);
        when(storedProcedureQuery.getOutputParameterValue("P_OUT_resp_message"))
                .thenReturn("Success");

        when(messageLanguageRepository.fnTranslatedMessage(anyString(), anyInt(), anyInt()))
                .thenReturn("Block updated successfully");

        BlockEditOutput result = blockEditService.editBlock(input);

        assertNotNull(result);
        assertEquals(1, result.getRespEstado());
        assertEquals("Block updated successfully", result.getRespMensaje());
        verify(blockRepository).save(any(Block.class));
    }

    @Test
    void Given_BlockNotFound_When_EditBlock_Then_ReturnErrorOutput() {
        when(blockRepository.findById(anyInt())).thenReturn(Optional.empty());

        BlockEditOutput result = blockEditService.editBlock(input);

        assertNotNull(result);
        assertEquals(0, result.getRespEstado());
        assertTrue(result.getRespMensaje().contains("Block not found"));
        verify(blockRepository, never()).save(any());
    }

    @Test
    void Given_ProcedureFails_When_EditBlock_Then_ReturnErrorOutput() {
        when(catalogRepository.findIdByAlias(anyString())).thenReturn(2);
        when(blockRepository.findById(anyInt())).thenReturn(Optional.of(block));
        when(entityManager.createStoredProcedureQuery(anyString())).thenReturn(storedProcedureQuery);

        when(storedProcedureQuery.registerStoredProcedureParameter(anyString(), any(), any()))
                .thenReturn(storedProcedureQuery);
        when(storedProcedureQuery.setParameter(anyString(), any()))
                .thenReturn(storedProcedureQuery);
        when(storedProcedureQuery.getOutputParameterValue("P_OUT_resp_status"))
                .thenReturn(0);
        when(storedProcedureQuery.getOutputParameterValue("P_OUT_resp_message"))
                .thenReturn("Procedure failed");

        BlockEditOutput result = blockEditService.editBlock(input);

        assertNotNull(result);
        assertEquals(0, result.getRespEstado());
        assertTrue(result.getRespMensaje().contains("Procedure failed"));
        verify(blockRepository, never()).save(any());
    }
}
