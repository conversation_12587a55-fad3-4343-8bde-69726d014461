package com.maersk.sd1.sdy.service;

import com.maersk.sd1.common.model.Layout;
import com.maersk.sd1.common.model.Yard;
import com.maersk.sd1.common.repository.LayoutRepository;
import com.maersk.sd1.common.repository.MessageLanguageRepository;
import com.maersk.sd1.common.repository.YardRepository;
import com.maersk.sd1.sdy.dto.PatioEditInput;
import com.maersk.sd1.sdy.dto.PatioEditOutput;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.math.BigDecimal;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

class PatioEditServiceTest {

    @Mock
    private YardRepository yardRepository;

    @Mock
    private LayoutRepository layoutRepository;

    @Mock
    private MessageLanguageRepository messageLanguageRepository;

    @InjectMocks
    private PatioEditService patioEditService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testEditYardSuccess() {
        PatioEditInput.Input input = new PatioEditInput.Input();
        input.setPatioId(1);
        input.setLayoutId(2);
        input.setCode("YARD001");
        input.setName("Test Yard");
        input.setZoom(5);
        input.setLatitude(new BigDecimal("12.345678"));
        input.setLongitude(new BigDecimal("98.765432"));
        input.setColor("Blue");
        input.setConfiguration("{\"points\":[]}");
        input.setActive(true);
        input.setUserModificationId(123);
        input.setUidSeleccionada("UID12345");
        input.setLanguageId(1);

        Yard yard = new Yard();
        yard.setId(1);

        Layout layout = new Layout();
        layout.setId(2);
        layout.setConfiguration("[]");

        when(yardRepository.findById(1)).thenReturn(Optional.of(yard));
        when(layoutRepository.findById(2)).thenReturn(Optional.of(layout));
        when(messageLanguageRepository.fnTranslatedMessage("GENERAL", 10, 1))
                .thenReturn("Yard updated successfully");

        PatioEditOutput result = patioEditService.editYard(input);

        assertNotNull(result);
        assertEquals(1, result.getRespEstado());
        assertEquals("Yard updated successfully", result.getRespMensaje());
        verify(yardRepository).save(yard);
        verify(layoutRepository).save(layout);
    }

    @Test
    void testEditYardYardNotFound() {
        PatioEditInput.Input input = new PatioEditInput.Input();
        input.setPatioId(1);

        when(yardRepository.findById(1)).thenReturn(Optional.empty());

        PatioEditOutput result = patioEditService.editYard(input);

        assertNotNull(result);
        assertEquals(0, result.getRespEstado());
        assertEquals("Yard not found", result.getRespMensaje());
        verify(yardRepository, never()).save(any());
    }

    @Test
    void testEditYardLayoutNotFound() {
        PatioEditInput.Input input = new PatioEditInput.Input();
        input.setPatioId(1);
        input.setLayoutId(2);

        Yard yard = new Yard();
        yard.setId(1);

        when(yardRepository.findById(1)).thenReturn(Optional.of(yard));
        when(layoutRepository.findById(2)).thenReturn(Optional.empty());

        PatioEditOutput result = patioEditService.editYard(input);

        assertNotNull(result);
        assertEquals(0, result.getRespEstado());
        assertEquals("Layout not found", result.getRespMensaje());
        verify(layoutRepository, never()).save(any());
    }

    @Test
    void testEditYardException() {
        PatioEditInput.Input input = new PatioEditInput.Input();
        input.setPatioId(1);

        when(yardRepository.findById(1)).thenThrow(new RuntimeException("Database error"));

        PatioEditOutput result = patioEditService.editYard(input);

        assertNotNull(result);
        assertEquals(0, result.getRespEstado());
        assertEquals("Database error", result.getRespMensaje());
    }
}