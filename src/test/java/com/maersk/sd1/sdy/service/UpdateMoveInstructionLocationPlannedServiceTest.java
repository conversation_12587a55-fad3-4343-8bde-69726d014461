package com.maersk.sd1.sdy.service;

import com.maersk.sd1.common.model.*;
import com.maersk.sd1.common.repository.*;
import com.maersk.sd1.sdy.dto.UpdateMoveInstructionLocationPlannedInputDTO;
import com.maersk.sd1.sdy.dto.UpdateMoveInstructionLocationPlannedOutputDTO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;

@DisplayName("UpdateMoveInstructionLocationPlannedService Tests")
class UpdateMoveInstructionLocationPlannedServiceTest {

    @Mock
    private MovementInstructionRepository movementInstructionRepository;

    @Mock
    private ContainerLocationRepository containerLocationRepository;

    @Mock
    private BlockRepository blockRepository;

    @Mock
    private CellRepository cellRepository;

    @Mock
    private LevelRepository levelRepository;

    @InjectMocks
    private UpdateMoveInstructionLocationPlannedService service;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Nested
    @DisplayName("Single Instruction Processing Tests")
    class SingleInstructionTests {

        @Test
        @DisplayName("Should successfully update movement instruction with normal location only")
        void givenValidInputWithNormalLocationOnly_whenUpdate_thenSuccessfullyUpdates() {
            // Arrange
            UpdateMoveInstructionLocationPlannedInputDTO input = createValidInput();
            input.setBlock40Code(null);
            input.setRow40(null);
            input.setColumn40(null);
            input.setLevel40Index(null);

            MovementInstruction movementInstruction = createMockMovementInstruction();
            ContainerLocation normalLocation = createMockContainerLocation(1, "A1", "01", "01", 1);

            when(movementInstructionRepository.findById(input.getInstructionMovementId()))
                .thenReturn(Optional.of(movementInstruction));
            when(containerLocationRepository.findLocationByBlockCellLevel("A1", "01", "01", 1))
                .thenReturn(Optional.of(normalLocation));
            when(movementInstructionRepository.save(any(MovementInstruction.class)))
                .thenReturn(movementInstruction);

            List<UpdateMoveInstructionLocationPlannedInputDTO> request = List.of(input);

            // Act
            UpdateMoveInstructionLocationPlannedOutputDTO result = service.updateMoveInstructionLocationPlanned(request);

            // Assert
            assertNotNull(result);
            assertEquals(1, result.getRespEstado());
            assertEquals("Success", result.getRespMensaje());

            verify(movementInstructionRepository).findById(input.getInstructionMovementId());
            verify(containerLocationRepository).findLocationByBlockCellLevel("A1", "01", "01", 1);
            verify(movementInstructionRepository).save(movementInstruction);

            // Verify movement instruction was updated correctly
            assertEquals(normalLocation.getBlock(), movementInstruction.getDestinationBlock());
            assertEquals(normalLocation.getCell(), movementInstruction.getDestinationCell());
            assertEquals(normalLocation.getLevel(), movementInstruction.getDestinationLevel());
            assertNull(movementInstruction.getDestination40Block());
            assertNull(movementInstruction.getDestination40Cell());
            assertNull(movementInstruction.getDestination40Level());
            assertEquals(input.getUserId(), movementInstruction.getModificationUser().getId());
            assertNotNull(movementInstruction.getModificationDate());
        }

        @Test
        @DisplayName("Should successfully update movement instruction with both normal and 40ft locations")
        void givenValidInputWithBothLocations_whenUpdate_thenSuccessfullyUpdates() {
            // Arrange
            UpdateMoveInstructionLocationPlannedInputDTO input = createValidInputWith40ft();
            MovementInstruction movementInstruction = createMockMovementInstruction();
            ContainerLocation normalLocation = createMockContainerLocation(1, "A1", "01", "01", 1);
            ContainerLocation location40 = createMockContainerLocation(2, "B2", "02", "02", 2);

            when(movementInstructionRepository.findById(input.getInstructionMovementId()))
                .thenReturn(Optional.of(movementInstruction));
            when(containerLocationRepository.findLocationByBlockCellLevel("A1", "01", "01", 1))
                .thenReturn(Optional.of(normalLocation));
            when(containerLocationRepository.findLocationByBlockCellLevel("B2", "02", "02", 2))
                .thenReturn(Optional.of(location40));
            when(movementInstructionRepository.save(any(MovementInstruction.class)))
                .thenReturn(movementInstruction);

            List<UpdateMoveInstructionLocationPlannedInputDTO> request = List.of(input);

            // Act
            UpdateMoveInstructionLocationPlannedOutputDTO result = service.updateMoveInstructionLocationPlanned(request);

            // Assert
            assertNotNull(result);
            assertEquals(1, result.getRespEstado());
            assertEquals("Success", result.getRespMensaje());

            // Verify both locations were set
            assertEquals(normalLocation.getBlock(), movementInstruction.getDestinationBlock());
            assertEquals(normalLocation.getCell(), movementInstruction.getDestinationCell());
            assertEquals(normalLocation.getLevel(), movementInstruction.getDestinationLevel());
            assertEquals(location40.getBlock(), movementInstruction.getDestination40Block());
            assertEquals(location40.getCell(), movementInstruction.getDestination40Cell());
            assertEquals(location40.getLevel(), movementInstruction.getDestination40Level());
        }

        @Test
        @DisplayName("Should create new container location when not found")
        void givenLocationNotFound_whenUpdate_thenCreatesNewLocation() {
            // Arrange
            UpdateMoveInstructionLocationPlannedInputDTO input = createValidInput();
            MovementInstruction movementInstruction = createMockMovementInstruction();
            Block block = createMockBlock(1, "A1");
            Cell cell = createMockCell(1, "01", "01");
            Level level = createMockLevel(1, 1);
            ContainerLocation newLocation = createMockContainerLocation(1, "A1", "01", "01", 1);

            when(movementInstructionRepository.findById(input.getInstructionMovementId()))
                .thenReturn(Optional.of(movementInstruction));
            when(containerLocationRepository.findLocationByBlockCellLevel("A1", "01", "01", 1))
                .thenReturn(Optional.empty());
            when(blockRepository.findBlockForInstruction(movementInstruction.getId(), "A1"))
                .thenReturn(Optional.of(block));
            when(cellRepository.findByBlockAndRowColumn(block.getId(), "01", "01"))
                .thenReturn(Optional.of(cell));
            when(levelRepository.findByCellAndIndex(cell.getId(), 1))
                .thenReturn(Optional.of(level));
            when(containerLocationRepository.save(any(ContainerLocation.class)))
                .thenReturn(newLocation);
            when(movementInstructionRepository.save(any(MovementInstruction.class)))
                .thenReturn(movementInstruction);

            List<UpdateMoveInstructionLocationPlannedInputDTO> request = List.of(input);

            // Act
            UpdateMoveInstructionLocationPlannedOutputDTO result = service.updateMoveInstructionLocationPlanned(request);

            // Assert
            assertNotNull(result);
            assertEquals(1, result.getRespEstado());
            assertEquals("Success", result.getRespMensaje());

            verify(blockRepository).findBlockForInstruction(movementInstruction.getId(), "A1");
            verify(cellRepository).findByBlockAndRowColumn(block.getId(), "01", "01");
            verify(levelRepository).findByCellAndIndex(cell.getId(), 1);
            verify(containerLocationRepository).save(any(ContainerLocation.class));
        }

        @Test
        @DisplayName("Should throw exception when movement instruction not found")
        void givenMovementInstructionNotFound_whenUpdate_thenThrowsException() {
            // Arrange
            UpdateMoveInstructionLocationPlannedInputDTO input = createValidInput();
            when(movementInstructionRepository.findById(input.getInstructionMovementId()))
                .thenReturn(Optional.empty());

            List<UpdateMoveInstructionLocationPlannedInputDTO> request = List.of(input);

            // Act
            UpdateMoveInstructionLocationPlannedOutputDTO result = service.updateMoveInstructionLocationPlanned(request);

            // Assert
            assertNotNull(result);
            assertEquals(0, result.getRespEstado());
            assertTrue(result.getRespMensaje().contains("MovementInstruction not found for ID"));
        }

        @Test
        @DisplayName("Should throw exception when block not found for instruction")
        void givenBlockNotFound_whenUpdate_thenThrowsException() {
            // Arrange
            UpdateMoveInstructionLocationPlannedInputDTO input = createValidInput();
            MovementInstruction movementInstruction = createMockMovementInstruction();

            when(movementInstructionRepository.findById(input.getInstructionMovementId()))
                .thenReturn(Optional.of(movementInstruction));
            when(containerLocationRepository.findLocationByBlockCellLevel("A1", "01", "01", 1))
                .thenReturn(Optional.empty());
            when(blockRepository.findBlockForInstruction(movementInstruction.getId(), "A1"))
                .thenReturn(Optional.empty());

            List<UpdateMoveInstructionLocationPlannedInputDTO> request = List.of(input);

            // Act
            UpdateMoveInstructionLocationPlannedOutputDTO result = service.updateMoveInstructionLocationPlanned(request);

            // Assert
            assertNotNull(result);
            assertEquals(0, result.getRespEstado());
            assertTrue(result.getRespMensaje().contains("Block not found for code A1"));
        }

        @Test
        @DisplayName("Should throw exception when cell not found")
        void givenCellNotFound_whenUpdate_thenThrowsException() {
            // Arrange
            UpdateMoveInstructionLocationPlannedInputDTO input = createValidInput();
            MovementInstruction movementInstruction = createMockMovementInstruction();
            Block block = createMockBlock(1, "A1");

            when(movementInstructionRepository.findById(input.getInstructionMovementId()))
                .thenReturn(Optional.of(movementInstruction));
            when(containerLocationRepository.findLocationByBlockCellLevel("A1", "01", "01", 1))
                .thenReturn(Optional.empty());
            when(blockRepository.findBlockForInstruction(movementInstruction.getId(), "A1"))
                .thenReturn(Optional.of(block));
            when(cellRepository.findByBlockAndRowColumn(block.getId(), "01", "01"))
                .thenReturn(Optional.empty());

            List<UpdateMoveInstructionLocationPlannedInputDTO> request = List.of(input);

            // Act
            UpdateMoveInstructionLocationPlannedOutputDTO result = service.updateMoveInstructionLocationPlanned(request);

            // Assert
            assertNotNull(result);
            assertEquals(0, result.getRespEstado());
            assertTrue(result.getRespMensaje().contains("Cell not found for blockId=1"));
        }

        @Test
        @DisplayName("Should throw exception when level not found")
        void givenLevelNotFound_whenUpdate_thenThrowsException() {
            // Arrange
            UpdateMoveInstructionLocationPlannedInputDTO input = createValidInput();
            MovementInstruction movementInstruction = createMockMovementInstruction();
            Block block = createMockBlock(1, "A1");
            Cell cell = createMockCell(1, "01", "01");

            when(movementInstructionRepository.findById(input.getInstructionMovementId()))
                .thenReturn(Optional.of(movementInstruction));
            when(containerLocationRepository.findLocationByBlockCellLevel("A1", "01", "01", 1))
                .thenReturn(Optional.empty());
            when(blockRepository.findBlockForInstruction(movementInstruction.getId(), "A1"))
                .thenReturn(Optional.of(block));
            when(cellRepository.findByBlockAndRowColumn(block.getId(), "01", "01"))
                .thenReturn(Optional.of(cell));
            when(levelRepository.findByCellAndIndex(cell.getId(), 1))
                .thenReturn(Optional.empty());

            List<UpdateMoveInstructionLocationPlannedInputDTO> request = List.of(input);

            // Act
            UpdateMoveInstructionLocationPlannedOutputDTO result = service.updateMoveInstructionLocationPlanned(request);

            // Assert
            assertNotNull(result);
            assertEquals(0, result.getRespEstado());
            assertTrue(result.getRespMensaje().contains("Level not found for cellId=1"));
        }
    }

    @Nested
    @DisplayName("Multiple Instructions Processing Tests")
    class MultipleInstructionsTests {

        @Test
        @DisplayName("Should successfully process multiple instructions")
        void givenMultipleValidInputs_whenUpdate_thenProcessesAll() {
            // Arrange
            UpdateMoveInstructionLocationPlannedInputDTO input1 = createValidInput();
            input1.setInstructionMovementId(1);
            
            UpdateMoveInstructionLocationPlannedInputDTO input2 = createValidInput();
            input2.setInstructionMovementId(2);
            input2.setBlockCode("B2");

            MovementInstruction movementInstruction1 = createMockMovementInstruction();
            movementInstruction1.setId(1);
            MovementInstruction movementInstruction2 = createMockMovementInstruction();
            movementInstruction2.setId(2);

            ContainerLocation location1 = createMockContainerLocation(1, "A1", "01", "01", 1);
            ContainerLocation location2 = createMockContainerLocation(2, "B2", "01", "01", 1);

            when(movementInstructionRepository.findById(1))
                .thenReturn(Optional.of(movementInstruction1));
            when(movementInstructionRepository.findById(2))
                .thenReturn(Optional.of(movementInstruction2));
            when(containerLocationRepository.findLocationByBlockCellLevel("A1", "01", "01", 1))
                .thenReturn(Optional.of(location1));
            when(containerLocationRepository.findLocationByBlockCellLevel("B2", "01", "01", 1))
                .thenReturn(Optional.of(location2));
            when(movementInstructionRepository.save(any(MovementInstruction.class)))
                .thenReturn(movementInstruction1, movementInstruction2);

            List<UpdateMoveInstructionLocationPlannedInputDTO> request = List.of(input1, input2);

            // Act
            UpdateMoveInstructionLocationPlannedOutputDTO result = service.updateMoveInstructionLocationPlanned(request);

            // Assert
            assertNotNull(result);
            assertEquals(1, result.getRespEstado());
            assertEquals("Success", result.getRespMensaje());

            verify(movementInstructionRepository, times(2)).findById(any());
            verify(movementInstructionRepository, times(2)).save(any(MovementInstruction.class));
        }

        @Test
        @DisplayName("Should handle partial failure in multiple instructions")
        void givenMultipleInputsWithOneFailure_whenUpdate_thenReturnsError() {
            // Arrange
            UpdateMoveInstructionLocationPlannedInputDTO input1 = createValidInput();
            input1.setInstructionMovementId(1);
            
            UpdateMoveInstructionLocationPlannedInputDTO input2 = createValidInput();
            input2.setInstructionMovementId(999); // Non-existent ID

            MovementInstruction movementInstruction1 = createMockMovementInstruction();
            ContainerLocation location1 = createMockContainerLocation(1, "A1", "01", "01", 1);

            when(movementInstructionRepository.findById(1))
                .thenReturn(Optional.of(movementInstruction1));
            when(movementInstructionRepository.findById(999))
                .thenReturn(Optional.empty());
            when(containerLocationRepository.findLocationByBlockCellLevel("A1", "01", "01", 1))
                .thenReturn(Optional.of(location1));

            List<UpdateMoveInstructionLocationPlannedInputDTO> request = List.of(input1, input2);

            // Act
            UpdateMoveInstructionLocationPlannedOutputDTO result = service.updateMoveInstructionLocationPlanned(request);

            // Assert
            assertNotNull(result);
            assertEquals(0, result.getRespEstado());
            assertTrue(result.getRespMensaje().contains("MovementInstruction not found for ID: 999"));
        }
    }

    @Nested
    @DisplayName("40ft Location Handling Tests")
    class Location40Tests {

        @Test
        @DisplayName("Should handle null 40ft location parameters")
        void givenNull40ftParameters_whenFindLocation_thenReturnsNull() {
            // Arrange
            UpdateMoveInstructionLocationPlannedInputDTO input = createValidInput();
            input.setBlock40Code(null);
            input.setRow40(null);
            input.setColumn40(null);
            input.setLevel40Index(null);

            MovementInstruction movementInstruction = createMockMovementInstruction();
            ContainerLocation normalLocation = createMockContainerLocation(1, "A1", "01", "01", 1);

            when(movementInstructionRepository.findById(input.getInstructionMovementId()))
                .thenReturn(Optional.of(movementInstruction));
            when(containerLocationRepository.findLocationByBlockCellLevel("A1", "01", "01", 1))
                .thenReturn(Optional.of(normalLocation));
            when(movementInstructionRepository.save(any(MovementInstruction.class)))
                .thenReturn(movementInstruction);

            List<UpdateMoveInstructionLocationPlannedInputDTO> request = List.of(input);

            // Act
            UpdateMoveInstructionLocationPlannedOutputDTO result = service.updateMoveInstructionLocationPlanned(request);

            // Assert
            assertNotNull(result);
            assertEquals(1, result.getRespEstado());
            assertNull(movementInstruction.getDestination40Block());
            assertNull(movementInstruction.getDestination40Cell());
            assertNull(movementInstruction.getDestination40Level());
        }

        @Test
        @DisplayName("Should handle empty 40ft location parameters")
        void givenEmpty40ftParameters_whenFindLocation_thenReturnsNull() {
            // Arrange
            UpdateMoveInstructionLocationPlannedInputDTO input = createValidInput();
            input.setBlock40Code("");
            input.setRow40("  ");
            input.setColumn40("");
            input.setLevel40Index(null);

            MovementInstruction movementInstruction = createMockMovementInstruction();
            ContainerLocation normalLocation = createMockContainerLocation(1, "A1", "01", "01", 1);

            when(movementInstructionRepository.findById(input.getInstructionMovementId()))
                .thenReturn(Optional.of(movementInstruction));
            when(containerLocationRepository.findLocationByBlockCellLevel("A1", "01", "01", 1))
                .thenReturn(Optional.of(normalLocation));
            when(movementInstructionRepository.save(any(MovementInstruction.class)))
                .thenReturn(movementInstruction);

            List<UpdateMoveInstructionLocationPlannedInputDTO> request = List.of(input);

            // Act
            UpdateMoveInstructionLocationPlannedOutputDTO result = service.updateMoveInstructionLocationPlanned(request);

            // Assert
            assertNotNull(result);
            assertEquals(1, result.getRespEstado());
            assertNull(movementInstruction.getDestination40Block());
            assertNull(movementInstruction.getDestination40Cell());
            assertNull(movementInstruction.getDestination40Level());
        }

        @Test
        @DisplayName("Should handle 40ft location not found")
        void given40ftLocationNotFound_whenUpdate_thenSetsNullValues() {
            // Arrange
            UpdateMoveInstructionLocationPlannedInputDTO input = createValidInputWith40ft();
            MovementInstruction movementInstruction = createMockMovementInstruction();
            ContainerLocation normalLocation = createMockContainerLocation(1, "A1", "01", "01", 1);

            when(movementInstructionRepository.findById(input.getInstructionMovementId()))
                .thenReturn(Optional.of(movementInstruction));
            when(containerLocationRepository.findLocationByBlockCellLevel("A1", "01", "01", 1))
                .thenReturn(Optional.of(normalLocation));
            when(containerLocationRepository.findLocationByBlockCellLevel("B2", "02", "02", 2))
                .thenReturn(Optional.empty());
            when(movementInstructionRepository.save(any(MovementInstruction.class)))
                .thenReturn(movementInstruction);

            List<UpdateMoveInstructionLocationPlannedInputDTO> request = List.of(input);

            // Act
            UpdateMoveInstructionLocationPlannedOutputDTO result = service.updateMoveInstructionLocationPlanned(request);

            // Assert
            assertNotNull(result);
            assertEquals(1, result.getRespEstado());
            assertNull(movementInstruction.getDestination40Block());
            assertNull(movementInstruction.getDestination40Cell());
            assertNull(movementInstruction.getDestination40Level());
        }
    }

    @Nested
    @DisplayName("Edge Cases and Error Handling Tests")
    class EdgeCasesTests {

        @Test
        @DisplayName("Should handle empty request list")
        void givenEmptyRequestList_whenUpdate_thenReturnsSuccess() {
            // Arrange
            List<UpdateMoveInstructionLocationPlannedInputDTO> request = List.of();

            // Act
            UpdateMoveInstructionLocationPlannedOutputDTO result = service.updateMoveInstructionLocationPlanned(request);

            // Assert
            assertNotNull(result);
            assertEquals(1, result.getRespEstado());
            assertEquals("Success", result.getRespMensaje());

            verifyNoInteractions(movementInstructionRepository);
            verifyNoInteractions(containerLocationRepository);
        }

        @Test
        @DisplayName("Should handle database exception during save")
        void givenDatabaseException_whenUpdate_thenReturnsError() {
            // Arrange
            UpdateMoveInstructionLocationPlannedInputDTO input = createValidInput();
            MovementInstruction movementInstruction = createMockMovementInstruction();
            ContainerLocation normalLocation = createMockContainerLocation(1, "A1", "01", "01", 1);

            when(movementInstructionRepository.findById(input.getInstructionMovementId()))
                .thenReturn(Optional.of(movementInstruction));
            when(containerLocationRepository.findLocationByBlockCellLevel("A1", "01", "01", 1))
                .thenReturn(Optional.of(normalLocation));
            when(movementInstructionRepository.save(any(MovementInstruction.class)))
                .thenThrow(new RuntimeException("Database connection failed"));

            List<UpdateMoveInstructionLocationPlannedInputDTO> request = List.of(input);

            // Act
            UpdateMoveInstructionLocationPlannedOutputDTO result = service.updateMoveInstructionLocationPlanned(request);

            // Assert
            assertNotNull(result);
            assertEquals(0, result.getRespEstado());
            assertEquals("Database connection failed", result.getRespMensaje());
        }
    }

    // Helper methods for creating test data
    private UpdateMoveInstructionLocationPlannedInputDTO createValidInput() {
        UpdateMoveInstructionLocationPlannedInputDTO input = new UpdateMoveInstructionLocationPlannedInputDTO();
        input.setInstructionMovementId(1);
        input.setBlockCode("A1");
        input.setRow("01");
        input.setColumn("01");
        input.setLevelIndex(1);
        input.setUserId(100);
        return input;
    }

    private UpdateMoveInstructionLocationPlannedInputDTO createValidInputWith40ft() {
        UpdateMoveInstructionLocationPlannedInputDTO input = createValidInput();
        input.setBlock40Code("B2");
        input.setRow40("02");
        input.setColumn40("02");
        input.setLevel40Index(2);
        return input;
    }

    private MovementInstruction createMockMovementInstruction() {
        MovementInstruction instruction = new MovementInstruction();
        instruction.setId(1);
        instruction.setActive(true);
        instruction.setRegistrationDate(LocalDateTime.now());
        return instruction;
    }

    private ContainerLocation createMockContainerLocation(Integer id, String blockCode, String row, String column, Integer levelIndex) {
        ContainerLocation location = new ContainerLocation();
        location.setId(id);
        location.setBlock(createMockBlock(id, blockCode));
        location.setCell(createMockCell(id, row, column));
        location.setLevel(createMockLevel(id, levelIndex));
        location.setActive(true);
        return location;
    }

    private Block createMockBlock(Integer id, String code) {
        Block block = new Block();
        block.setId(id);
        block.setCode(code);
        block.setActive(true);
        return block;
    }

    private Cell createMockCell(Integer id, String row, String column) {
        Cell cell = new Cell();
        cell.setId(id);
        cell.setRow(row);
        cell.setColumn(column);
        cell.setActive(true);
        return cell;
    }

    private Level createMockLevel(Integer id, Integer index) {
        Level level = new Level();
        level.setId(id);
        level.setIndex(index);
        level.setActive(true);
        return level;
    }
}
