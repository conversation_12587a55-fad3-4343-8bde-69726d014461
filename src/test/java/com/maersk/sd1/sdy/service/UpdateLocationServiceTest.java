package com.maersk.sd1.sdy.service;

import com.maersk.sd1.sdy.dto.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.*;

class UpdateLocationServiceTest {

    @InjectMocks
    private UpdateLocationService updateLocationService;

    @Mock
    private ContainerMovementLocationValidateService containerMovementLocationValidateService;

    @Mock
    private PlanificacionUbicacionDestinoService planificacionUbicacionDestinoService;

    @Mock
    private PlanificacionInstruccionMovimientoAtenderService planificacionInstruccionMovimientoAtenderService;

    @Mock
    private ConfirmMovementInstructionService confirmMovementInstructionService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void updateLocation_ShouldHandleValidationErrorGracefully() {
        UpdateLocationInputDTO.Input input = new UpdateLocationInputDTO.Input();
        UpdateLocationInputDTO.ContainerVisitRequest visitRequest = new UpdateLocationInputDTO.ContainerVisitRequest();
        visitRequest.setContainerId(101);
        visitRequest.setContainerNumber("CONT123");
        visitRequest.setInstructionNumber(1001);

        UpdateLocationInputDTO.ContainerLocationValue location = new UpdateLocationInputDTO.ContainerLocationValue();
        location.setBlockCode("B1");
        location.setRow("R1");
        location.setColumn("C1");
        location.setLevel(1);
        visitRequest.setLocation(location);

        List<UpdateLocationInputDTO.ContainerVisitRequest> visitRequests = new ArrayList<>();
        visitRequests.add(visitRequest);
        input.setContainers(visitRequests);

        ContainerMovementLocationValidateOutput validateOutput = new ContainerMovementLocationValidateOutput();
        validateOutput.setStatusCode(0);
        validateOutput.setMessage("Validation Failed");

        when(containerMovementLocationValidateService.validateContainerMovement(any())).thenReturn(validateOutput);

        UpdateLocationOutputDTO output = updateLocationService.updateLocation(input);

        assertNotNull(output);
        assertEquals(0, output.getContainers().size());
    }

    @Test
    void planDestinationLoacation_ShouldCallServiceForEachContainer() {
        // Arrange
        UpdateLocationInputDTO.Input input = new UpdateLocationInputDTO.Input();
        input.setUserId(1);

        UpdateLocationInputDTO.ContainerVisitRequest container1 = new UpdateLocationInputDTO.ContainerVisitRequest();
        container1.setInstructionNumber(1001);
        UpdateLocationInputDTO.ContainerLocationValue location1 = new UpdateLocationInputDTO.ContainerLocationValue();
        location1.setBlockCode("B1");
        location1.setRow("R1");
        location1.setColumn("C1");
        location1.setLevel(1);
        location1.setBlockCode40("B40-1");
        location1.setRow40("R40-1");
        location1.setColumn40("C40-1");
        location1.setLevel40(2);
        container1.setLocation(location1);

        UpdateLocationInputDTO.ContainerVisitRequest container2 = new UpdateLocationInputDTO.ContainerVisitRequest();
        container2.setInstructionNumber(1002);
        UpdateLocationInputDTO.ContainerLocationValue location2 = new UpdateLocationInputDTO.ContainerLocationValue();
        location2.setBlockCode("B2");
        location2.setRow("R2");
        location2.setColumn("C2");
        location2.setLevel(2);
        location2.setBlockCode40("B40-2");
        location2.setRow40("R40-2");
        location2.setColumn40("C40-2");
        location2.setLevel40(3);
        container2.setLocation(location2);

        List<UpdateLocationInputDTO.ContainerVisitRequest> containers = new ArrayList<>();
        containers.add(container1);
        containers.add(container2);
        input.setContainers(containers);

        updateLocationService.planDestinationLoacation(input);

        verify(planificacionUbicacionDestinoService, times(2)).updateUbicacionDestino(any(PlanificacionUbicacionDestinoInput.Input.class));
        verify(planificacionUbicacionDestinoService).updateUbicacionDestino(argThat(planInput ->
                planInput.getInstruccionMovimientoId() == 1001 &&
                        planInput.getBloqueCodigo().equals("B1") &&
                        planInput.getFila().equals("R1") &&
                        planInput.getColumna().equals("C1") &&
                        planInput.getNivel() == 1 &&
                        planInput.getBloque40Codigo().equals("B40-1") &&
                        planInput.getFila40().equals("R40-1") &&
                        planInput.getColumna40().equals("C40-1") &&
                        planInput.getNivel40() == 2 &&
                        planInput.getUsuarioId() == 1
        ));
        verify(planificacionUbicacionDestinoService).updateUbicacionDestino(argThat(planInput ->
                planInput.getInstruccionMovimientoId() == 1002 &&
                        planInput.getBloqueCodigo().equals("B2") &&
                        planInput.getFila().equals("R2") &&
                        planInput.getColumna().equals("C2") &&
                        planInput.getNivel() == 2 &&
                        planInput.getBloque40Codigo().equals("B40-2") &&
                        planInput.getFila40().equals("R40-2") &&
                        planInput.getColumna40().equals("C40-2") &&
                        planInput.getNivel40() == 3 &&
                        planInput.getUsuarioId() == 1
        ));
    }

    @Test
    void attendMovementInstruction_ShouldCallProcessMovements() {
        UpdateLocationInputDTO.Input input = new UpdateLocationInputDTO.Input();
        input.setUserId(1);

        UpdateLocationInputDTO.ContainerVisitRequest container1 = new UpdateLocationInputDTO.ContainerVisitRequest();
        container1.setInstructionNumber(1001);

        UpdateLocationInputDTO.ContainerVisitRequest container2 = new UpdateLocationInputDTO.ContainerVisitRequest();
        container2.setInstructionNumber(1002);

        List<UpdateLocationInputDTO.ContainerVisitRequest> containers = new ArrayList<>();
        containers.add(container1);
        containers.add(container2);
        input.setContainers(containers);

        updateLocationService.attendMovementInstruction(input);

        verify(planificacionInstruccionMovimientoAtenderService, times(1)).processMovements(argThat(atenderRoot -> {
            PlanificacionInstruccionMovimientoAtenderInput.Prefix prefix = atenderRoot.getPrefix();
            PlanificacionInstruccionMovimientoAtenderInput.Input atenderInput = prefix.getInput();
            List<PlanificacionInstruccionMovimientoAtenderInput.Instrucciones> instructions = atenderInput.getInstruccionesMovimiento();

            return atenderInput.getUsuarioId() == 1 &&
                    instructions.size() == 2 &&
                    instructions.get(0).getId() == 1001 &&
                    instructions.get(1).getId() == 1002;
        }));
    }
}
