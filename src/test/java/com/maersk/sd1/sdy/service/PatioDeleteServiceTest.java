package com.maersk.sd1.sdy.service;

import com.maersk.sd1.common.model.Layout;
import com.maersk.sd1.common.model.Yard;
import com.maersk.sd1.common.repository.LayoutRepository;
import com.maersk.sd1.common.repository.MessageLanguageRepository;
import com.maersk.sd1.common.repository.YardRepository;
import com.maersk.sd1.sdy.dto.PatioDeleteOutput;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

class PatioDeleteServiceTest {

    @Mock
    private YardRepository yardRepository;

    @Mock
    private LayoutRepository layoutRepository;

    @Mock
    private MessageLanguageRepository messageLanguageRepository;

    @InjectMocks
    private PatioDeleteService patioDeleteService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testDeletePatioSuccess() {
        Integer yardId = 1;
        Integer userModificationId = 2;
        Integer languageId = 1;

        Yard yard = new Yard();
        yard.setId(yardId);
        yard.setActive(true);
        Layout layout = new Layout();
        layout.setId(10);
        layout.setConfiguration("[{\"patio_id\":1,\"color\":\"red\"}]");
        yard.setLayout(layout);

        when(yardRepository.findById(yardId)).thenReturn(Optional.of(yard));
        when(layoutRepository.findById(layout.getId())).thenReturn(Optional.of(layout));
        when(messageLanguageRepository.fnTranslatedMessage("GENERAL", 7, languageId)).thenReturn("Success message");

        PatioDeleteOutput result = patioDeleteService.deletePatio(yardId, userModificationId, languageId);

        assertEquals(1, result.getRespEstado());
        assertEquals("Success message", result.getRespMensaje());
        assertFalse(yard.getActive());
        verify(yardRepository).save(yard);
        verify(layoutRepository).save(layout);
    }

    @Test
    void testDeletePatioYardNotFound() {
        Integer yardId = 1;
        Integer userModificationId = 2;
        Integer languageId = 1;

        when(yardRepository.findById(yardId)).thenReturn(Optional.empty());

        PatioDeleteOutput result = patioDeleteService.deletePatio(yardId, userModificationId, languageId);

        assertEquals(0, result.getRespEstado());
        assertTrue(result.getRespMensaje().contains("No Yard found for id"));
        verify(yardRepository, never()).save(any());
        verify(layoutRepository, never()).save(any());
    }
}