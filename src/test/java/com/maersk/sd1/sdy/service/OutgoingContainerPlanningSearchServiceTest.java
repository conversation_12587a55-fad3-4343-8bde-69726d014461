package com.maersk.sd1.sdy.service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.maersk.sd1.common.dto.SystemRuleMergedShippingLinesDTO;
import com.maersk.sd1.common.model.*;
import com.maersk.sd1.common.repository.*;
import com.maersk.sd1.sdy.dto.OutgoingContainerPlanningSearchInput;
import com.maersk.sd1.sdy.dto.OutgoingContainerPlanningSearchOutput;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import org.mockito.InjectMocks;
import org.mockito.Mock;

import java.time.LocalDateTime;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;

import org.mockito.MockitoAnnotations;

class OutgoingContainerPlanningSearchServiceTest {

    @Mock private ContainerRepository containerRepository;
    @Mock private ContainerLocationRepository containerLocationRepository;
    @Mock private BookingRepository bookingRepository;
    @Mock private SystemRuleRepository systemRuleRepository;
    @Mock private BusinessUnitRepository businessUnitRepository;
    @Mock private EirRepository eirRepository;
    @Mock private EirActivityZoneRepository eirActivityZoneRepository;
    @Mock private ObjectMapper objectMapper;

    @InjectMocks
    private OutgoingContainerPlanningSearchService service;

    @BeforeEach
    void setup() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    @DisplayName("Should return empty result when valid input provided but no containers found")
    void givenValidInput_whenSearch_thenReturnsOutput() {
        OutgoingContainerPlanningSearchInput.Input input = new OutgoingContainerPlanningSearchInput.Input();
        input.setUnidadNegocioId(1);
        input.setBooking("BKG123");
        input.setContenedorReferencia("CONT123");
        input.setContenedorTamanoId(20);
        input.setContenedorTipoId(1);

        BusinessUnit unit = new BusinessUnit();
        unit.setId(1);
        when(businessUnitRepository.findById(1)).thenReturn(Optional.of(unit));
        when(systemRuleRepository.findRuleByIdAndBusinessUnitIdAndActiveTrue("sdy_disponibilidad_sde", 1)).thenReturn(null);

        Booking booking = new Booking();
        ShippingLine shippingLine = new ShippingLine();
        shippingLine.setId(99);
        booking.setShippingLine(shippingLine);
        when(bookingRepository.findByBookingNumber("BKG123")).thenReturn(Optional.of(booking));

        when(systemRuleRepository.findByAlias("sdy_lineas_grupo_maersk_fs")).thenReturn(null);

        when(containerLocationRepository.findReferenceContainerLocationByContainerNumber("CONT123")).thenReturn(Optional.empty());

        when(containerRepository.findEligibleContainersForOutgoingPlanning(
            any(), any(), any(), any(), any(), any(), any(), any(), any(), any()))
            .thenReturn(Collections.emptyList());

        OutgoingContainerPlanningSearchOutput result = service.searchOutgoingContainerPlanning(input);

        assertNotNull(result);
        assertNotNull(result.getContenedores());
        assertEquals(0, result.getContenedores().size());
    }

    @Test
    @DisplayName("Should enable yard by default when business unit is missing")
    void givenMissingBusinessUnit_whenValidateYard_thenYardEnabledByDefault() {
        when(businessUnitRepository.findById(123)).thenReturn(Optional.empty());

        OutgoingContainerPlanningSearchInput.Input input = new OutgoingContainerPlanningSearchInput.Input();
        input.setUnidadNegocioId(123);
        input.setBooking("BK001");
        input.setContenedorTamanoId(20);
        input.setContenedorTipoId(1);

        when(bookingRepository.findByBookingNumber("BK001")).thenReturn(Optional.empty());
        when(systemRuleRepository.findByAlias("sdy_lineas_grupo_maersk_fs")).thenReturn(null);
        when(containerRepository.findEligibleContainersForOutgoingPlanning(
            any(), any(), any(), any(), any(), any(), any(), any(), any(), any()))
            .thenReturn(Collections.emptyList());

        OutgoingContainerPlanningSearchOutput result = service.searchOutgoingContainerPlanning(input);

        assertNotNull(result);
        assertNotNull(result.getContenedores());
        assertEquals(0, result.getContenedores().size());
    }

    @Test
    @DisplayName("Should handle missing booking gracefully")
    void givenBookingWithoutShippingLine_whenSearch_thenShippingLineIdIsNull() {
        OutgoingContainerPlanningSearchInput.Input input = new OutgoingContainerPlanningSearchInput.Input();
        input.setUnidadNegocioId(1);
        input.setBooking("INVALID_BOOKING");
        input.setContenedorTamanoId(20);
        input.setContenedorTipoId(1);

        when(businessUnitRepository.findById(1)).thenReturn(Optional.of(new BusinessUnit()));
        when(systemRuleRepository.findRuleByIdAndBusinessUnitIdAndActiveTrue(anyString(), anyInt())).thenReturn(null);
        when(bookingRepository.findByBookingNumber("INVALID_BOOKING")).thenReturn(Optional.empty());
        when(systemRuleRepository.findByAlias("sdy_lineas_grupo_maersk_fs")).thenReturn(null);
        when(containerRepository.findEligibleContainersForOutgoingPlanning(
            any(), any(), any(), any(), any(), any(), any(), any(), any(), any()))
            .thenReturn(Collections.emptyList());

        OutgoingContainerPlanningSearchOutput result = service.searchOutgoingContainerPlanning(input);

        assertNotNull(result);
        assertNotNull(result.getContenedores());
        assertEquals(0, result.getContenedores().size());
    }

    @Test
    @DisplayName("Should return containers when found with reference location")
    void givenValidInputWithContainers_whenSearch_thenReturnsContainers()  {
        OutgoingContainerPlanningSearchInput.Input input = new OutgoingContainerPlanningSearchInput.Input();
        input.setUnidadNegocioId(1);
        input.setBooking("BKG123");
        input.setContenedorReferencia("CONT123");
        input.setContenedorTamanoId(20);
        input.setContenedorTipoId(1);
        input.setCantidadRegistros(5);

        BusinessUnit unit = new BusinessUnit();
        unit.setId(1);
        when(businessUnitRepository.findById(1)).thenReturn(Optional.of(unit));
        when(systemRuleRepository.findRuleByIdAndBusinessUnitIdAndActiveTrue("sdy_disponibilidad_sde", 1)).thenReturn(null);

        Booking booking = new Booking();
        ShippingLine shippingLine = new ShippingLine();
        shippingLine.setId(99);
        shippingLine.setShippingLineCompany("MAERSK");
        booking.setShippingLine(shippingLine);
        when(bookingRepository.findByBookingNumber("BKG123")).thenReturn(Optional.of(booking));

        when(systemRuleRepository.findByAlias("sdy_lineas_grupo_maersk_fs")).thenReturn(null);

        ContainerLocation refLocation = createMockContainerLocation("A", "01", "01", 1, 1, 1, 1);
        when(containerLocationRepository.findReferenceContainerLocationByContainerNumber("CONT123"))
            .thenReturn(Optional.of(refLocation));

        List<Container> containers = createMockContainers();
        when(containerRepository.findEligibleContainersForOutgoingPlanning(
            any(), any(), any(), any(), any(), any(), any(), any(), any(), any()))
            .thenReturn(containers);

        for (Container container : containers) {
            List<ContainerLocation> locations = List.of(createMockContainerLocation("B", "02", "02", 2, 2, 2, 0));
            when(containerLocationRepository.findByContainerIdAndActiveTrue(container.getId()))
                .thenReturn(locations);
        }

        mockEirData();

        OutgoingContainerPlanningSearchOutput result = service.searchOutgoingContainerPlanning(input);

        assertNotNull(result);
        assertNotNull(result.getContenedores());
        assertEquals(2, result.getContenedores().size());

        var firstContainer = result.getContenedores().get(0);
        assertNotNull(firstContainer.getContenedorId());
        assertNotNull(firstContainer.getNumeroContenedor());
        assertNotNull(firstContainer.getLineaNaviera());
    }

    @Test
    @DisplayName("Should handle yard availability rule correctly")
    void givenYardAvailabilityRule_whenSearch_thenProcessesCorrectly() throws Exception {
        OutgoingContainerPlanningSearchInput.Input input = new OutgoingContainerPlanningSearchInput.Input();
        input.setUnidadNegocioId(1);
        input.setBooking("BKG123");
        input.setContenedorTamanoId(20);
        input.setContenedorTipoId(1);

        BusinessUnit parentUnit = new BusinessUnit();
        parentUnit.setId(100);
        BusinessUnit unit = new BusinessUnit();
        unit.setId(1);
        unit.setParentBusinessUnit(parentUnit);
        when(businessUnitRepository.findById(1)).thenReturn(Optional.of(unit));

        String yardRule = "[{\"unidad_negocio_id\": 1, \"activo\": true}]";
        when(systemRuleRepository.findRuleByIdAndBusinessUnitIdAndActiveTrue("sdy_disponibilidad_sde", 100))
            .thenReturn(yardRule);

        JsonNode mockNode = mock(JsonNode.class);
        JsonNode arrayNode = mock(JsonNode.class);
        when(objectMapper.readTree(yardRule)).thenReturn(arrayNode);
        when(arrayNode.isArray()).thenReturn(true);
        when(arrayNode.iterator()).thenReturn(List.of(mockNode).iterator());
        when(mockNode.has("unidad_negocio_id")).thenReturn(true);
        when(mockNode.get("unidad_negocio_id")).thenReturn(mockNode);
        when(mockNode.asInt()).thenReturn(1);
        when(mockNode.has("activo")).thenReturn(true);
        when(mockNode.get("activo")).thenReturn(mockNode);
        when(mockNode.asBoolean()).thenReturn(true);

        when(bookingRepository.findByBookingNumber("BKG123")).thenReturn(Optional.empty());
        when(systemRuleRepository.findByAlias("sdy_lineas_grupo_maersk_fs")).thenReturn(null);
        when(containerRepository.findEligibleContainersForOutgoingPlanning(
            any(), any(), any(), any(), any(), any(), any(), any(), any(), any()))
            .thenReturn(Collections.emptyList());

        OutgoingContainerPlanningSearchOutput result = service.searchOutgoingContainerPlanning(input);

        assertNotNull(result);
        verify(systemRuleRepository).findRuleByIdAndBusinessUnitIdAndActiveTrue("sdy_disponibilidad_sde", 100);
    }

    @Test
    @DisplayName("Should handle merged shipping lines correctly")
    void givenMergedShippingLines_whenSearch_thenProcessesCorrectly() throws Exception {
        OutgoingContainerPlanningSearchInput.Input input = new OutgoingContainerPlanningSearchInput.Input();
        input.setUnidadNegocioId(1);
        input.setBooking("BKG123");
        input.setContenedorTamanoId(20);
        input.setContenedorTipoId(1);

        when(businessUnitRepository.findById(1)).thenReturn(Optional.of(new BusinessUnit()));
        when(systemRuleRepository.findRuleByIdAndBusinessUnitIdAndActiveTrue(anyString(), anyInt())).thenReturn(null);
        when(bookingRepository.findByBookingNumber("BKG123")).thenReturn(Optional.empty());

        SystemRule mergedRule = new SystemRule();
        String ruleJson = "[{\"linea_id\": 1, \"nombre\": \"MAERSK\"}, {\"linea_id\": 2, \"nombre\": \"MSC\"}]";
        mergedRule.setRule(ruleJson);
        when(systemRuleRepository.findByAlias("sdy_lineas_grupo_maersk_fs")).thenReturn(mergedRule);

        List<SystemRuleMergedShippingLinesDTO.MergedDetail> mergedDetails = List.of(
            createMergedDetail(1, "MAERSK"),
            createMergedDetail(2, "MSC")
        );
        when(objectMapper.readValue(eq(ruleJson), any(TypeReference.class))).thenReturn(mergedDetails);

        when(containerRepository.findEligibleContainersForOutgoingPlanning(
            any(), any(), eq(List.of(1, 2)), any(), any(), any(), any(), any(), any(), any()))
            .thenReturn(Collections.emptyList());

        OutgoingContainerPlanningSearchOutput result = service.searchOutgoingContainerPlanning(input);

        assertNotNull(result);
        verify(objectMapper).readValue(eq(ruleJson), any(TypeReference.class));
    }

    @Test
    @DisplayName("Should handle exception gracefully")
    void givenExceptionInProcessing_whenSearch_thenThrowsRuntimeException() {
        OutgoingContainerPlanningSearchInput.Input input = new OutgoingContainerPlanningSearchInput.Input();
        input.setUnidadNegocioId(1);
        input.setBooking("BKG123");
        input.setContenedorTamanoId(20);
        input.setContenedorTipoId(1);

        when(businessUnitRepository.findById(1)).thenReturn(Optional.of(new BusinessUnit()));
        when(systemRuleRepository.findRuleByIdAndBusinessUnitIdAndActiveTrue(anyString(), anyInt())).thenReturn(null);
        when(bookingRepository.findByBookingNumber("BKG123")).thenReturn(Optional.empty());
        when(systemRuleRepository.findByAlias("sdy_lineas_grupo_maersk_fs")).thenReturn(null);

        when(containerRepository.findEligibleContainersForOutgoingPlanning(
            any(), any(), any(), any(), any(), any(), any(), any(), any(), any()))
            .thenThrow(new RuntimeException("Database error"));

        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            service.searchOutgoingContainerPlanning(input);
        });

        assertEquals("Error searching outgoing container planning", exception.getMessage());
    }

    private List<Container> createMockContainers() {
        Container container1 = new Container();
        container1.setId(1);
        container1.setContainerNumber("CONT001");
        container1.setMaximunPayload(25000);

        Catalog sizeCategory = new Catalog();
        sizeCategory.setDescription("20");
        container1.setCatSize(sizeCategory);

        Catalog typeCategory = new Catalog();
        typeCategory.setDescription("DRY");
        container1.setCatContainerType(typeCategory);

        ShippingLine shippingLine = new ShippingLine();
        shippingLine.setShippingLineCompany("MAERSK");
        container1.setShippingLine(shippingLine);

        Container container2 = new Container();
        container2.setId(2);
        container2.setContainerNumber("CONT002");
        container2.setMaximunPayload(30000);
        container2.setCatSize(sizeCategory);
        container2.setCatContainerType(typeCategory);
        container2.setShippingLine(shippingLine);

        return List.of(container1, container2);
    }

    private ContainerLocation createMockContainerLocation(String blockCode, String column, String row,
                                                         Integer columnIndex, Integer rowIndex, Integer levelIndex, Integer quantityRemoved) {
        ContainerLocation location = new ContainerLocation();

        Block block = new Block();
        block.setCode(blockCode);
        location.setBlock(block);

        Cell cell = new Cell();
        cell.setColumn(column);
        cell.setRow(row);
        cell.setColumnIndex(columnIndex);
        cell.setRowIndex(rowIndex);
        location.setCell(cell);

        Level level = new Level();
        level.setIndex(levelIndex);
        location.setLevel(level);

        location.setQuantityRemoved(quantityRemoved);

        return location;
    }

    private void mockEirData() {
        Eir eir = new Eir();
        eir.setId(1);
        when(eirRepository.findTopByContainer_IdOrderByIdDesc(any())).thenReturn(Optional.of(eir));

        EirActivityZone activityZone = new EirActivityZone();
        activityZone.setStartDate(LocalDateTime.now().minusDays(5));
        when(eirActivityZoneRepository.findTopByEir_IdOrderByIdDesc(1)).thenReturn(Optional.of(activityZone));
    }

    private SystemRuleMergedShippingLinesDTO.MergedDetail createMergedDetail(Integer lineId, String lineIdName) {
        SystemRuleMergedShippingLinesDTO.MergedDetail detail = new SystemRuleMergedShippingLinesDTO.MergedDetail();
        detail.setLineId(lineId);
        detail.setLineIdName(lineIdName);
        return detail;
    }
}
