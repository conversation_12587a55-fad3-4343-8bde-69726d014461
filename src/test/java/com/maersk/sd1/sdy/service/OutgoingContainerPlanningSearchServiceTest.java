package com.maersk.sd1.sdy.service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.maersk.sd1.common.dto.SystemRuleMergedShippingLinesDTO;
import com.maersk.sd1.common.model.*;
import com.maersk.sd1.common.repository.*;
import com.maersk.sd1.sdy.dto.OutgoingContainerPlanningSearchInput;
import com.maersk.sd1.sdy.dto.OutgoingContainerPlanningSearchOutput;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.mockito.InjectMocks;
import org.mockito.Mock;

import java.time.LocalDateTime;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;

import org.mockito.MockitoAnnotations;

public class OutgoingContainerPlanningSearchServiceTest {

    @Mock private ContainerRepository containerRepository;
    @Mock private ContainerLocationRepository containerLocationRepository;
    @Mock private BookingRepository bookingRepository;
    @Mock private SystemRuleRepository systemRuleRepository;
    @Mock private BusinessUnitRepository businessUnitRepository;
    @Mock private EirRepository eirRepository;
    @Mock private EirActivityZoneRepository eirActivityZoneRepository;
    @Mock private ObjectMapper objectMapper;

    @InjectMocks
    private OutgoingContainerPlanningSearchService service;

    @BeforeEach
    public void setup() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    public void givenValidInput_whenSearch_thenReturnsOutput() {
        // Arrange
        OutgoingContainerPlanningSearchInput.Input input = new OutgoingContainerPlanningSearchInput.Input();
        input.setUnidadNegocioId(1);
        input.setBooking("BKG123");
        input.setContenedorReferencia("CONT123");

        BusinessUnit unit = new BusinessUnit();
        unit.setId(1);
        when(businessUnitRepository.findById(1)).thenReturn(Optional.of(unit));
        when(systemRuleRepository.findRuleByIdAndBusinessUnitIdAndActiveTrue("sdy_disponibilidad_sde", 1)).thenReturn(null);

        Booking booking = new Booking();
        ShippingLine shippingLine = new ShippingLine();
        shippingLine.setId(99);
        booking.setShippingLine(shippingLine);
        when(bookingRepository.findByBookingNumber("BKG123")).thenReturn(Optional.of(booking));

        when(systemRuleRepository.findByAlias("sdy_lineas_grupo_maersk_fs")).thenReturn(null);

        // Simulate no reference location found
        when(containerLocationRepository.findReferenceContainerLocationByContainerNumber("CONT123")).thenReturn(Optional.empty());

        // Return an empty container list from the container search logic
        when(containerRepository.findAll()).thenReturn(Collections.emptyList());

        // Act
        OutgoingContainerPlanningSearchOutput result = service.searchOutgoingContainerPlanning(input);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getContenedores());
        assertEquals(0, result.getContenedores().size());
    }

    @Test
    public void givenMissingBusinessUnit_whenValidateYard_thenYardEnabledByDefault() {
        // Arrange
        when(businessUnitRepository.findById(123)).thenReturn(Optional.empty());

        // Act (invoke private method via reflection or through public path)
        OutgoingContainerPlanningSearchInput.Input input = new OutgoingContainerPlanningSearchInput.Input();
        input.setUnidadNegocioId(123);
        input.setBooking("BK001");

        // Should not throw any exception
        OutgoingContainerPlanningSearchOutput result = service.searchOutgoingContainerPlanning(input);

        // Assert
        assertNotNull(result);
    }

    @Test
    public void givenBookingWithoutShippingLine_whenSearch_thenShippingLineIdIsNull() {
        // Arrange
        OutgoingContainerPlanningSearchInput.Input input = new OutgoingContainerPlanningSearchInput.Input();
        input.setUnidadNegocioId(1);
        input.setBooking("INVALID_BOOKING");

        when(businessUnitRepository.findById(1)).thenReturn(Optional.of(new BusinessUnit()));
        when(systemRuleRepository.findRuleByIdAndBusinessUnitIdAndActiveTrue(anyString(), anyInt())).thenReturn(null);
        when(bookingRepository.findByBookingNumber("INVALID_BOOKING")).thenReturn(Optional.empty());

        // Act
        OutgoingContainerPlanningSearchOutput result = service.searchOutgoingContainerPlanning(input);

        // Assert
        assertNotNull(result);
    }
}
