package com.maersk.sd1.sdy.service;


import com.fasterxml.jackson.core.JsonProcessingException;
import com.maersk.sd1.common.repository.CatalogRepository;
import com.maersk.sd1.common.repository.ContainerRepository;
import com.maersk.sd1.sdy.dto.GetEquipmentAdditionalDataInput;
import com.maersk.sd1.sdy.dto.GetEquipmentAdditionalDataOutput;
import com.maersk.sd1.sdy.dto.VisitDTO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.*;

class GetEquipmentAdditionalDataServiceTest {

    private CatalogRepository catalogRepository;
    private ContainerRepository containerRepository;
    private GetEquipmentAdditionalDataService service;

    @BeforeEach
    void setUp() {
        catalogRepository = mock(CatalogRepository.class);
        containerRepository = mock(ContainerRepository.class);
        service = new GetEquipmentAdditionalDataService(catalogRepository, containerRepository);
    }

    @Test
    void givenValidInput_WhenGetEquipmentAdditionalData_ThenReturnSuccess() throws JsonProcessingException {
        // Given
        GetEquipmentAdditionalDataInput.Input input = new GetEquipmentAdditionalDataInput.Input();
        input.setProcess("GO");
        input.setEquipment("[{\"numero_contenedor\":\"AMFU3156454\",\"eir_numero\":\"408274\"}]");

        VisitDTO visit = new VisitDTO();
        visit.setContainerNumber("AMFU3156454");
        visit.setEirNumber("408274");

        List<Object[]> mockResult = new ArrayList<>();
        mockResult.add(new Object[]{
                480822, "AMFU3156454", new BigDecimal("42882"), new BigDecimal("31070"),
                new BigDecimal("31049"), new BigDecimal("42912"), 4104, new BigDecimal("2230"),
                new BigDecimal("30480"), 38, null, null, false, new BigDecimal("49494"),
                null, "Operative", "MSL", "Maersk Line"
        });

        when(catalogRepository.findIdByAlias("sd1_equipment_category_container")).thenReturn(1);
        when(containerRepository.getEquipmentAdditionalData(
                (1), (1), ("408274"), ("AMFU3156454")
        )).thenReturn(mockResult);

        // When
        List<GetEquipmentAdditionalDataOutput> result = service.getEquipmentAdditionalData(input);

        // Then
        assertEquals(1, result.size());
        GetEquipmentAdditionalDataOutput output = result.get(0);
        assertEquals("AMFU3156454", output.getContainerNumber());
        assertEquals(new BigDecimal("2230"), output.getTareWeight());
        assertEquals("MSL", output.getShippingLineCode());
        assertEquals("Maersk Line", output.getShippingLineName());

    }

    @Test
    void givenWithEmptyEquipmentInput_WhenGetEquipmentAdditionalData_ThenThrowException() {
        GetEquipmentAdditionalDataInput.Input input = new GetEquipmentAdditionalDataInput.Input();
        input.setProcess("GO");
        input.setEquipment("[]");

        IllegalArgumentException exception = org.junit.jupiter.api.Assertions.assertThrows(
                IllegalArgumentException.class,
                () -> service.getEquipmentAdditionalData(input)
        );

        assertEquals("No visits provided in input document.", exception.getMessage());
    }


    @Test
    void givenWithInvalidJsonInput_WhenGetEquipmentAdditionalData_ThenThrowException() {
        GetEquipmentAdditionalDataInput.Input input = new GetEquipmentAdditionalDataInput.Input();
        input.setProcess("GO");
        input.setEquipment("[{\"invalid}");

        // It shouldn't throw, but should return an empty list (or throw because visits are empty)
        IllegalArgumentException exception = org.junit.jupiter.api.Assertions.assertThrows(
                IllegalArgumentException.class,
                () -> service.getEquipmentAdditionalData(input)
        );

        assertEquals("No visits provided in input document.", exception.getMessage());
    }

}

