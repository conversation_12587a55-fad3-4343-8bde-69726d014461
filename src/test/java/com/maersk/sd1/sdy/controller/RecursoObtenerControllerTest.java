package com.maersk.sd1.sdy.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.maersk.sd1.sdy.controller.dto.RecursoObtenerInput;
import com.maersk.sd1.sdy.controller.dto.RecursoObtenerOutput;
import com.maersk.sd1.sdy.service.RecursoObtenerService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@WebMvcTest(RecursoObtenerController.class)
class RecursoObtenerControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private RecursoObtenerService recursoObtenerService;

    @Autowired
    private ObjectMapper objectMapper;

    private RecursoObtenerInput.Root validRequest;
    private RecursoObtenerOutput successOutput;

    @BeforeEach
    void setUp() {
        // Setup valid request
        validRequest = new RecursoObtenerInput.Root();
        RecursoObtenerInput.Prefix prefix = new RecursoObtenerInput.Prefix();
        RecursoObtenerInput.Input input = new RecursoObtenerInput.Input();
        input.setRecursoId(42);
        prefix.setInput(input);
        validRequest.setPrefix(prefix);

        // Setup success output
        successOutput = new RecursoObtenerOutput();
        successOutput.setRecursoId(42);
        successOutput.setCatRecursoId(42893);
        successOutput.setCatCargaContenedorId(42889);
        successOutput.setCodigo("OUT");
        successOutput.setNombre("Gate Out");
        successOutput.setMarca("KALMAR");
        successOutput.setMaximoNivelApilamiento(7);
        successOutput.setActivo('1');
        successOutput.setFechaRegistro("2022-05-28T03:51:48.587");
        successOutput.setFechaModificacion(null);
        successOutput.setPuntoTrabajoId(13);
        successOutput.setRespEstado(1);
        successOutput.setRespMensaje("Resource found successfully");
    }

    @Test
    void testSdyrecursoObtener_Success() throws Exception {
        // Arrange
        when(recursoObtenerService.obtenerRecurso(42)).thenReturn(successOutput);

        // Act & Assert
        mockMvc.perform(post("/ModuleSDY/module/sdy/SDYRecursoServiceImp/sdyrecursoObtener")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(validRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.isCorrect").value(true))
                .andExpect(jsonPath("$.result").isArray())
                .andExpect(jsonPath("$.result[0]").isArray())
                .andExpect(jsonPath("$.result[0][0]").value(42))
                .andExpect(jsonPath("$.result[0][11]").value(1))
                .andExpect(jsonPath("$.result[0][12]").value("Resource found successfully"));
    }

    @Test
    void testSdyrecursoObtener_ResourceNotFound() throws Exception {
        // Arrange
        RecursoObtenerOutput notFoundOutput = new RecursoObtenerOutput();
        notFoundOutput.setRespEstado(0);
        notFoundOutput.setRespMensaje("Resource not found with ID: 999");

        when(recursoObtenerService.obtenerRecurso(999)).thenReturn(notFoundOutput);

        RecursoObtenerInput.Root notFoundRequest = new RecursoObtenerInput.Root();
        RecursoObtenerInput.Prefix prefix = new RecursoObtenerInput.Prefix();
        RecursoObtenerInput.Input input = new RecursoObtenerInput.Input();
        input.setRecursoId(999);
        prefix.setInput(input);
        notFoundRequest.setPrefix(prefix);

        // Act & Assert
        mockMvc.perform(post("/ModuleSDY/module/sdy/SDYRecursoServiceImp/sdyrecursoObtener")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(notFoundRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.isCorrect").value(true))
                .andExpect(jsonPath("$.result[0][11]").value(0))
                .andExpect(jsonPath("$.result[0][12]").value("Resource not found with ID: 999"));
    }

    @Test
    void testSdyrecursoObtener_ServiceException() throws Exception {
        // Arrange
        when(recursoObtenerService.obtenerRecurso(any())).thenThrow(new RuntimeException("Database connection error"));

        // Act & Assert
        mockMvc.perform(post("/ModuleSDY/module/sdy/SDYRecursoServiceImp/sdyrecursoObtener")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(validRequest)))
                .andExpect(status().isInternalServerError())
                .andExpect(jsonPath("$.isCorrect").value(true));
    }

    @Test
    void testSdyrecursoObtener_InvalidRequest_MissingRecursoId() throws Exception {
        // Arrange
        RecursoObtenerInput.Root invalidRequest = new RecursoObtenerInput.Root();
        RecursoObtenerInput.Prefix prefix = new RecursoObtenerInput.Prefix();
        RecursoObtenerInput.Input input = new RecursoObtenerInput.Input();
        // recursoId is null (missing)
        prefix.setInput(input);
        invalidRequest.setPrefix(prefix);

        // Act & Assert
        mockMvc.perform(post("/ModuleSDY/module/sdy/SDYRecursoServiceImp/sdyrecursoObtener")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(invalidRequest)))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.isCorrect").value(true));
    }
}
