#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (malloc) failed to allocate 2917936 bytes. Error detail: Chunk::new
# Possible reasons:
#   The system is out of physical RAM or swap space
#   This process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Zero Based Compressed Oops mode in which the Java heap is
#     placed in the first 32GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 32GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (arena.cpp:149), pid=9504, tid=6524
#
# JRE version: Java(TM) SE Runtime Environment (23.0.1+11) (build 23.0.1+11-39)
# Java VM: Java HotSpot(TM) 64-Bit Server VM (23.0.1+11-39, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: -agentlib:jdwp=transport=dt_socket,address=127.0.0.1:56348,suspend=y,server=n -javaagent:C:\Users\<USER>\AppData\Local\JetBrains\IdeaIC2025.1\captureAgent\debugger-agent.jar=file:///C:/Users/<USER>/AppData/Local/Temp/capture7061347************.props -Dkotlinx.coroutines.debug.enable.creation.stack.trace=false -Ddebugger.agent.enable.coroutines=true -Dkotlinx.coroutines.debug.enable.flows.stack.trace=true -Dkotlinx.coroutines.debug.enable.mutable.state.flows.stack.trace=true -Dfile.encoding=UTF-8 -Dsun.stdout.encoding=UTF-8 -Dsun.stderr.encoding=UTF-8 com.maersk.sd1.BusinessApplication

Host: 11th Gen Intel(R) Core(TM) i5-1145G7 @ 2.60GHz, 8 cores, 15G,  Windows 11 , 64 bit Build 26100 (10.0.26100.3912)
Time: Mon Jun  9 14:26:35 2025 India Standard Time elapsed time: 47.270399 seconds (0d 0h 0m 47s)

---------------  T H R E A D  ---------------

Current thread (0x000002086fff1010):  JavaThread "C2 CompilerThread1" daemon [_thread_in_native, id=6524, stack(0x0000004683d00000,0x0000004683e00000) (1024K)]


Current CompileTask:
C2:47270 13430       4       org.antlr.v4.runtime.atn.ParserATNSimulator::closureCheckingStopState (244 bytes)

Stack: [0x0000004683d00000,0x0000004683e00000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x6e08b9]  (no source info available)
V  [jvm.dll+0x87dc83]  (no source info available)
V  [jvm.dll+0x88010e]  (no source info available)
V  [jvm.dll+0x8807e3]  (no source info available)
V  [jvm.dll+0x27aee6]  (no source info available)
V  [jvm.dll+0xbc06f]  (no source info available)
V  [jvm.dll+0xbc2cb]  (no source info available)
V  [jvm.dll+0x3b3a62]  (no source info available)
V  [jvm.dll+0x1dc19c]  (no source info available)
V  [jvm.dll+0x244be5]  (no source info available)
V  [jvm.dll+0x243dff]  (no source info available)
V  [jvm.dll+0x1c33a0]  (no source info available)
V  [jvm.dll+0x253cf1]  (no source info available)
V  [jvm.dll+0x251faa]  (no source info available)
V  [jvm.dll+0x3ee036]  (no source info available)
V  [jvm.dll+0x824f8b]  (no source info available)
V  [jvm.dll+0x6df065]  (no source info available)
C  [ucrtbase.dll+0x37b0]  (no source info available)
C  [KERNEL32.DLL+0x2e8d7]  (no source info available)
C  [ntdll.dll+0x9c5dc]  (no source info available)

Lock stack of current Java thread (top to bottom):


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x0000020870a2d240, length=23, elements={
0x00000208480e7f40, 0x0000020867c125c0, 0x0000020867c13310, 0x0000020867c20220,
0x0000020867c23330, 0x0000020867c26950, 0x0000020867c29d60, 0x0000020867c2aea0,
0x0000020845011170, 0x0000020845223cf0, 0x00000208452ad3a0, 0x00000208452a0ba0,
0x000002084535c160, 0x000002084532eb20, 0x0000020845330710, 0x0000020870b687c0,
0x0000020870b6af20, 0x0000020870b6b5b0, 0x00000208450c7e50, 0x00000208450c5d80,
0x00000208450c6aa0, 0x000002086fff1010, 0x000002086b180ba0
}

Java Threads: ( => current thread )
  0x00000208480e7f40 JavaThread "main"                              [_thread_in_native, id=35692, stack(0x0000004681900000,0x0000004681a00000) (1024K)]
  0x0000020867c125c0 JavaThread "Reference Handler"          daemon [_thread_blocked, id=32028, stack(0x0000004682100000,0x0000004682200000) (1024K)]
  0x0000020867c13310 JavaThread "Finalizer"                  daemon [_thread_blocked, id=19076, stack(0x0000004682200000,0x0000004682300000) (1024K)]
  0x0000020867c20220 JavaThread "Signal Dispatcher"          daemon [_thread_blocked, id=25852, stack(0x0000004682300000,0x0000004682400000) (1024K)]
  0x0000020867c23330 JavaThread "Attach Listener"            daemon [_thread_blocked, id=33784, stack(0x0000004682400000,0x0000004682500000) (1024K)]
  0x0000020867c26950 JavaThread "Service Thread"             daemon [_thread_blocked, id=10964, stack(0x0000004682500000,0x0000004682600000) (1024K)]
  0x0000020867c29d60 JavaThread "Monitor Deflation Thread"   daemon [_thread_blocked, id=21080, stack(0x0000004682600000,0x0000004682700000) (1024K)]
  0x0000020867c2aea0 JavaThread "C2 CompilerThread0"         daemon [_thread_in_native, id=5500, stack(0x0000004682700000,0x0000004682800000) (1024K)]
  0x0000020845011170 JavaThread "C1 CompilerThread0"         daemon [_thread_in_native, id=6116, stack(0x0000004682800000,0x0000004682900000) (1024K)]
  0x0000020845223cf0 JavaThread "Common-Cleaner"             daemon [_thread_blocked, id=13916, stack(0x0000004682900000,0x0000004682a00000) (1024K)]
  0x00000208452ad3a0 JavaThread "JDWP Transport Listener: dt_socket" daemon [_thread_blocked, id=18764, stack(0x0000004682a00000,0x0000004682b00000) (1024K)]
  0x00000208452a0ba0 JavaThread "JDWP Event Helper Thread"   daemon [_thread_blocked, id=19768, stack(0x0000004682b00000,0x0000004682c00000) (1024K)]
  0x000002084535c160 JavaThread "JDWP Command Reader"        daemon [_thread_in_native, id=29404, stack(0x0000004682c00000,0x0000004682d00000) (1024K)]
  0x000002084532eb20 JavaThread "IntelliJ Suspend Helper"    daemon [_thread_blocked, id=20532, stack(0x0000004682d00000,0x0000004682e00000) (1024K)]
  0x0000020845330710 JavaThread "Notification Thread"        daemon [_thread_blocked, id=27508, stack(0x0000004682e00000,0x0000004682f00000) (1024K)]
  0x0000020870b687c0 JavaThread "Catalina-utility-1"                [_thread_blocked, id=30796, stack(0x0000004683900000,0x0000004683a00000) (1024K)]
  0x0000020870b6af20 JavaThread "Catalina-utility-2"                [_thread_blocked, id=23400, stack(0x0000004683a00000,0x0000004683b00000) (1024K)]
  0x0000020870b6b5b0 JavaThread "container-0"                       [_thread_blocked, id=29364, stack(0x0000004683b00000,0x0000004683c00000) (1024K)]
  0x00000208450c7e50 JavaThread "mssql-jdbc-shared-timer-core-0" daemon [_thread_blocked, id=31328, stack(0x0000004683700000,0x0000004683800000) (1024K)]
  0x00000208450c5d80 JavaThread "HikariPool-1 housekeeper"   daemon [_thread_blocked, id=12500, stack(0x0000004683c00000,0x0000004683d00000) (1024K)]
  0x00000208450c6aa0 JavaThread "HikariPool-1 connection adder" daemon [_thread_blocked, id=25536, stack(0x0000004683e00000,0x0000004683f00000) (1024K)]
=>0x000002086fff1010 JavaThread "C2 CompilerThread1"         daemon [_thread_in_native, id=6524, stack(0x0000004683d00000,0x0000004683e00000) (1024K)]
  0x000002086b180ba0 JavaThread "C2 CompilerThread2"         daemon [_thread_in_native, id=29956, stack(0x0000004683f00000,0x0000004684000000) (1024K)]
Total: 23

Other Threads:
  0x0000020867bf6500 VMThread "VM Thread"                           [id=33152, stack(0x0000004682000000,0x0000004682100000) (1024K)]
  0x0000020867be23d0 WatcherThread "VM Periodic Task Thread"        [id=30920, stack(0x0000004681f00000,0x0000004682000000) (1024K)]
  0x00000208480fd970 WorkerThread "GC Thread#0"                     [id=30016, stack(0x0000004681a00000,0x0000004681b00000) (1024K)]
  0x00000208686e0b50 WorkerThread "GC Thread#1"                     [id=10820, stack(0x0000004683000000,0x0000004683100000) (1024K)]
  0x00000208686e0f00 WorkerThread "GC Thread#2"                     [id=6540, stack(0x0000004683100000,0x0000004683200000) (1024K)]
  0x00000208686eb700 WorkerThread "GC Thread#3"                     [id=27844, stack(0x0000004683200000,0x0000004683300000) (1024K)]
  0x00000208686ec2c0 WorkerThread "GC Thread#4"                     [id=29896, stack(0x0000004683300000,0x0000004683400000) (1024K)]
  0x00000208686ee090 WorkerThread "GC Thread#5"                     [id=31508, stack(0x0000004683400000,0x0000004683500000) (1024K)]
  0x00000208686ed930 WorkerThread "GC Thread#6"                     [id=29284, stack(0x0000004683500000,0x0000004683600000) (1024K)]
  0x00000208686edce0 WorkerThread "GC Thread#7"                     [id=34976, stack(0x0000004683600000,0x0000004683700000) (1024K)]
  0x0000020848164790 ConcurrentGCThread "G1 Main Marker"            [id=20672, stack(0x0000004681b00000,0x0000004681c00000) (1024K)]
  0x00000208481652a0 WorkerThread "G1 Conc#0"                       [id=29280, stack(0x0000004681c00000,0x0000004681d00000) (1024K)]
  0x0000020868f4c8c0 WorkerThread "G1 Conc#1"                       [id=31880, stack(0x0000004683800000,0x0000004683900000) (1024K)]
  0x0000020867ab09a0 ConcurrentGCThread "G1 Refine#0"               [id=32940, stack(0x0000004681d00000,0x0000004681e00000) (1024K)]
  0x0000020867ab2440 ConcurrentGCThread "G1 Service"                [id=7556, stack(0x0000004681e00000,0x0000004681f00000) (1024K)]
Total: 15

Threads with active compile tasks:
C2 CompilerThread0  47348 13650       4       org.antlr.v4.runtime.misc.Array2DHashSet::getOrAddImpl (146 bytes)
C1 CompilerThread0  47348 13665       3       org.antlr.v4.runtime.ParserRuleContext::getChild (93 bytes)
C2 CompilerThread1  47349 13430       4       org.antlr.v4.runtime.atn.ParserATNSimulator::closureCheckingStopState (244 bytes)
C2 CompilerThread2  47349 13642       4       org.antlr.v4.runtime.atn.ParserATNSimulator::closure_ (311 bytes)
Total: 4

VM state: not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread: None

Heap address: 0x0000000704e00000, size: 4018 MB, Compressed Oops mode: Zero based, Oop shift amount: 3

CDS archive(s) mapped at: [0x0000020800000000-0x0000020800d70000-0x0000020800d70000), size 14090240, SharedBaseAddress: 0x0000020800000000, ArchiveRelocationMode: 1.
Compressed class space mapped at: 0x0000020801000000-0x0000020841000000, reserved size: 1073741824
Narrow klass base: 0x0000020800000000, Narrow klass shift: 0, Narrow klass range: 0x41000000

GC Precious Log:
 CardTable entry size: 512
 Card Set container configuration: InlinePtr #cards 4 size 8 Array Of Cards #cards 16 size 48 Howl #buckets 8 coarsen threshold 3686 Howl Bitmap #cards 512 size 80 coarsen threshold 460 Card regions per heap region 1 cards per card region 4096
 CPUs: 8 total, 8 available
 Memory: 16064M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (Zero based)
 Heap Region Size: 2M
 Heap Min Capacity: 8M
 Heap Initial Capacity: 252M
 Heap Max Capacity: 4018M
 Pre-touch: Disabled
 Parallel Workers: 8
 Concurrent Workers: 2
 Concurrent Refinement Workers: 8
 Periodic GC: Disabled

Heap:
 garbage-first heap   total reserved 4114432K, committed 227328K, used 145700K [0x0000000704e00000, 0x0000000800000000)
  region size 2048K, 19 young (38912K), 5 survivors (10240K)
 Metaspace       used 105039K, committed 105728K, reserved 1179648K
  class space    used 13259K, committed 13568K, reserved 1048576K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, TAMS=top-at-mark-start, PB=parsable bottom
|   0|0x0000000704e00000, 0x0000000705000000, 0x0000000705000000|100%| O|  |TAMS 0x0000000704e00000| PB 0x0000000704e00000| Untracked |  0
|   1|0x0000000705000000, 0x0000000705200000, 0x0000000705200000|100%| O|  |TAMS 0x0000000705000000| PB 0x0000000705000000| Untracked |  0
|   2|0x0000000705200000, 0x0000000705400000, 0x0000000705400000|100%| O|  |TAMS 0x0000000705200000| PB 0x0000000705200000| Untracked |  0
|   3|0x0000000705400000, 0x0000000705600000, 0x0000000705600000|100%| O|  |TAMS 0x0000000705400000| PB 0x0000000705400000| Untracked |  0
|   4|0x0000000705600000, 0x0000000705800000, 0x0000000705800000|100%| O|  |TAMS 0x0000000705600000| PB 0x0000000705600000| Untracked |  0
|   5|0x0000000705800000, 0x0000000705a00000, 0x0000000705a00000|100%| O|  |TAMS 0x0000000705800000| PB 0x0000000705800000| Untracked |  0
|   6|0x0000000705a00000, 0x0000000705c00000, 0x0000000705c00000|100%| O|  |TAMS 0x0000000705a00000| PB 0x0000000705a00000| Untracked |  0
|   7|0x0000000705c00000, 0x0000000705e00000, 0x0000000705e00000|100%| O|  |TAMS 0x0000000705c00000| PB 0x0000000705c00000| Untracked |  0
|   8|0x0000000705e00000, 0x0000000706000000, 0x0000000706000000|100%| O|  |TAMS 0x0000000705e00000| PB 0x0000000705e00000| Untracked |  0
|   9|0x0000000706000000, 0x0000000706200000, 0x0000000706200000|100%| O|  |TAMS 0x0000000706000000| PB 0x0000000706000000| Untracked |  0
|  10|0x0000000706200000, 0x0000000706400000, 0x0000000706400000|100%| O|  |TAMS 0x0000000706200000| PB 0x0000000706200000| Untracked |  0
|  11|0x0000000706400000, 0x0000000706600000, 0x0000000706600000|100%| O|  |TAMS 0x0000000706400000| PB 0x0000000706400000| Untracked |  0
|  12|0x0000000706600000, 0x0000000706800000, 0x0000000706800000|100%| O|  |TAMS 0x0000000706600000| PB 0x0000000706600000| Untracked |  0
|  13|0x0000000706800000, 0x0000000706a00000, 0x0000000706a00000|100%| O|  |TAMS 0x0000000706800000| PB 0x0000000706800000| Untracked |  0
|  14|0x0000000706a00000, 0x0000000706c00000, 0x0000000706c00000|100%| O|  |TAMS 0x0000000706a00000| PB 0x0000000706a00000| Untracked |  0
|  15|0x0000000706c00000, 0x0000000706e00000, 0x0000000706e00000|100%| O|  |TAMS 0x0000000706c00000| PB 0x0000000706c00000| Untracked |  0
|  16|0x0000000706e00000, 0x0000000707000000, 0x0000000707000000|100%| O|  |TAMS 0x0000000706e00000| PB 0x0000000706e00000| Untracked |  0
|  17|0x0000000707000000, 0x0000000707200000, 0x0000000707200000|100%| O|  |TAMS 0x0000000707000000| PB 0x0000000707000000| Untracked |  0
|  18|0x0000000707200000, 0x0000000707400000, 0x0000000707400000|100%| O|  |TAMS 0x0000000707200000| PB 0x0000000707200000| Untracked |  0
|  19|0x0000000707400000, 0x0000000707600000, 0x0000000707600000|100%| O|  |TAMS 0x0000000707400000| PB 0x0000000707400000| Untracked |  0
|  20|0x0000000707600000, 0x0000000707800000, 0x0000000707800000|100%| O|  |TAMS 0x0000000707600000| PB 0x0000000707600000| Untracked |  0
|  21|0x0000000707800000, 0x0000000707a00000, 0x0000000707a00000|100%| O|  |TAMS 0x0000000707800000| PB 0x0000000707800000| Untracked |  0
|  22|0x0000000707a00000, 0x0000000707c00000, 0x0000000707c00000|100%| O|  |TAMS 0x0000000707a00000| PB 0x0000000707a00000| Untracked |  0
|  23|0x0000000707c00000, 0x0000000707e00000, 0x0000000707e00000|100%| O|  |TAMS 0x0000000707c00000| PB 0x0000000707c00000| Untracked |  0
|  24|0x0000000707e00000, 0x0000000708000000, 0x0000000708000000|100%| O|  |TAMS 0x0000000707e00000| PB 0x0000000707e00000| Untracked |  0
|  25|0x0000000708000000, 0x0000000708200000, 0x0000000708200000|100%| O|  |TAMS 0x0000000708000000| PB 0x0000000708000000| Untracked |  0
|  26|0x0000000708200000, 0x0000000708400000, 0x0000000708400000|100%| O|  |TAMS 0x0000000708200000| PB 0x0000000708200000| Untracked |  0
|  27|0x0000000708400000, 0x0000000708600000, 0x0000000708600000|100%| O|  |TAMS 0x0000000708400000| PB 0x0000000708400000| Untracked |  0
|  28|0x0000000708600000, 0x0000000708800000, 0x0000000708800000|100%| O|  |TAMS 0x0000000708600000| PB 0x0000000708600000| Untracked |  0
|  29|0x0000000708800000, 0x0000000708a00000, 0x0000000708a00000|100%| O|  |TAMS 0x0000000708800000| PB 0x0000000708800000| Untracked |  0
|  30|0x0000000708a00000, 0x0000000708c00000, 0x0000000708c00000|100%| O|  |TAMS 0x0000000708a00000| PB 0x0000000708a00000| Untracked |  0
|  31|0x0000000708c00000, 0x0000000708e00000, 0x0000000708e00000|100%| O|  |TAMS 0x0000000708c00000| PB 0x0000000708c00000| Untracked |  0
|  32|0x0000000708e00000, 0x0000000709000000, 0x0000000709000000|100%| O|  |TAMS 0x0000000708e00000| PB 0x0000000708e00000| Untracked |  0
|  33|0x0000000709000000, 0x0000000709200000, 0x0000000709200000|100%| O|  |TAMS 0x0000000709000000| PB 0x0000000709000000| Untracked |  0
|  34|0x0000000709200000, 0x0000000709400000, 0x0000000709400000|100%| O|  |TAMS 0x0000000709200000| PB 0x0000000709200000| Untracked |  0
|  35|0x0000000709400000, 0x0000000709600000, 0x0000000709600000|100%| O|  |TAMS 0x0000000709400000| PB 0x0000000709400000| Untracked |  0
|  36|0x0000000709600000, 0x0000000709800000, 0x0000000709800000|100%| O|  |TAMS 0x0000000709600000| PB 0x0000000709600000| Untracked |  0
|  37|0x0000000709800000, 0x0000000709a00000, 0x0000000709a00000|100%| O|  |TAMS 0x0000000709800000| PB 0x0000000709800000| Untracked |  0
|  38|0x0000000709a00000, 0x0000000709c00000, 0x0000000709c00000|100%| O|  |TAMS 0x0000000709a00000| PB 0x0000000709a00000| Untracked |  0
|  39|0x0000000709c00000, 0x0000000709e00000, 0x0000000709e00000|100%| O|  |TAMS 0x0000000709c00000| PB 0x0000000709c00000| Untracked |  0
|  40|0x0000000709e00000, 0x000000070a000000, 0x000000070a000000|100%| O|  |TAMS 0x0000000709e00000| PB 0x0000000709e00000| Untracked |  0
|  41|0x000000070a000000, 0x000000070a200000, 0x000000070a200000|100%| O|  |TAMS 0x000000070a000000| PB 0x000000070a000000| Untracked |  0
|  42|0x000000070a200000, 0x000000070a400000, 0x000000070a400000|100%| O|  |TAMS 0x000000070a200000| PB 0x000000070a200000| Untracked |  0
|  43|0x000000070a400000, 0x000000070a600000, 0x000000070a600000|100%| O|  |TAMS 0x000000070a400000| PB 0x000000070a400000| Untracked |  0
|  44|0x000000070a600000, 0x000000070a800000, 0x000000070a800000|100%| O|  |TAMS 0x000000070a600000| PB 0x000000070a600000| Untracked |  0
|  45|0x000000070a800000, 0x000000070aa00000, 0x000000070aa00000|100%| O|  |TAMS 0x000000070a800000| PB 0x000000070a800000| Untracked |  0
|  46|0x000000070aa00000, 0x000000070ac00000, 0x000000070ac00000|100%| O|  |TAMS 0x000000070aa00000| PB 0x000000070aa00000| Untracked |  0
|  47|0x000000070ac00000, 0x000000070ae00000, 0x000000070ae00000|100%| O|  |TAMS 0x000000070ac00000| PB 0x000000070ac00000| Untracked |  0
|  48|0x000000070ae00000, 0x000000070b000000, 0x000000070b000000|100%| O|  |TAMS 0x000000070ae00000| PB 0x000000070ae00000| Untracked |  0
|  49|0x000000070b000000, 0x000000070b200000, 0x000000070b200000|100%| O|  |TAMS 0x000000070b000000| PB 0x000000070b000000| Untracked |  0
|  50|0x000000070b200000, 0x000000070b200000, 0x000000070b400000|  0%| F|  |TAMS 0x000000070b200000| PB 0x000000070b200000| Untracked |  0
|  51|0x000000070b400000, 0x000000070b400000, 0x000000070b600000|  0%| F|  |TAMS 0x000000070b400000| PB 0x000000070b400000| Untracked |  0
|  52|0x000000070b600000, 0x000000070b600000, 0x000000070b800000|  0%| F|  |TAMS 0x000000070b600000| PB 0x000000070b600000| Untracked |  0
|  53|0x000000070b800000, 0x000000070ba00000, 0x000000070ba00000|100%| O|  |TAMS 0x000000070b800000| PB 0x000000070b800000| Untracked |  0
|  54|0x000000070ba00000, 0x000000070bc00000, 0x000000070bc00000|100%| O|  |TAMS 0x000000070ba00000| PB 0x000000070ba00000| Untracked |  0
|  55|0x000000070bc00000, 0x000000070be00000, 0x000000070be00000|100%| O|  |TAMS 0x000000070bc00000| PB 0x000000070bc00000| Untracked |  0
|  56|0x000000070be00000, 0x000000070be00000, 0x000000070c000000|  0%| F|  |TAMS 0x000000070be00000| PB 0x000000070be00000| Untracked |  0
|  57|0x000000070c000000, 0x000000070c000000, 0x000000070c200000|  0%| F|  |TAMS 0x000000070c000000| PB 0x000000070c000000| Untracked |  0
|  58|0x000000070c200000, 0x000000070c200000, 0x000000070c400000|  0%| F|  |TAMS 0x000000070c200000| PB 0x000000070c200000| Untracked |  0
|  59|0x000000070c400000, 0x000000070c400000, 0x000000070c600000|  0%| F|  |TAMS 0x000000070c400000| PB 0x000000070c400000| Untracked |  0
|  60|0x000000070c600000, 0x000000070c800000, 0x000000070c800000|100%| O|  |TAMS 0x000000070c600000| PB 0x000000070c600000| Untracked |  0
|  61|0x000000070c800000, 0x000000070c800000, 0x000000070ca00000|  0%| F|  |TAMS 0x000000070c800000| PB 0x000000070c800000| Untracked |  0
|  62|0x000000070ca00000, 0x000000070ca00000, 0x000000070cc00000|  0%| F|  |TAMS 0x000000070ca00000| PB 0x000000070ca00000| Untracked |  0
|  63|0x000000070cc00000, 0x000000070cc492d0, 0x000000070ce00000| 14%| S|CS|TAMS 0x000000070cc00000| PB 0x000000070cc00000| Complete |  0
|  64|0x000000070ce00000, 0x000000070d000000, 0x000000070d000000|100%| S|CS|TAMS 0x000000070ce00000| PB 0x000000070ce00000| Complete |  0
|  65|0x000000070d000000, 0x000000070d200000, 0x000000070d200000|100%| S|CS|TAMS 0x000000070d000000| PB 0x000000070d000000| Complete |  0
|  66|0x000000070d200000, 0x000000070d400000, 0x000000070d400000|100%| S|CS|TAMS 0x000000070d200000| PB 0x000000070d200000| Complete |  0
|  67|0x000000070d400000, 0x000000070d600000, 0x000000070d600000|100%| S|CS|TAMS 0x000000070d400000| PB 0x000000070d400000| Complete |  0
|  68|0x000000070d600000, 0x000000070d600000, 0x000000070d800000|  0%| F|  |TAMS 0x000000070d600000| PB 0x000000070d600000| Untracked |  0
|  69|0x000000070d800000, 0x000000070d800000, 0x000000070da00000|  0%| F|  |TAMS 0x000000070d800000| PB 0x000000070d800000| Untracked |  0
|  70|0x000000070da00000, 0x000000070da00000, 0x000000070dc00000|  0%| F|  |TAMS 0x000000070da00000| PB 0x000000070da00000| Untracked |  0
|  71|0x000000070dc00000, 0x000000070dc00000, 0x000000070de00000|  0%| F|  |TAMS 0x000000070dc00000| PB 0x000000070dc00000| Untracked |  0
|  72|0x000000070de00000, 0x000000070de00000, 0x000000070e000000|  0%| F|  |TAMS 0x000000070de00000| PB 0x000000070de00000| Untracked |  0
|  73|0x000000070e000000, 0x000000070e000000, 0x000000070e200000|  0%| F|  |TAMS 0x000000070e000000| PB 0x000000070e000000| Untracked |  0
|  74|0x000000070e200000, 0x000000070e200000, 0x000000070e400000|  0%| F|  |TAMS 0x000000070e200000| PB 0x000000070e200000| Untracked |  0
|  75|0x000000070e400000, 0x000000070e400000, 0x000000070e600000|  0%| F|  |TAMS 0x000000070e400000| PB 0x000000070e400000| Untracked |  0
|  76|0x000000070e600000, 0x000000070e600000, 0x000000070e800000|  0%| F|  |TAMS 0x000000070e600000| PB 0x000000070e600000| Untracked |  0
|  77|0x000000070e800000, 0x000000070e800000, 0x000000070ea00000|  0%| F|  |TAMS 0x000000070e800000| PB 0x000000070e800000| Untracked |  0
|  78|0x000000070ea00000, 0x000000070ea00000, 0x000000070ec00000|  0%| F|  |TAMS 0x000000070ea00000| PB 0x000000070ea00000| Untracked |  0
|  79|0x000000070ec00000, 0x000000070ec00000, 0x000000070ee00000|  0%| F|  |TAMS 0x000000070ec00000| PB 0x000000070ec00000| Untracked |  0
|  80|0x000000070ee00000, 0x000000070ee00000, 0x000000070f000000|  0%| F|  |TAMS 0x000000070ee00000| PB 0x000000070ee00000| Untracked |  0
|  81|0x000000070f000000, 0x000000070f000000, 0x000000070f200000|  0%| F|  |TAMS 0x000000070f000000| PB 0x000000070f000000| Untracked |  0
|  82|0x000000070f200000, 0x000000070f200000, 0x000000070f400000|  0%| F|  |TAMS 0x000000070f200000| PB 0x000000070f200000| Untracked |  0
|  83|0x000000070f400000, 0x000000070f400000, 0x000000070f600000|  0%| F|  |TAMS 0x000000070f400000| PB 0x000000070f400000| Untracked |  0
|  84|0x000000070f600000, 0x000000070f600000, 0x000000070f800000|  0%| F|  |TAMS 0x000000070f600000| PB 0x000000070f600000| Untracked |  0
|  85|0x000000070f800000, 0x000000070f800000, 0x000000070fa00000|  0%| F|  |TAMS 0x000000070f800000| PB 0x000000070f800000| Untracked |  0
|  86|0x000000070fa00000, 0x000000070fa00000, 0x000000070fc00000|  0%| F|  |TAMS 0x000000070fa00000| PB 0x000000070fa00000| Untracked |  0
|  87|0x000000070fc00000, 0x000000070fc00000, 0x000000070fe00000|  0%| F|  |TAMS 0x000000070fc00000| PB 0x000000070fc00000| Untracked |  0
|  88|0x000000070fe00000, 0x000000070fe00000, 0x0000000710000000|  0%| F|  |TAMS 0x000000070fe00000| PB 0x000000070fe00000| Untracked |  0
|  89|0x0000000710000000, 0x0000000710000000, 0x0000000710200000|  0%| F|  |TAMS 0x0000000710000000| PB 0x0000000710000000| Untracked |  0
|  90|0x0000000710200000, 0x0000000710200000, 0x0000000710400000|  0%| F|  |TAMS 0x0000000710200000| PB 0x0000000710200000| Untracked |  0
|  91|0x0000000710400000, 0x0000000710400000, 0x0000000710600000|  0%| F|  |TAMS 0x0000000710400000| PB 0x0000000710400000| Untracked |  0
|  92|0x0000000710600000, 0x0000000710600000, 0x0000000710800000|  0%| F|  |TAMS 0x0000000710600000| PB 0x0000000710600000| Untracked |  0
|  93|0x0000000710800000, 0x0000000710800000, 0x0000000710a00000|  0%| F|  |TAMS 0x0000000710800000| PB 0x0000000710800000| Untracked |  0
|  94|0x0000000710a00000, 0x0000000710a00000, 0x0000000710c00000|  0%| F|  |TAMS 0x0000000710a00000| PB 0x0000000710a00000| Untracked |  0
|  95|0x0000000710c00000, 0x0000000710c00000, 0x0000000710e00000|  0%| F|  |TAMS 0x0000000710c00000| PB 0x0000000710c00000| Untracked |  0
|  96|0x0000000710e00000, 0x0000000710e00000, 0x0000000711000000|  0%| F|  |TAMS 0x0000000710e00000| PB 0x0000000710e00000| Untracked |  0
|  97|0x0000000711000000, 0x0000000711200000, 0x0000000711200000|100%| E|  |TAMS 0x0000000711000000| PB 0x0000000711000000| Complete |  0
|  98|0x0000000711200000, 0x0000000711400000, 0x0000000711400000|100%| E|CS|TAMS 0x0000000711200000| PB 0x0000000711200000| Complete |  0
|  99|0x0000000711400000, 0x0000000711600000, 0x0000000711600000|100%| E|CS|TAMS 0x0000000711400000| PB 0x0000000711400000| Complete |  0
| 100|0x0000000711600000, 0x0000000711800000, 0x0000000711800000|100%| E|CS|TAMS 0x0000000711600000| PB 0x0000000711600000| Complete |  0
| 101|0x0000000711800000, 0x0000000711a00000, 0x0000000711a00000|100%| E|CS|TAMS 0x0000000711800000| PB 0x0000000711800000| Complete |  0
| 102|0x0000000711a00000, 0x0000000711c00000, 0x0000000711c00000|100%| E|CS|TAMS 0x0000000711a00000| PB 0x0000000711a00000| Complete |  0
| 103|0x0000000711c00000, 0x0000000711e00000, 0x0000000711e00000|100%| E|CS|TAMS 0x0000000711c00000| PB 0x0000000711c00000| Complete |  0
| 104|0x0000000711e00000, 0x0000000712000000, 0x0000000712000000|100%| E|CS|TAMS 0x0000000711e00000| PB 0x0000000711e00000| Complete |  0
| 105|0x0000000712000000, 0x0000000712200000, 0x0000000712200000|100%| E|CS|TAMS 0x0000000712000000| PB 0x0000000712000000| Complete |  0
| 106|0x0000000712200000, 0x0000000712400000, 0x0000000712400000|100%| E|CS|TAMS 0x0000000712200000| PB 0x0000000712200000| Complete |  0
| 107|0x0000000712400000, 0x0000000712600000, 0x0000000712600000|100%| E|CS|TAMS 0x0000000712400000| PB 0x0000000712400000| Complete |  0
| 108|0x0000000712600000, 0x0000000712800000, 0x0000000712800000|100%| E|CS|TAMS 0x0000000712600000| PB 0x0000000712600000| Complete |  0
| 124|0x0000000714600000, 0x0000000714800000, 0x0000000714800000|100%| E|CS|TAMS 0x0000000714600000| PB 0x0000000714600000| Complete |  0
| 125|0x0000000714800000, 0x0000000714a00000, 0x0000000714a00000|100%| E|CS|TAMS 0x0000000714800000| PB 0x0000000714800000| Complete |  0

Card table byte_map: [0x0000020860c90000,0x0000020861470000] _byte_map_base: 0x000002085d469000

Marking Bits: (CMBitMap*) 0x000002084814f870
 Bits: [0x0000020861470000, 0x0000020865338000)

Polling page: 0x0000020846710000

Metaspace:

Usage:
  Non-class:     89.63 MB used.
      Class:     12.95 MB used.
       Both:    102.58 MB used.

Virtual space:
  Non-class space:      128.00 MB reserved,      90.00 MB ( 70%) committed,  2 nodes.
      Class space:        1.00 GB reserved,      13.25 MB (  1%) committed,  1 nodes.
             Both:        1.12 GB reserved,     103.25 MB (  9%) committed. 

Chunk freelists:
   Non-Class:  5.19 MB
       Class:  2.78 MB
        Both:  7.97 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 148.25 MB
CDS: on
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - use_allocation_guard: 0.


Internal statistics:

num_allocs_failed_limit: 12.
num_arena_births: 1052.
num_arena_deaths: 0.
num_vsnodes_births: 3.
num_vsnodes_deaths: 0.
num_space_committed: 1652.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 12.
num_chunks_taken_from_freelist: 4515.
num_chunk_merges: 12.
num_chunk_splits: 3349.
num_chunks_enlarged: 2710.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=120064Kb used=7429Kb max_used=7429Kb free=112634Kb
 bounds [0x0000020858510000, 0x0000020858c60000, 0x000002085fa50000]
CodeHeap 'profiled nmethods': size=120000Kb used=17088Kb max_used=17088Kb free=102911Kb
 bounds [0x0000020850a50000, 0x0000020851b10000, 0x0000020857f80000]
CodeHeap 'non-nmethods': size=5696Kb used=2059Kb max_used=2157Kb free=3636Kb
 bounds [0x0000020857f80000, 0x00000208581f0000, 0x0000020858510000]
CodeCache: size=245760Kb, used=26576Kb, max_used=26674Kb, free=219181Kb
 total_blobs=11901, nmethods=11088, adapters=717, full_count=0
Compilation: enabled, stopped_count=0, restarted_count=0

Compilation events (20 events):
Event: 45.846 Thread 0x0000020845011170 13586   !   2       org.antlr.v4.runtime.atn.ParserATNSimulator::adaptivePredict (274 bytes)
Event: 45.847 Thread 0x0000020845011170 nmethod 13586 0x0000020851adb008 code [0x0000020851adb2c0, 0x0000020851adc5d0]
Event: 45.848 Thread 0x0000020845011170 13587       2       org.antlr.v4.runtime.Parser::exitRule (81 bytes)
Event: 45.848 Thread 0x0000020845011170 nmethod 13587 0x0000020851adc708 code [0x0000020851adc860, 0x0000020851adcbf0]
Event: 45.848 Thread 0x0000020845011170 13588       2       org.antlr.v4.runtime.BufferedTokenStream::seek (14 bytes)
Event: 45.848 Thread 0x0000020845011170 nmethod 13588 0x0000020851adcc88 code [0x0000020851adcdc0, 0x0000020851adcf88]
Event: 45.854 Thread 0x0000020845011170 13590 %     3       org.antlr.v4.runtime.atn.ParserATNSimulator::computeReachSet @ 144 (455 bytes)
Event: 45.858 Thread 0x0000020845011170 nmethod 13590% 0x0000020851add008 code [0x0000020851add4a0, 0x0000020851ae0d80]
Event: 45.858 Thread 0x0000020845011170 13592       3       org.antlr.v4.runtime.misc.Array2DHashSet::expand (247 bytes)
Event: 45.859 Thread 0x0000020845011170 nmethod 13592 0x0000020851ae0e88 code [0x0000020851ae1080, 0x0000020851ae1e58]
Event: 45.859 Thread 0x0000020845011170 13591       2       org.antlr.v4.runtime.atn.PredictionMode::hasConflictingAltSet (41 bytes)
Event: 45.859 Thread 0x0000020845011170 nmethod 13591 0x0000020851ae1e88 code [0x0000020851ae1fe0, 0x0000020851ae2270]
Event: 45.873 Thread 0x0000020845011170 13594       3       org.antlr.v4.runtime.BufferedTokenStream::sync (55 bytes)
Event: 45.874 Thread 0x0000020845011170 nmethod 13594 0x0000020851ae2308 code [0x0000020851ae2440, 0x0000020851ae27b0]
Event: 45.874 Thread 0x0000020845011170 13595 %     3       org.antlr.v4.runtime.misc.FlexibleHashMap::values @ 23 (95 bytes)
Event: 45.874 Thread 0x0000020845011170 nmethod 13595% 0x0000020851ae2808 code [0x0000020851ae29c0, 0x0000020851ae34f0]
Event: 45.875 Thread 0x0000020845011170 13596       2       org.antlr.v4.runtime.BufferedTokenStream::previousTokenOnChannel (69 bytes)
Event: 45.875 Thread 0x0000020845011170 nmethod 13596 0x0000020851ae3588 code [0x0000020851ae3700, 0x0000020851ae3ab0]
Event: 45.885 Thread 0x0000020845011170 13597       2       org.antlr.v4.runtime.Parser::getCurrentToken (11 bytes)
Event: 45.885 Thread 0x0000020845011170 nmethod 13597 0x0000020851ae3b88 code [0x0000020851ae3ca0, 0x0000020851ae3de8]

GC Heap History (20 events):
Event: 39.333 GC heap before
{Heap before GC invocations=25 (full 0):
 garbage-first heap   total reserved 4114432K, committed 188416K, used 164349K [0x0000000704e00000, 0x0000000800000000)
  region size 2048K, 45 young (92160K), 3 survivors (6144K)
 Metaspace       used 78460K, committed 79104K, reserved 1179648K
  class space    used 9479K, committed 9792K, reserved 1048576K
}
Event: 39.343 GC heap after
{Heap after GC invocations=26 (full 0):
 garbage-first heap   total reserved 4114432K, committed 188416K, used 87873K [0x0000000704e00000, 0x0000000800000000)
  region size 2048K, 6 young (12288K), 6 survivors (12288K)
 Metaspace       used 78460K, committed 79104K, reserved 1179648K
  class space    used 9479K, committed 9792K, reserved 1048576K
}
Event: 41.419 GC heap before
{Heap before GC invocations=26 (full 0):
 garbage-first heap   total reserved 4114432K, committed 188416K, used 163649K [0x0000000704e00000, 0x0000000800000000)
  region size 2048K, 43 young (88064K), 6 survivors (12288K)
 Metaspace       used 84223K, committed 84864K, reserved 1179648K
  class space    used 10320K, committed 10624K, reserved 1048576K
}
Event: 41.432 GC heap after
{Heap after GC invocations=27 (full 0):
 garbage-first heap   total reserved 4114432K, committed 188416K, used 95959K [0x0000000704e00000, 0x0000000800000000)
  region size 2048K, 5 young (10240K), 5 survivors (10240K)
 Metaspace       used 84223K, committed 84864K, reserved 1179648K
  class space    used 10320K, committed 10624K, reserved 1048576K
}
Event: 42.077 GC heap before
{Heap before GC invocations=27 (full 0):
 garbage-first heap   total reserved 4114432K, committed 188416K, used 165591K [0x0000000704e00000, 0x0000000800000000)
  region size 2048K, 39 young (79872K), 5 survivors (10240K)
 Metaspace       used 88678K, committed 89344K, reserved 1179648K
  class space    used 10975K, committed 11264K, reserved 1048576K
}
Event: 42.083 GC heap after
{Heap after GC invocations=28 (full 0):
 garbage-first heap   total reserved 4114432K, committed 188416K, used 98388K [0x0000000704e00000, 0x0000000800000000)
  region size 2048K, 2 young (4096K), 2 survivors (4096K)
 Metaspace       used 88678K, committed 89344K, reserved 1179648K
  class space    used 10975K, committed 11264K, reserved 1048576K
}
Event: 42.430 GC heap before
{Heap before GC invocations=29 (full 0):
 garbage-first heap   total reserved 4114432K, committed 188416K, used 170068K [0x0000000704e00000, 0x0000000800000000)
  region size 2048K, 37 young (75776K), 2 survivors (4096K)
 Metaspace       used 89127K, committed 89792K, reserved 1179648K
  class space    used 11043K, committed 11328K, reserved 1048576K
}
Event: 42.432 GC heap after
{Heap after GC invocations=30 (full 0):
 garbage-first heap   total reserved 4114432K, committed 188416K, used 98733K [0x0000000704e00000, 0x0000000800000000)
  region size 2048K, 2 young (4096K), 2 survivors (4096K)
 Metaspace       used 89127K, committed 89792K, reserved 1179648K
  class space    used 11043K, committed 11328K, reserved 1048576K
}
Event: 42.628 GC heap before
{Heap before GC invocations=30 (full 0):
 garbage-first heap   total reserved 4114432K, committed 188416K, used 170413K [0x0000000704e00000, 0x0000000800000000)
  region size 2048K, 37 young (75776K), 2 survivors (4096K)
 Metaspace       used 89639K, committed 90304K, reserved 1179648K
  class space    used 11091K, committed 11392K, reserved 1048576K
}
Event: 42.634 GC heap after
{Heap after GC invocations=31 (full 0):
 garbage-first heap   total reserved 4114432K, committed 188416K, used 98911K [0x0000000704e00000, 0x0000000800000000)
  region size 2048K, 2 young (4096K), 2 survivors (4096K)
 Metaspace       used 89639K, committed 90304K, reserved 1179648K
  class space    used 11091K, committed 11392K, reserved 1048576K
}
Event: 42.878 GC heap before
{Heap before GC invocations=31 (full 0):
 garbage-first heap   total reserved 4114432K, committed 188416K, used 170591K [0x0000000704e00000, 0x0000000800000000)
  region size 2048K, 37 young (75776K), 2 survivors (4096K)
 Metaspace       used 90289K, committed 90944K, reserved 1179648K
  class space    used 11158K, committed 11456K, reserved 1048576K
}
Event: 42.881 GC heap after
{Heap after GC invocations=32 (full 0):
 garbage-first heap   total reserved 4114432K, committed 188416K, used 100054K [0x0000000704e00000, 0x0000000800000000)
  region size 2048K, 3 young (6144K), 3 survivors (6144K)
 Metaspace       used 90289K, committed 90944K, reserved 1179648K
  class space    used 11158K, committed 11456K, reserved 1048576K
}
Event: 43.147 GC heap before
{Heap before GC invocations=33 (full 0):
 garbage-first heap   total reserved 4114432K, committed 188416K, used 167638K [0x0000000704e00000, 0x0000000800000000)
  region size 2048K, 36 young (73728K), 3 survivors (6144K)
 Metaspace       used 90846K, committed 91520K, reserved 1179648K
  class space    used 11216K, committed 11520K, reserved 1048576K
}
Event: 43.150 GC heap after
{Heap after GC invocations=34 (full 0):
 garbage-first heap   total reserved 4114432K, committed 188416K, used 100874K [0x0000000704e00000, 0x0000000800000000)
  region size 2048K, 3 young (6144K), 3 survivors (6144K)
 Metaspace       used 90846K, committed 91520K, reserved 1179648K
  class space    used 11216K, committed 11520K, reserved 1048576K
}
Event: 43.401 GC heap before
{Heap before GC invocations=34 (full 0):
 garbage-first heap   total reserved 4114432K, committed 188416K, used 168458K [0x0000000704e00000, 0x0000000800000000)
  region size 2048K, 36 young (73728K), 3 survivors (6144K)
 Metaspace       used 91476K, committed 92160K, reserved 1179648K
  class space    used 11286K, committed 11584K, reserved 1048576K
}
Event: 43.407 GC heap after
{Heap after GC invocations=35 (full 0):
 garbage-first heap   total reserved 4114432K, committed 227328K, used 100466K [0x0000000704e00000, 0x0000000800000000)
  region size 2048K, 3 young (6144K), 3 survivors (6144K)
 Metaspace       used 91476K, committed 92160K, reserved 1179648K
  class space    used 11286K, committed 11584K, reserved 1048576K
}
Event: 44.323 GC heap before
{Heap before GC invocations=35 (full 0):
 garbage-first heap   total reserved 4114432K, committed 227328K, used 204914K [0x0000000704e00000, 0x0000000800000000)
  region size 2048K, 54 young (110592K), 3 survivors (6144K)
 Metaspace       used 96242K, committed 96960K, reserved 1179648K
  class space    used 11916K, committed 12224K, reserved 1048576K
}
Event: 44.330 GC heap after
{Heap after GC invocations=36 (full 0):
 garbage-first heap   total reserved 4114432K, committed 227328K, used 109843K [0x0000000704e00000, 0x0000000800000000)
  region size 2048K, 7 young (14336K), 7 survivors (14336K)
 Metaspace       used 96242K, committed 96960K, reserved 1179648K
  class space    used 11916K, committed 12224K, reserved 1048576K
}
Event: 45.800 GC heap before
{Heap before GC invocations=36 (full 0):
 garbage-first heap   total reserved 4114432K, committed 227328K, used 197907K [0x0000000704e00000, 0x0000000800000000)
  region size 2048K, 50 young (102400K), 7 survivors (14336K)
 Metaspace       used 103177K, committed 103872K, reserved 1179648K
  class space    used 13004K, committed 13312K, reserved 1048576K
}
Event: 45.812 GC heap after
{Heap after GC invocations=37 (full 0):
 garbage-first heap   total reserved 4114432K, committed 227328K, used 119076K [0x0000000704e00000, 0x0000000800000000)
  region size 2048K, 5 young (10240K), 5 survivors (10240K)
 Metaspace       used 103177K, committed 103872K, reserved 1179648K
  class space    used 13004K, committed 13312K, reserved 1048576K
}

Dll operation events (13 events):
Event: 0.036 Loaded shared library C:\Program Files\Java\jdk-23\bin\java.dll
Event: 0.040 Loaded shared library C:\Program Files\Java\jdk-23\bin\zip.dll
Event: 0.112 Loaded shared library C:\Program Files\Java\jdk-23\bin\jsvml.dll
Event: 0.382 Loaded shared library C:\Program Files\Java\jdk-23\bin\instrument.dll
Event: 0.457 Loaded shared library C:\Program Files\Java\jdk-23\bin\net.dll
Event: 0.460 Loaded shared library C:\Program Files\Java\jdk-23\bin\nio.dll
Event: 0.475 Loaded shared library C:\Program Files\Java\jdk-23\bin\zip.dll
Event: 0.645 Loaded shared library C:\Program Files\Java\jdk-23\bin\jimage.dll
Event: 1.916 Loaded shared library C:\Program Files\Java\jdk-23\bin\verify.dll
Event: 2.302 Loaded shared library C:\Program Files\Java\jdk-23\bin\sunmscapi.dll
Event: 10.424 Loaded shared library C:\Program Files\Java\jdk-23\bin\management.dll
Event: 10.434 Loaded shared library C:\Program Files\Java\jdk-23\bin\management_ext.dll
Event: 27.807 Loaded shared library C:\Program Files\Java\jdk-23\bin\extnet.dll

Deoptimization events (20 events):
Event: 45.695 Thread 0x00000208480e7f40 DEOPT PACKING pc=0x0000020858c3b4b4 sp=0x00000046819f9870
Event: 45.695 Thread 0x00000208480e7f40 DEOPT UNPACKING pc=0x0000020857fd7982 sp=0x00000046819f9850 mode 2
Event: 45.695 Thread 0x00000208480e7f40 Uncommon trap: trap_request=0xffffffd6 fr.pc=0x0000020858c3b4b4 relative=0x0000000000001114
Event: 45.695 Thread 0x00000208480e7f40 Uncommon trap: reason=array_check action=maybe_recompile pc=0x0000020858c3b4b4 method=org.antlr.v4.runtime.misc.Array2DHashSet.getOrAddImpl(Ljava/lang/Object;)Ljava/lang/Object; @ 36 c2
Event: 45.695 Thread 0x00000208480e7f40 DEOPT PACKING pc=0x0000020858c3b4b4 sp=0x00000046819f9b70
Event: 45.695 Thread 0x00000208480e7f40 DEOPT UNPACKING pc=0x0000020857fd7982 sp=0x00000046819f9b50 mode 2
Event: 45.695 Thread 0x00000208480e7f40 Uncommon trap: trap_request=0xffffffbe fr.pc=0x0000020858c3b264 relative=0x0000000000000ec4
Event: 45.695 Thread 0x00000208480e7f40 Uncommon trap: reason=profile_predicate action=maybe_recompile pc=0x0000020858c3b264 method=org.antlr.v4.runtime.misc.Array2DHashSet.getOrAddImpl(Ljava/lang/Object;)Ljava/lang/Object; @ 56 c2
Event: 45.695 Thread 0x00000208480e7f40 DEOPT PACKING pc=0x0000020858c3b264 sp=0x00000046819f9670
Event: 45.695 Thread 0x00000208480e7f40 DEOPT UNPACKING pc=0x0000020857fd7982 sp=0x00000046819f9658 mode 2
Event: 45.695 Thread 0x00000208480e7f40 Uncommon trap: trap_request=0xffffffd6 fr.pc=0x0000020858c3b4b4 relative=0x0000000000001114
Event: 45.695 Thread 0x00000208480e7f40 Uncommon trap: reason=array_check action=maybe_recompile pc=0x0000020858c3b4b4 method=org.antlr.v4.runtime.misc.Array2DHashSet.getOrAddImpl(Ljava/lang/Object;)Ljava/lang/Object; @ 36 c2
Event: 45.695 Thread 0x00000208480e7f40 DEOPT PACKING pc=0x0000020858c3b4b4 sp=0x00000046819f9670
Event: 45.695 Thread 0x00000208480e7f40 DEOPT UNPACKING pc=0x0000020857fd7982 sp=0x00000046819f9650 mode 2
Event: 45.695 Thread 0x00000208480e7f40 Uncommon trap: trap_request=0xffffffd6 fr.pc=0x0000020858c3b4b4 relative=0x0000000000001114
Event: 45.695 Thread 0x00000208480e7f40 Uncommon trap: reason=array_check action=maybe_recompile pc=0x0000020858c3b4b4 method=org.antlr.v4.runtime.misc.Array2DHashSet.getOrAddImpl(Ljava/lang/Object;)Ljava/lang/Object; @ 36 c2
Event: 45.695 Thread 0x00000208480e7f40 DEOPT PACKING pc=0x0000020858c3b4b4 sp=0x00000046819f9570
Event: 45.695 Thread 0x00000208480e7f40 DEOPT UNPACKING pc=0x0000020857fd7982 sp=0x00000046819f9550 mode 2
Event: 45.855 Thread 0x00000208480e7f40 DEOPT PACKING pc=0x0000020851727c1f sp=0x00000046819f7750
Event: 45.855 Thread 0x00000208480e7f40 DEOPT UNPACKING pc=0x0000020857fd84c2 sp=0x00000046819f6c00 mode 0

Classes loaded (20 events):
Event: 46.241 Loading class org/hibernate/query/spi/SqmQuery
Event: 46.241 Loading class org/hibernate/query/spi/SqmQuery done
Event: 46.243 Loading class org/hibernate/query/sqm/internal/SqmInterpretationsKey$InterpretationsKeySource
Event: 46.243 Loading class org/hibernate/query/sqm/internal/SqmInterpretationsKey$InterpretationsKeySource done
Event: 46.244 Loading class org/hibernate/query/sqm/internal/SqmInterpretationsKey$CacheabilityInfluencers
Event: 46.244 Loading class org/hibernate/query/sqm/internal/SqmInterpretationsKey$CacheabilityInfluencers done
Event: 46.244 Loading class org/hibernate/query/spi/DomainQueryExecutionContext
Event: 46.244 Loading class org/hibernate/query/spi/DomainQueryExecutionContext done
Event: 46.245 Loading class org/hibernate/query/spi/AbstractSelectionQuery
Event: 46.245 Loading class org/hibernate/query/spi/AbstractSelectionQuery done
Event: 46.247 Loading class org/hibernate/query/spi/AbstractCommonQueryContract
Event: 46.247 Loading class org/hibernate/query/spi/AbstractCommonQueryContract done
Event: 46.251 Loading class org/hibernate/query/spi/MutableQueryOptions
Event: 46.251 Loading class org/hibernate/query/spi/MutableQueryOptions done
Event: 46.253 Loading class org/hibernate/ScrollableResults
Event: 46.253 Loading class org/hibernate/ScrollableResults done
Event: 46.254 Loading class org/hibernate/NonUniqueResultException
Event: 46.254 Loading class org/hibernate/NonUniqueResultException done
Event: 46.257 Loading class org/hibernate/query/ResultListTransformer
Event: 46.257 Loading class org/hibernate/query/ResultListTransformer done

Classes unloaded (0 events):
No events

Classes redefined (1 events):
Event: 0.412 Thread 0x0000020867bf6500 redefined class name=java.lang.Throwable, count=1

Internal exceptions (20 events):
Event: 43.802 Thread 0x00000208480e7f40 Exception <a 'java/lang/NoSuchMethodError'{0x000000070e743aa8}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeInterface(java.lang.Object, java.lang.Object, int, java.lang.Object)'> (0x000000070e743aa8) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 796]
Event: 43.805 Thread 0x00000208480e7f40 Exception <a 'java/lang/NoSuchMethodError'{0x000000070e76a868}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.newInvokeSpecial(java.lang.Object, java.lang.Object, int, java.lang.Object)'> (0x000000070e76a868) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 796]
Event: 43.805 Thread 0x00000208480e7f40 Exception <a 'java/lang/NoSuchMethodError'{0x000000070e76e210}: 'java.lang.Object java.lang.invoke.Invokers$Holder.linkToTargetMethod(java.lang.Object, int, java.lang.Object, java.lang.Object)'> (0x000000070e76e210) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 796]
Event: 43.818 Thread 0x00000208480e7f40 Exception <a 'java/lang/IncompatibleClassChangeError'{0x000000070e41f300}: Found class java.lang.Object, but interface was expected> (0x000000070e41f300) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 863]
Event: 43.840 Thread 0x00000208480e7f40 Exception <a 'java/lang/NoSuchMethodError'{0x000000070e5c8c60}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, int, java.lang.Object)'> (0x000000070e5c8c60) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 796]
Event: 43.843 Thread 0x00000208480e7f40 Exception <a 'java/lang/NoSuchMethodError'{0x000000070e5ee8a8}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.newInvokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, int, java.lang.Object)'> (0x000000070e5ee8a8) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 796]
Event: 43.860 Thread 0x00000208450c6aa0 Implicit null exception at 0x0000020850b82178 to 0x0000020850b82ddc
Event: 43.860 Thread 0x00000208450c6aa0 Exception <a 'java/lang/NullPointerException'{0x000000070e3178b0}> (0x000000070e3178b0) 
thrown [s\open\src\hotspot\share\runtime\sharedRuntime.cpp, line 627]
Event: 43.888 Thread 0x00000208480e7f40 Exception <a 'java/lang/NoSuchMethodError'{0x000000070e0a7bc8}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.newInvokeSpecial(java.lang.Object, java.lang.Object, int, java.lang.Object, int, java.lang.Object, java.lang.Object)'> (0x000000070e0a7bc8) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 796]
Event: 43.892 Thread 0x00000208480e7f40 Exception <a 'java/lang/NoSuchMethodError'{0x000000070e0eac70}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.newInvokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, int)'> (0x000000070e0eac70) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 796]
Event: 43.892 Thread 0x00000208480e7f40 Exception <a 'java/lang/NoSuchMethodError'{0x000000070e0ef238}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, int)'> (0x000000070e0ef238) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 796]
Event: 43.892 Thread 0x00000208480e7f40 Exception <a 'java/lang/NoSuchMethodError'{0x000000070e0f3288}: 'java.lang.Object java.lang.invoke.Invokers$Holder.linkToTargetMethod(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, int, java.lang.Object)'> (0x000000070e0f3288) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 796]
Event: 44.096 Thread 0x00000208480e7f40 Exception <a 'java/lang/NoSuchMethodError'{0x000000070cf344a0}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeInterface(java.lang.Object, java.lang.Object)'> (0x000000070cf344a0) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 796]
Event: 44.295 Thread 0x00000208480e7f40 Exception <a 'java/lang/NoSuchMethodError'{0x000000070c719010}: 'void java.lang.invoke.Invokers$Holder.invokeExact_MT(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x000000070c719010) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 796]
Event: 44.690 Thread 0x00000208480e7f40 Exception <a 'java/lang/IncompatibleClassChangeError'{0x000000071160c268}: Found class java.lang.Object, but interface was expected> (0x000000071160c268) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 863]
Event: 44.707 Thread 0x00000208480e7f40 Implicit null exception at 0x0000020858a04924 to 0x0000020858a08000
Event: 44.717 Thread 0x00000208480e7f40 Exception <a 'java/lang/IncompatibleClassChangeError'{0x00000007114cc170}: Found class java.lang.Object, but interface was expected> (0x00000007114cc170) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 863]
Event: 44.778 Thread 0x00000208480e7f40 Exception <a 'java/lang/IncompatibleClassChangeError'{0x0000000711346408}: Found class java.lang.Object, but interface was expected> (0x0000000711346408) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 863]
Event: 44.961 Thread 0x00000208480e7f40 Implicit null exception at 0x0000020858b3bf31 to 0x0000020858b3bfb9
Event: 45.094 Thread 0x00000208480e7f40 Exception <a 'java/lang/IncompatibleClassChangeError'{0x000000071053e7a8}: Found class java.lang.Object, but interface was expected> (0x000000071053e7a8) 
thrown [s\open\src\hotspot\share\interpreter\linkResolver.cpp, line 863]

VM Operations (20 events):
Event: 44.323 Executing VM operation: G1CollectForAllocation (G1 Evacuation Pause)
Event: 44.331 Executing VM operation: G1CollectForAllocation (G1 Evacuation Pause) done
Event: 44.655 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 44.655 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 44.967 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 44.967 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 45.119 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 45.119 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 45.122 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 45.122 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 45.256 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 45.256 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 45.469 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 45.469 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 45.800 Executing VM operation: G1CollectForAllocation (G1 Evacuation Pause)
Event: 45.812 Executing VM operation: G1CollectForAllocation (G1 Evacuation Pause) done
Event: 45.946 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 45.946 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 46.007 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 46.007 Executing VM operation: HandshakeAllThreads (Deoptimize) done

Memory protections (0 events):
No events

Nmethod flushes (20 events):
Event: 42.979 Thread 0x0000020867bf6500 flushing  nmethod 0x00000208516e7388
Event: 42.979 Thread 0x0000020867bf6500 flushing  nmethod 0x0000020851758008
Event: 42.979 Thread 0x0000020867bf6500 flushing  nmethod 0x000002085175c088
Event: 42.979 Thread 0x0000020867bf6500 flushing  nmethod 0x000002085175e288
Event: 42.979 Thread 0x0000020867bf6500 flushing  nmethod 0x00000208517c0e08
Event: 42.979 Thread 0x0000020867bf6500 flushing  nmethod 0x00000208517c4588
Event: 42.979 Thread 0x0000020867bf6500 flushing  nmethod 0x00000208517c6d08
Event: 42.979 Thread 0x0000020867bf6500 flushing  nmethod 0x00000208517cb988
Event: 42.979 Thread 0x0000020867bf6500 flushing  nmethod 0x000002085185bb08
Event: 42.979 Thread 0x0000020867bf6500 flushing  nmethod 0x0000020851860408
Event: 42.979 Thread 0x0000020867bf6500 flushing  nmethod 0x0000020851863608
Event: 42.979 Thread 0x0000020867bf6500 flushing  nmethod 0x00000208518ea388
Event: 42.979 Thread 0x0000020867bf6500 flushing  nmethod 0x00000208518eb008
Event: 42.979 Thread 0x0000020867bf6500 flushing  nmethod 0x00000208518eba08
Event: 42.979 Thread 0x0000020867bf6500 flushing  nmethod 0x00000208518ebf08
Event: 42.979 Thread 0x0000020867bf6500 flushing  nmethod 0x00000208518ec208
Event: 42.979 Thread 0x0000020867bf6500 flushing  nmethod 0x00000208518ec908
Event: 42.979 Thread 0x0000020867bf6500 flushing  nmethod 0x00000208518ecc08
Event: 42.979 Thread 0x0000020867bf6500 flushing  nmethod 0x0000020851923d08
Event: 42.979 Thread 0x0000020867bf6500 flushing  nmethod 0x0000020851927e88

Events (20 events):
Event: 32.256 Thread 0x000002086f958340 Thread exited: 0x000002086f958340
Event: 33.068 Thread 0x0000020867c2aea0 Thread added: 0x000002086f9575a0
Event: 33.589 Thread 0x000002086f9575a0 Thread exited: 0x000002086f9575a0
Event: 33.621 Thread 0x00000208480e7f40 Thread added: 0x00000208450c7e50
Event: 33.893 Thread 0x00000208480e7f40 Thread added: 0x00000208450c5d80
Event: 33.920 Thread 0x0000020867c2aea0 Thread added: 0x000002086f95b2f0
Event: 34.008 Thread 0x00000208450c5d80 Thread added: 0x00000208450c6aa0
Event: 35.131 Thread 0x000002086f95b2f0 Thread exited: 0x000002086f95b2f0
Event: 35.466 Thread 0x0000020845011170 Thread added: 0x000002086ffe7360
Event: 35.735 Thread 0x000002086ffe7360 Thread exited: 0x000002086ffe7360
Event: 35.977 Thread 0x0000020845011170 Thread added: 0x000002086ffe7360
Event: 35.979 Thread 0x0000020845011170 Thread added: 0x000002086ffecbf0
Event: 36.432 Thread 0x000002086ffecbf0 Thread exited: 0x000002086ffecbf0
Event: 36.432 Thread 0x000002086ffe7360 Thread exited: 0x000002086ffe7360
Event: 38.612 Thread 0x0000020845011170 Thread added: 0x000002086ffed990
Event: 39.755 Thread 0x000002086ffed990 Thread exited: 0x000002086ffed990
Event: 40.735 Thread 0x0000020845011170 Thread added: 0x000002086fff1010
Event: 40.979 Thread 0x0000020845011170 Thread added: 0x0000020869428a80
Event: 41.378 Thread 0x0000020869428a80 Thread exited: 0x0000020869428a80
Event: 42.015 Thread 0x0000020845011170 Thread added: 0x000002086b180ba0


Dynamic libraries:
0x00007ff7190a0000 - 0x00007ff7190b0000 	C:\Program Files\Java\jdk-23\bin\java.exe
0x00007fff6d8e0000 - 0x00007fff6db46000 	C:\WINDOWS\SYSTEM32\ntdll.dll
0x00007fff6c110000 - 0x00007fff6c1d9000 	C:\WINDOWS\System32\KERNEL32.DLL
0x00007fff6af40000 - 0x00007fff6b30c000 	C:\WINDOWS\System32\KERNELBASE.dll
0x00007fff6b490000 - 0x00007fff6b5db000 	C:\WINDOWS\System32\ucrtbase.dll
0x00007fff3d190000 - 0x00007fff3d1a7000 	C:\Program Files\Java\jdk-23\bin\jli.dll
0x00007fff6d0b0000 - 0x00007fff6d27a000 	C:\WINDOWS\System32\USER32.dll
0x00007fff3a090000 - 0x00007fff3a0ab000 	C:\Program Files\Java\jdk-23\bin\VCRUNTIME140.dll
0x00007fff6aca0000 - 0x00007fff6acc7000 	C:\WINDOWS\System32\win32u.dll
0x00007fff6cb80000 - 0x00007fff6cbab000 	C:\WINDOWS\System32\GDI32.dll
0x00007fff6ad70000 - 0x00007fff6aea2000 	C:\WINDOWS\System32\gdi32full.dll
0x00007fff475d0000 - 0x00007fff4786a000 	C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.26100.3912_none_3e07963ce335137e\COMCTL32.dll
0x00007fff6aa70000 - 0x00007fff6ab13000 	C:\WINDOWS\System32\msvcp_win.dll
0x00007fff6ba90000 - 0x00007fff6bb39000 	C:\WINDOWS\System32\msvcrt.dll
0x00007fff6bc30000 - 0x00007fff6bc60000 	C:\WINDOWS\System32\IMM32.DLL
0x0000020846730000 - 0x0000020846746000 	C:\WINDOWS\System32\umppc19607.dll
0x0000020847f30000 - 0x0000020847f43000 	C:\WINDOWS\System32\CsXumd64_19607.dll
0x00007fff40210000 - 0x00007fff4021c000 	C:\Program Files\Java\jdk-23\bin\vcruntime140_1.dll
0x00007fff33780000 - 0x00007fff3380e000 	C:\Program Files\Java\jdk-23\bin\msvcp140.dll
0x00007ffeb1ad0000 - 0x00007ffeb281f000 	C:\Program Files\Java\jdk-23\bin\server\jvm.dll
0x00007fff6b850000 - 0x00007fff6b902000 	C:\WINDOWS\System32\ADVAPI32.dll
0x00007fff6ccd0000 - 0x00007fff6cd76000 	C:\WINDOWS\System32\sechost.dll
0x00007fff6cbb0000 - 0x00007fff6ccc6000 	C:\WINDOWS\System32\RPCRT4.dll
0x00007fff6d030000 - 0x00007fff6d0a4000 	C:\WINDOWS\System32\WS2_32.dll
0x00007fff6a8c0000 - 0x00007fff6a91e000 	C:\WINDOWS\SYSTEM32\POWRPROF.dll
0x00007fff62440000 - 0x00007fff62476000 	C:\WINDOWS\SYSTEM32\WINMM.dll
0x00007fff592c0000 - 0x00007fff592cb000 	C:\WINDOWS\SYSTEM32\VERSION.dll
0x00007fff6a8a0000 - 0x00007fff6a8b4000 	C:\WINDOWS\SYSTEM32\UMPDC.dll
0x00007fff696c0000 - 0x00007fff696da000 	C:\WINDOWS\SYSTEM32\kernel.appcore.dll
0x00007fff3f400000 - 0x00007fff3f40a000 	C:\Program Files\Java\jdk-23\bin\jimage.dll
0x00007fff67cb0000 - 0x00007fff67ef1000 	C:\WINDOWS\SYSTEM32\DBGHELP.DLL
0x00007fff6d280000 - 0x00007fff6d604000 	C:\WINDOWS\System32\combase.dll
0x00007fff6bb40000 - 0x00007fff6bc20000 	C:\WINDOWS\System32\OLEAUT32.dll
0x00007fff3cf70000 - 0x00007fff3cfa9000 	C:\WINDOWS\SYSTEM32\dbgcore.DLL
0x00007fff6acd0000 - 0x00007fff6ad69000 	C:\WINDOWS\System32\bcryptPrimitives.dll
0x00007fff33710000 - 0x00007fff3374c000 	C:\Program Files\Java\jdk-23\bin\jdwp.dll
0x00007fff3f030000 - 0x00007fff3f03f000 	C:\Program Files\Java\jdk-23\bin\instrument.dll
0x00007fff39db0000 - 0x00007fff39dce000 	C:\Program Files\Java\jdk-23\bin\java.dll
0x00007fff6b6a0000 - 0x00007fff6b83f000 	C:\WINDOWS\System32\ole32.dll
0x00007fff6c2e0000 - 0x00007fff6ca0d000 	C:\WINDOWS\System32\SHELL32.dll
0x00007fff6b310000 - 0x00007fff6b484000 	C:\WINDOWS\System32\wintypes.dll
0x00007fff338d0000 - 0x00007fff338e7000 	C:\Program Files\Java\jdk-23\bin\zip.dll
0x00007fff68360000 - 0x00007fff68bb6000 	C:\WINDOWS\SYSTEM32\windows.storage.dll
0x00007fff6b910000 - 0x00007fff6b9ff000 	C:\WINDOWS\System32\SHCORE.dll
0x00007fff6ba10000 - 0x00007fff6ba79000 	C:\WINDOWS\System32\shlwapi.dll
0x00007fff6a980000 - 0x00007fff6a9af000 	C:\WINDOWS\SYSTEM32\profapi.dll
0x00007fff19390000 - 0x00007fff19467000 	C:\Program Files\Java\jdk-23\bin\jsvml.dll
0x00007fff3d9f0000 - 0x00007fff3d9fc000 	C:\Program Files\Java\jdk-23\bin\dt_socket.dll
0x00007fff68f20000 - 0x00007fff68f53000 	C:\WINDOWS\SYSTEM32\IPHLPAPI.DLL
0x00007fff69d40000 - 0x00007fff69daa000 	C:\WINDOWS\system32\mswsock.dll
0x00007fff3f260000 - 0x00007fff3f270000 	C:\Program Files\Java\jdk-23\bin\net.dll
0x00007fff39cc0000 - 0x00007fff39cd6000 	C:\Program Files\Java\jdk-23\bin\nio.dll
0x00007fff3d3e0000 - 0x00007fff3d3f0000 	C:\Program Files\Java\jdk-23\bin\verify.dll
0x00007fff3fea0000 - 0x00007fff3feae000 	C:\Program Files\Java\jdk-23\bin\sunmscapi.dll
0x00007fff6ab20000 - 0x00007fff6ac97000 	C:\WINDOWS\System32\CRYPT32.dll
0x00007fff6a250000 - 0x00007fff6a280000 	C:\WINDOWS\SYSTEM32\ncrypt.dll
0x00007fff6a200000 - 0x00007fff6a23f000 	C:\WINDOWS\SYSTEM32\NTASN1.dll
0x00007fff3fe80000 - 0x00007fff3fe8a000 	C:\Program Files\Java\jdk-23\bin\management.dll
0x00007fff3f150000 - 0x00007fff3f15b000 	C:\Program Files\Java\jdk-23\bin\management_ext.dll
0x00007fff6bc70000 - 0x00007fff6bc78000 	C:\WINDOWS\System32\PSAPI.DLL
0x00007fff6a100000 - 0x00007fff6a11c000 	C:\WINDOWS\SYSTEM32\CRYPTSP.dll
0x00007fff69620000 - 0x00007fff6965a000 	C:\WINDOWS\system32\rsaenh.dll
0x00007fff69de0000 - 0x00007fff69e0b000 	C:\WINDOWS\SYSTEM32\USERENV.dll
0x00007fff6a950000 - 0x00007fff6a976000 	C:\WINDOWS\SYSTEM32\bcrypt.dll
0x00007fff6a0e0000 - 0x00007fff6a0ec000 	C:\WINDOWS\SYSTEM32\CRYPTBASE.dll
0x00007fff6ba80000 - 0x00007fff6ba8a000 	C:\WINDOWS\System32\NSI.dll
0x00007fff3fe90000 - 0x00007fff3fe99000 	C:\Program Files\Java\jdk-23\bin\extnet.dll
0x00007fff31bd0000 - 0x00007fff31be8000 	C:\WINDOWS\system32\napinsp.dll
0x00007fff68fc0000 - 0x00007fff690e7000 	C:\WINDOWS\SYSTEM32\DNSAPI.dll
0x00007fff31bf0000 - 0x00007fff31c02000 	C:\WINDOWS\System32\winrnr.dll
0x00007fff31c40000 - 0x00007fff31c70000 	C:\WINDOWS\system32\nlansp_c.dll
0x00007fff31c80000 - 0x00007fff31ca0000 	C:\WINDOWS\system32\wshbth.dll
0x00007fff59ff0000 - 0x00007fff59ffb000 	C:\Windows\System32\rasadhlp.dll
0x00007fff63ee0000 - 0x00007fff63f66000 	C:\WINDOWS\System32\fwpuclnt.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;C:\Program Files\Java\jdk-23\bin;C:\WINDOWS\SYSTEM32;C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.26100.3912_none_3e07963ce335137e;C:\Program Files\Java\jdk-23\bin\server

VM Arguments:
jvm_args: -agentlib:jdwp=transport=dt_socket,address=127.0.0.1:56348,suspend=y,server=n -javaagent:C:\Users\<USER>\AppData\Local\JetBrains\IdeaIC2025.1\captureAgent\debugger-agent.jar=file:///C:/Users/<USER>/AppData/Local/Temp/capture7061347************.props -Dkotlinx.coroutines.debug.enable.creation.stack.trace=false -Ddebugger.agent.enable.coroutines=true -Dkotlinx.coroutines.debug.enable.flows.stack.trace=true -Dkotlinx.coroutines.debug.enable.mutable.state.flows.stack.trace=true -Dfile.encoding=UTF-8 -Dsun.stdout.encoding=UTF-8 -Dsun.stderr.encoding=UTF-8 
java_command: com.maersk.sd1.BusinessApplication
java_class_path (initial): C:\Users\<USER>\Documents\SD1 SDY\SD1-ModuleSDY\target\classes;C:\Users\<USER>\.m2\repository\io\micrometer\micrometer-registry-prometheus\1.13.6\micrometer-registry-prometheus-1.13.6.jar;C:\Users\<USER>\.m2\repository\io\micrometer\micrometer-core\1.13.6\micrometer-core-1.13.6.jar;C:\Users\<USER>\.m2\repository\io\micrometer\micrometer-commons\1.13.6\micrometer-commons-1.13.6.jar;C:\Users\<USER>\.m2\repository\org\hdrhistogram\HdrHistogram\2.2.2\HdrHistogram-2.2.2.jar;C:\Users\<USER>\.m2\repository\org\latencyutils\LatencyUtils\2.0.3\LatencyUtils-2.0.3.jar;C:\Users\<USER>\.m2\repository\io\prometheus\prometheus-metrics-core\1.2.1\prometheus-metrics-core-1.2.1.jar;C:\Users\<USER>\.m2\repository\io\prometheus\prometheus-metrics-model\1.2.1\prometheus-metrics-model-1.2.1.jar;C:\Users\<USER>\.m2\repository\io\prometheus\prometheus-metrics-config\1.2.1\prometheus-metrics-config-1.2.1.jar;C:\Users\<USER>\.m2\repository\io\prometheus\prometheus-metrics-tracer-common\1.2.1\prometheus-metrics-tracer-common-1.2.1.jar;C:\Users\<USER>\.m2\repository\io\prometheus\prometheus-metrics-exposition-formats\1.2.1\prometheus-metrics-exposition-formats-1.2.1.jar;C:\Users\<USER>\.m2\repository\io\prometheus\prometheus-metrics-shaded-protobuf\1.2.1\prometheus-metrics-shaded-protobuf-1.2.1.jar;C:\Users\<USER>\.m2\repository\io\micrometer\micrometer-tracing\1.3.5\micrometer-tracing-1.3.5.jar;C:\Users\<USER>\.m2\repository\io\micrometer\micrometer-observation\1.13.6\micrometer-observation-1.13.6.jar;C:\Users\<USER>\.m2\repository\io\micrometer\context-propagation\1.1.2\context-propagation-1.1.2.jar;C:\Users\<USER>\.m2\repository\aopalliance\aopalliance\1.0\aopalliance-1.0.jar;C:\Users\<USER>\.m2\repository\io\micrometer\micrometer-tracing-bridge-brave\1.3.5\micrometer-tracing-bridge-brave-1.3.5.jar;C:\Users\<USER>\.m2\repository\org\slf4j\slf4j-api\2.0.16\slf4j-api-2.0.16.jar;C:\Users\<USER>\.m2\repository\io\zipkin\brave\brave\6.0.3\brave-6.0.3.jar;C:\Users\<USER>\.m2\repository\io\zipkin\brave\brave-context-slf4j\6.0.3\brave-context-slf4j-6.0.3.jar;C:\Users\<USER>\.m2\repository\io\zipkin\brave\brave-instrumentation-http\6.0.3\brave-instrumentation-http-6.0.3.jar;C:\Users\<USER>\.m2\repository\io\zipkin\aws\brave-propagation-aws\1.2.5\brave-propagation-aws-1.2.5.jar;C:\Users\<USER>\.m2\repository\io\zipkin\contrib\brave-propagation-w3c\brave-propagation-tracecontext\0.2.0\brave-propagation-tracecontext-0.2.0.jar;C:\Users\<USER>\.m2\repository\io\zipkin\reporter2\zipkin-reporter-brave\3.4.2\zipkin-reporter-brave-3.4.2.jar;C:\Users\<USER>\.m2\repository\io\zipkin\reporter2\zipkin-reporter\3.4.2\zipkin-reporter-3.4.2.jar;C:\Users\<USER>\.m2\repository\commons-io\commons-io\2.11.0\commons-io-2.11.0.jar;C:\Users\<USER>\.m2\repository\commons-codec\commons-codec\1.16.1\commons-codec-1.16.1.jar;C:\Users\<USER>\.m2\repository\com\sendgrid\sendgrid-java\4.10.3\sendgrid-java-4.10.3.jar;C:\Users\<USER>\.m2\repository\com\sendgrid\java-http-client\4.5.0\java-http-client-4.5.0.jar;C:\Users\<USER>\.m2\repository\org\apache\httpcomponents\httpcore\4.4.16\httpcore-4.4.16.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-core\2.17.2\jackson-core-2.17.2.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-annotations\2.17.2\jackson-annotations-2.17.2.jar;C:\Users\<USER>\.m2\repository\com\google\guava\guava\30.1.1-jre\guava-30.1.1-jre.jar;C:\Users\<USER>\.m2\repository\com\google\guava\failureaccess\1.0.1\failureaccess-1.0.1.jar;C:\Users\<USER>\.m2\repository\com\google\guava\listenablefuture\9999.0-empty-to-avoid-conflict-with-guava\listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar;C:\Users\<USER>\.m2\repository\com\google\code\findbugs\jsr305\3.0.2\jsr305-3.0.2.jar;C:\Users\<USER>\.m2\repository\org\checkerframework\checker-qual\3.8.0\checker-qual-3.8.0.jar;C:\Users\<USER>\.m2\repository\com\google\errorprone\error_prone_annotations\2.5.1\error_prone_annotations-2.5.1.jar;C:\Users\<USER>\.m2\repository\com\google\j2objc\j2objc-annotations\1.3\j2objc-annotations-1.3.jar;C:\Users\<USER>\.m2\repository\io\springfox\springfox-boot-starter\3.0.0\springfox-boot-starter-3.0.0.jar;C:\Users\<USER>\.m2\repository\io\springfox\springfox-oas\3.0.0\springfox-oas-3.0.0.jar;C:\Users\<USER>\.m2\repository\io\swagger\core\v3\swagger-annotations\2.1.2\swagger-annotations-2.1.2.jar;C:\Users\<USER>\.m2\repository\io\swagger\core\v3\swagger-models\2.1.2\swagger-models-2.1.2.jar;C:\Users\<USER>\.m2\repository\io\springfox\springfox-core\3.0.0\springfox-core-3.0.0.jar;C:\Users\<USER>\.m2\repository\io\springfox\springfox-data-rest\3.0.0\springfox-data-rest-3.0.0.jar;C:\Users\<USER>\.m2\repository\io\springfox\springfox-bean-validators\3.0.0\springfox-bean-validators-3.0.0.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\classmate\1.7.0\classmate-1.7.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\plugin\spring-plugin-core\2.0.0.RELEASE\spring-plugin-core-2.0.0.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-beans\6.1.14\spring-beans-6.1.14.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-context\6.1.14\spring-context-6.1.14.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-aop\6.1.14\spring-aop-6.1.14.jar;C:\Users\<USER>\.m2\repository\org\springframework\plugin\spring-plugin-metadata\2.0.0.RELEASE\spring-plugin-metadata-2.0.0.RELEASE.jar;C:\Users\<USER>\.m2\repository\io\springfox\springfox-swagger2\3.0.0\springfox-swagger2-3.0.0.jar;C:\Users\<USER>\.m2\repository\io\springfox\springfox-spi\3.0.0\springfox-spi-3.0.0.jar;C:\Users\<USER>\.m2\repository\io\springfox\springfox-schema\3.0.0\springfox-schema-3.0.0.jar;C:\Users\<USER>\.m2\repository\io\springfox\springfox-swagger-common\3.0.0\springfox-swagger-common-3.0.0.jar;C:\Users\<USER>\.m2\repository\io\springfox\springfox-spring-web\3.0.0\springfox-spring-web-3.0.0.jar;C:\Users\<USER>\.m2\repository\io\github\classgraph\classgraph\4.8.83\classgraph-4.8.83.jar;C:\Users\<USER>\.m2\repository\io\springfox\springfox-spring-webmvc\3.0.0\springfox-spring-webmvc-3.0.0.jar;C:\Users\<USER>\.m2\repository\io\springfox\springfox-spring-webflux\3.0.0\springfox-spring-webflux-3.0.0.jar;C:\Users\<USER>\.m2\repository\io\swagger\swagger-annotations\1.5.20\swagger-annotations-1.5.20.jar;C:\Users\<USER>\.m2\repository\io\swagger\swagger-models\1.5.20\swagger-models-1.5.20.jar;C:\Users\<USER>\.m2\repository\io\springfox\springfox-swagger-ui\3.0.0\springfox-swagger-ui-3.0.0.jar;C:\Users\<USER>\.m2\repository\org\hibernate\orm\hibernate-core\6.3.1.Final\hibernate-core-6.3.1.Final.jar;C:\Users\<USER>\.m2\repository\jakarta\transaction\jakarta.transaction-api\2.0.1\jakarta.transaction-api-2.0.1.jar;C:\Users\<USER>\.m2\repository\org\jboss\logging\jboss-logging\3.5.3.Final\jboss-logging-3.5.3.Final.jar;C:\Users\<USER>\.m2\repository\org\hibernate\common\hibernate-commons-annotations\6.0.6.Final\hibernate-commons-annotations-6.0.6.Final.jar;C:\Users\<USER>\.m2\repository\io\smallrye\jandex\3.1.2\jandex-3.1.2.jar;C:\Users\<USER>\.m2\repository\net\bytebuddy\byte-buddy\1.14.19\byte-buddy-1.14.19.jar;C:\Users\<USER>\.m2\repository\jakarta\xml\bind\jakarta.xml.bind-api\4.0.2\jakarta.xml.bind-api-4.0.2.jar;C:\Users\<USER>\.m2\repository\org\glassfish\jaxb\jaxb-runtime\4.0.5\jaxb-runtime-4.0.5.jar;C:\Users\<USER>\.m2\repository\org\glassfish\jaxb\jaxb-core\4.0.5\jaxb-core-4.0.5.jar;C:\Users\<USER>\.m2\repository\org\glassfish\jaxb\txw2\4.0.5\txw2-4.0.5.jar;C:\Users\<USER>\.m2\repository\com\sun\istack\istack-commons-runtime\4.1.2\istack-commons-runtime-4.1.2.jar;C:\Users\<USER>\.m2\repository\jakarta\inject\jakarta.inject-api\2.0.1\jakarta.inject-api-2.0.1.jar;C:\Users\<USER>\.m2\repository\org\antlr\antlr4-runtime\4.10.1\antlr4-runtime-4.10.1.jar;C:\Users\<USER>\.m2\repository\jakarta\persistence\jakarta.persistence-api\3.1.0\jakarta.persistence-api-3.1.0.jar;C:\Users\<USER>\.m2\repository\Maersk-Global\ohrest-sd1\1.0.3\ohrest-sd1-1.0.3.jar;C:\Users\<USER>\.m2\repository\jakarta\mail\jakarta.mail-api\2.1.3\jakarta.mail-api-2.1.3.jar;C:\Users\<USER>\.m2\repository\org\eclipse\angus\jakarta.mail\2.0.3\jakarta.mail-2.0.3.jar;C:\Users\<USER>\.m2\repository\org\eclipse\angus\angus-activation\2.0.2\angus-activation-2.0.2.jar;C:\Users\<USER>\.m2\repository\jakarta\activation\jakarta.activation-api\2.1.3\jakarta.activation-api-2.1.3.jar;C:\Users\<USER>\.m2\repository\jakarta\xml\ws\jakarta.xml.ws-api\4.0.2\jakarta.xml.ws-api-4.0.2.jar;C:\Users\<USER>\.m2\repository\jakarta\xml\soap\jakarta.xml.soap-api\3.0.2\jakarta.xml.soap-api-3.0.2.jar;C:\Users\<USER>\.m2\repository\com\twelvemonkeys\imageio\imageio-core\3.9.4\imageio-core-3.9.4.jar;C:\Users\<USER>\.m2\repository\com\twelvemonkeys\common\common-lang\3.9.4\common-lang-3.9.4.jar;C:\Users\<USER>\.m2\repository\com\twelvemonkeys\common\common-io\3.9.4\common-io-3.9.4.jar;C:\Users\<USER>\.m2\repository\com\twelvemonkeys\common\common-image\3.9.4\common-image-3.9.4.jar;C:\Users\<USER>\.m2\repository\jakarta\security\enterprise\jakarta.security.enterprise-api\3.0.0\jakarta.security.enterprise-api-3.0.0.jar;C:\Users\<USER>\.m2\repository\jakarta\json\jakarta.json-api\2.1.3\jakarta.json-api-2.1.3.jar;C:\Users\<USER>\.m2\repository\org\mockito\mockito-core\5.11.0\mockito-core-5.11.0.jar;C:\Users\<USER>\.m2\repository\net\bytebuddy\byte-buddy-agent\1.14.19\byte-buddy-agent-1.14.19.jar;C:\Users\<USER>\.m2\repository\org\objenesis\objenesis\3.3\objenesis-3.3.jar;C:\Users\<USER>\.m2\repository\Maersk-Global\ohjpo-sd1\1.0.2\ohjpo-sd1-1.0.2.jar;C:\Users\<USER>\.m2\repository\junit\junit\4.13.2\junit-4.13.2.jar;C:\Users\<USER>\.m2\repository\org\hamcrest\hamcrest-core\2.2\hamcrest-core-2.2.jar;C:\Users\<USER>\.m2\repository\org\apache\logging\log4j\log4j-api\2.23.1\log4j-api-2.23.1.jar;C:\Users\<USER>\.m2\repository\org\apache\logging\log4j\log4j-core\2.23.1\log4j-core-2.23.1.jar;C:\Users\<USER>\.m2\repository\maersklogistics\ohrestconnection\1.0.9\ohrestconnection-1.0.9.jar;C:\Users\<USER>\.m2\repository\com\konghq\unirest-java\2.3.14\unirest-java-2.3.14.jar;C:\Users\<USER>\.m2\repository\maersklogistics\ohRest\1.0.1\ohrest-1.0.1.jar;C:\Users\<USER>\.m2\repository\javax\mail\javax.mail-api\1.6.2\javax.mail-api-1.6.2.jar;C:\Users\<USER>\.m2\repository\javax\activation\activation\1.1.1\activation-1.1.1.jar;C:\Users\<USER>\.m2\repository\maersklogistics\ohjpo\1.0.2\ohjpo-1.0.2.jar;C:\Users\<USER>\.m2\repository\maersklogistics\ohazure\1.2.0\ohazure-1.2.0.jar;C:\Users\<USER>\.m2\repository\com\microsoft\azure\azure-storage\8.4.0\azure-storage-8.4.0.jar;C:\Users\<USER>\.m2\repository\org\apache\commons\commons-lang3\3.14.0\commons-lang3-3.14.0.jar;C:\Users\<USER>\.m2\repository\com\microsoft\azure\azure-keyvault-core\1.0.0\azure-keyvault-core-1.0.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-actuator\3.3.5\spring-boot-starter-actuator-3.3.5.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter\3.3.5\spring-boot-starter-3.3.5.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot\3.3.5\spring-boot-3.3.5.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-autoconfigure\3.3.5\spring-boot-autoconfigure-3.3.5.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-logging\3.3.5\spring-boot-starter-logging-3.3.5.jar;C:\Users\<USER>\.m2\repository\ch\qos\logback\logback-classic\1.5.11\logback-classic-1.5.11.jar;C:\Users\<USER>\.m2\repository\ch\qos\logback\logback-core\1.5.11\logback-core-1.5.11.jar;C:\Users\<USER>\.m2\repository\org\apache\logging\log4j\log4j-to-slf4j\2.23.1\log4j-to-slf4j-2.23.1.jar;C:\Users\<USER>\.m2\repository\org\slf4j\jul-to-slf4j\2.0.16\jul-to-slf4j-2.0.16.jar;C:\Users\<USER>\.m2\repository\jakarta\annotation\jakarta.annotation-api\2.1.1\jakarta.annotation-api-2.1.1.jar;C:\Users\<USER>\.m2\repository\org\yaml\snakeyaml\2.2\snakeyaml-2.2.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-actuator-autoconfigure\3.3.5\spring-boot-actuator-autoconfigure-3.3.5.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-actuator\3.3.5\spring-boot-actuator-3.3.5.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\datatype\jackson-datatype-jsr310\2.17.2\jackson-datatype-jsr310-2.17.2.jar;C:\Users\<USER>\.m2\repository\io\micrometer\micrometer-jakarta9\1.13.6\micrometer-jakarta9-1.13.6.jar;C:\Users\<USER>\.m2\repository\org\hamcrest\hamcrest\2.2\hamcrest-2.2.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-core\6.1.14\spring-core-6.1.14.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-jcl\6.1.14\spring-jcl-6.1.14.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-web\3.3.5\spring-boot-starter-web-3.3.5.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-json\3.3.5\spring-boot-starter-json-3.3.5.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\datatype\jackson-datatype-jdk8\2.17.2\jackson-datatype-jdk8-2.17.2.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\module\jackson-module-parameter-names\2.17.2\jackson-module-parameter-names-2.17.2.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-tomcat\3.3.5\spring-boot-starter-tomcat-3.3.5.jar;C:\Users\<USER>\.m2\repository\org\apache\tomcat\embed\tomcat-embed-core\10.1.31\tomcat-embed-core-10.1.31.jar;C:\Users\<USER>\.m2\repository\org\apache\tomcat\embed\tomcat-embed-el\10.1.31\tomcat-embed-el-10.1.31.jar;C:\Users\<USER>\.m2\repository\org\apache\tomcat\embed\tomcat-embed-websocket\10.1.31\tomcat-embed-websocket-10.1.31.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-web\6.1.14\spring-web-6.1.14.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-webmvc\6.1.14\spring-webmvc-6.1.14.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-expression\6.1.14\spring-expression-6.1.14.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-data-jpa\3.3.5\spring-boot-starter-data-jpa-3.3.5.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-aop\3.3.5\spring-boot-starter-aop-3.3.5.jar;C:\Users\<USER>\.m2\repository\org\aspectj\aspectjweaver\1.9.22.1\aspectjweaver-1.9.22.1.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-jdbc\3.3.5\spring-boot-starter-jdbc-3.3.5.jar;C:\Users\<USER>\.m2\repository\com\zaxxer\HikariCP\5.1.0\HikariCP-5.1.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-jdbc\6.1.14\spring-jdbc-6.1.14.jar;C:\Users\<USER>\.m2\repository\org\springframework\data\spring-data-jpa\3.3.5\spring-data-jpa-3.3.5.jar;C:\Users\<USER>\.m2\repository\org\springframework\data\spring-data-commons\3.3.5\spring-data-commons-3.3.5.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-orm\6.1.14\spring-orm-6.1.14.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-tx\6.1.14\spring-tx-6.1.14.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-aspects\6.1.14\spring-aspects-6.1.14.jar;C:\Users\<USER>\.m2\repository\com\microsoft\sqlserver\mssql-jdbc\12.8.1.jre11\mssql-jdbc-12.8.1.jre11.jar;C:\Users\<USER>\.m2\repository\org\springdoc\springdoc-openapi-starter-webmvc-ui\2.6.0\springdoc-openapi-starter-webmvc-ui-2.6.0.jar;C:\Users\<USER>\.m2\repository\org\springdoc\springdoc-openapi-starter-webmvc-api\2.6.0\springdoc-openapi-starter-webmvc-api-2.6.0.jar;C:\Users\<USER>\.m2\repository\org\springdoc\springdoc-openapi-starter-common\2.6.0\springdoc-openapi-starter-common-2.6.0.jar;C:\Users\<USER>\.m2\repository\io\swagger\core\v3\swagger-core-jakarta\2.2.22\swagger-core-jakarta-2.2.22.jar;C:\Users\<USER>\.m2\repository\io\swagger\core\v3\swagger-annotations-jakarta\2.2.22\swagger-annotations-jakarta-2.2.22.jar;C:\Users\<USER>\.m2\repository\io\swagger\core\v3\swagger-models-jakarta\2.2.22\swagger-models-jakarta-2.2.22.jar;C:\Users\<USER>\.m2\repository\jakarta\validation\jakarta.validation-api\3.0.2\jakarta.validation-api-3.0.2.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\dataformat\jackson-dataformat-yaml\2.17.2\jackson-dataformat-yaml-2.17.2.jar;C:\Users\<USER>\.m2\repository\org\webjars\swagger-ui\5.17.14\swagger-ui-5.17.14.jar;C:\Users\<USER>\.m2\repository\org\json\json\20240303\json-20240303.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-databind\2.17.1\jackson-databind-2.17.1.jar;C:\Users\<USER>\.m2\repository\javax\validation\validation-api\2.0.1.Final\validation-api-2.0.1.Final.jar;C:\Users\<USER>\.m2\repository\javax\inject\javax.inject\1\javax.inject-1.jar;C:\Users\<USER>\.m2\repository\org\apache\httpcomponents\client5\httpclient5\5.3.1\httpclient5-5.3.1.jar;C:\Users\<USER>\.m2\repository\org\apache\httpcomponents\core5\httpcore5\5.2.5\httpcore5-5.2.5.jar;C:\Users\<USER>\.m2\repository\org\apache\httpcomponents\core5\httpcore5-h2\5.2.5\httpcore5-h2-5.2.5.jar;C:\Users\<USER>\.m2\repository\com\google\code\gson\gson\2.8.6\gson-2.8.6.jar;C:\Users\<USER>\.m2\repository\com\mashape\unirest\unirest-java\1.4.9\unirest-java-1.4.9.jar;C:\Users\<USER>\.m2\repository\org\apache\httpcomponents\httpclient\4.5.2\httpclient-4.5.2.jar;C:\Users\<USER>\.m2\repository\commons-logging\commons-logging\1.2\commons-logging-1.2.jar;C:\Users\<USER>\.m2\repository\org\apache\httpcomponents\httpasyncclient\4.1.5\httpasyncclient-4.1.5.jar;C:\Users\<USER>\.m2\repository\org\apache\httpcomponents\httpcore-nio\4.4.16\httpcore-nio-4.4.16.jar;C:\Users\<USER>\.m2\repository\org\apache\httpcomponents\httpmime\4.5.2\httpmime-4.5.2.jar;C:\Users\<USER>\.m2\repository\org\mapstruct\mapstruct\1.6.3\mapstruct-1.6.3.jar;C:\Users\<USER>\.m2\repository\org\dom4j\dom4j\2.1.3\dom4j-2.1.3.jar;C:\Users\<USER>\.m2\repository\org\modelmapper\modelmapper\2.3.5\modelmapper-2.3.5.jar;C:\Users\<USER>\.m2\repository\javax\ws\rs\javax.ws.rs-api\2.1.1\javax.ws.rs-api-2.1.1.jar;C:\Program Files\JetBrains\IntelliJ IDEA Community Edition 2024.3.1\lib\idea_rt.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 4                                         {product} {ergonomic}
     uint ConcGCThreads                            = 2                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 8                                         {product} {ergonomic}
   size_t G1HeapRegionSize                         = 2097152                                   {product} {ergonomic}
   size_t InitialHeapSize                          = 264241152                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MarkStackSizeMax                         = 536870912                                 {product} {ergonomic}
   size_t MaxHeapSize                              = 4213178368                                {product} {ergonomic}
   size_t MaxNewSize                               = 2527068160                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 2097152                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5832704                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122945536                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122880000                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 4213178368                             {manageable} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags foldmultilines=false
 #1: stderr all=off uptime,level,tags foldmultilines=false

Environment Variables:
PATH=C:\Program Files\Microsoft SDKs\Azure\CLI2\wbin;C:\Program Files\Common Files\Oracle\Java\javapath;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\Microsoft VS Code\bin;C:\Program Files\Git\cmd;C:\Program Files\Java\jdk-23\bin;C:\Gradle\gradle-8.10.1\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Launcher\;C:\Users\<USER>\AppData\Local\Programs\Python\Python313\;C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Scripts\;C:\Users\<USER>\.cargo\bin;C:\Program Files\Go\bin;C:\ProgramData\chocolatey\bin;C:\Program Files\Vault\vault_1.18.1_windows_amd64;C:\Program Files\apache-maven-3.9.9\bin;C:\Program Files\Rancher Desktop\resources\resources\win32\bin\;C:\Program Files\Rancher Desktop\resources\resources\win32\docker-cli-plugins\;C:\Program Files\Rancher Desktop\resources\resources\linux\bin\;C:\Program Files\Rancher Desktop\resources\resources\linux\docker-cli-plugins\;C:\Program Files\nvm;C:\Program Files\nodejs;C:\Users\<USER>\.cargo\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Scripts\;C:\Users\<USER>\AppData\Local\Programs\Python\Python313\;C:\Users\<USER>\AppData\Local\Programs\Python\Launcher\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\go\bin;C:\Users\<USER>\.azure-kubectl;C:\Users\<USER>\.azure-kubelogin;C:\Users\<USER>\AppData\Local\Programs\Azure Data Studio\bin;C:\Program Files\JetBrains\IntelliJ IDEA Community Edition 2024.3.1\bin;;C:\Program Files\nvm;C:\Program Files\nodejs;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;
USERNAME=AKA665
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 140 Stepping 1, GenuineIntel
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp




Periodic native trim disabled

---------------  S Y S T E M  ---------------

OS:
 Windows 11 , 64 bit Build 26100 (10.0.26100.3912)
OS uptime: 1 days 4:59 hours
Hyper-V role detected

CPU: total 8 (initial active 8) (4 cores per cpu, 2 threads per core) family 6 model 140 stepping 1 microcode 0xb8, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, avx512f, avx512dq, avx512cd, avx512bw, avx512vl, sha, fma, vzeroupper, avx512_vpopcntdq, avx512_vpclmulqdq, avx512_vaes, avx512_vnni, clflush, clflushopt, clwb, avx512_vbmi2, avx512_vbmi, hv, rdtscp, rdpid, fsrm, gfni, avx512_bitalg, f16c, cet_ibt, cet_ss, avx512_ifma
Processor Information for the first 8 processors :
  Max Mhz: 2611, Current Mhz: 2611, Mhz Limit: 2611

Memory: 4k page, system-wide physical 16064M (655M free)
TotalPageFile size 46432M (AvailPageFile size 17M)
current process WorkingSet (physical memory assigned to process): 650M, peak: 664M
current process commit charge ("private bytes"): 686M, peak: 700M

vm_info: Java HotSpot(TM) 64-Bit Server VM (23.0.1+11-39) for windows-amd64 JRE (23.0.1+11-39), built on 2024-09-30T07:20:43Z with MS VC++ 17.6 (VS2022)

END.
