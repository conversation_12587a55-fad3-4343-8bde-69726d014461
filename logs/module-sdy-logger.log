2025-06-09 14:16:01,452 INFO o.s.b.StartupInfoLogger [main] Starting BusinessApplication using Java 23.0.1 with PID 28888 (C:\Users\<USER>\Documents\SD1 SDY\SD1-ModuleSDY\target\classes started by AKA665 in C:\Users\<USER>\Documents\SD1 SDY)
2025-06-09 14:16:01,536 INFO o.s.b.SpringApplication [main] No active profile set, falling back to 1 default profile: "default"
2025-06-09 14:16:16,857 INFO o.s.d.r.c.RepositoryConfigurationDelegate [main] Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-09 14:16:20,261 INFO o.s.d.r.c.RepositoryConfigurationDelegate [main] Finished Spring Data repository scanning in 3383 ms. Found 297 JPA repository interfaces.
2025-06-09 14:16:24,464 INFO o.s.b.w.e.t.TomcatWebServer [main] Tomcat initialized with port 8081 (http)
2025-06-09 14:16:24,503 INFO o.a.j.l.DirectJDKLog [main] Initializing ProtocolHandler ["http-nio-8081"]
2025-06-09 14:16:24,513 INFO o.a.j.l.DirectJDKLog [main] Starting service [Tomcat]
2025-06-09 14:16:24,513 INFO o.a.j.l.DirectJDKLog [main] Starting Servlet engine: [Apache Tomcat/10.1.31]
2025-06-09 14:16:24,715 INFO o.a.j.l.DirectJDKLog [main] Initializing Spring embedded WebApplicationContext
2025-06-09 14:16:24,719 INFO o.s.b.w.s.c.ServletWebServerApplicationContext [main] Root WebApplicationContext: initialization completed in 22991 ms
2025-06-09 14:16:28,245 INFO c.z.h.HikariDataSource [main] HikariPool-1 - Starting...
2025-06-09 14:16:44,779 INFO o.h.j.i.u.LogHelper [main] HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-09 14:16:44,910 INFO o.h.Version [main] HHH000412: Hibernate ORM core version 6.3.1.Final
2025-06-09 14:16:45,017 INFO o.h.c.i.RegionFactoryInitiator [main] HHH000026: Second-level cache disabled
2025-06-09 14:16:45,448 INFO o.s.o.j.p.SpringPersistenceUnitInfo [main] No LoadTimeWeaver setup: ignoring JPA class transformer
2025-06-09 14:16:45,480 INFO c.z.h.HikariDataSource [main] HikariPool-1 - Starting...
2025-06-09 14:17:00,097 WARN o.h.e.j.e.i.JdbcEnvironmentInitiator [main] HHH000342: Could not obtain connection to query metadata
java.lang.NullPointerException: Cannot invoke "org.hibernate.engine.jdbc.spi.SqlExceptionHelper.convert(java.sql.SQLException, String)" because the return value of "org.hibernate.resource.transaction.backend.jdbc.internal.JdbcIsolationDelegate.sqlExceptionHelper()" is null
	at org.hibernate.resource.transaction.backend.jdbc.internal.JdbcIsolationDelegate.delegateWork(JdbcIsolationDelegate.java:116)
	at org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator.getJdbcEnvironmentUsingJdbcMetadata(JdbcEnvironmentInitiator.java:273)
	at org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator.initiateService(JdbcEnvironmentInitiator.java:105)
	at org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator.initiateService(JdbcEnvironmentInitiator.java:66)
	at org.hibernate.boot.registry.internal.StandardServiceRegistryImpl.initiateService(StandardServiceRegistryImpl.java:129)
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.createService(AbstractServiceRegistryImpl.java:263)
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.initializeService(AbstractServiceRegistryImpl.java:238)
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.getService(AbstractServiceRegistryImpl.java:215)
	at org.hibernate.boot.model.relational.Database.<init>(Database.java:45)
	at org.hibernate.boot.internal.InFlightMetadataCollectorImpl.getDatabase(InFlightMetadataCollectorImpl.java:223)
	at org.hibernate.boot.internal.InFlightMetadataCollectorImpl.<init>(InFlightMetadataCollectorImpl.java:191)
	at org.hibernate.boot.model.process.spi.MetadataBuildingProcess.complete(MetadataBuildingProcess.java:169)
	at org.hibernate.jpa.boot.internal.EntityManagerFactoryBuilderImpl.metadata(EntityManagerFactoryBuilderImpl.java:1432)
	at org.hibernate.jpa.boot.internal.EntityManagerFactoryBuilderImpl.build(EntityManagerFactoryBuilderImpl.java:1503)
	at org.springframework.orm.jpa.vendor.SpringHibernateJpaPersistenceProvider.createContainerEntityManagerFactory(SpringHibernateJpaPersistenceProvider.java:75)
	at org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean.createNativeEntityManagerFactory(LocalContainerEntityManagerFactoryBean.java:390)
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.buildNativeEntityManagerFactory(AbstractEntityManagerFactoryBean.java:409)
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.afterPropertiesSet(AbstractEntityManagerFactoryBean.java:396)
	at org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean.afterPropertiesSet(LocalContainerEntityManagerFactoryBean.java:366)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1853)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1802)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:600)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:205)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:954)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:625)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:754)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:456)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:335)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1363)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1352)
	at com.maersk.sd1.BusinessApplication.main(BusinessApplication.java:13)
2025-06-09 14:17:00,103 ERROR o.s.o.j.AbstractEntityManagerFactoryBean [main] Failed to initialize JPA EntityManagerFactory: Unable to create requested service [org.hibernate.engine.jdbc.env.spi.JdbcEnvironment] due to: Unable to determine Dialect without JDBC metadata (please set 'javax.persistence.jdbc.url', 'hibernate.connection.url', or 'hibernate.dialect')
2025-06-09 14:17:00,103 WARN o.s.c.s.AbstractApplicationContext [main] Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'entityManagerFactory' defined in class path resource [org/springframework/boot/autoconfigure/orm/jpa/HibernateJpaConfiguration.class]: Unable to create requested service [org.hibernate.engine.jdbc.env.spi.JdbcEnvironment] due to: Unable to determine Dialect without JDBC metadata (please set 'javax.persistence.jdbc.url', 'hibernate.connection.url', or 'hibernate.dialect')
2025-06-09 14:17:01,114 WARN z.r.i.AsyncReporter$BoundedAsyncReporter [main] Timed out waiting for in-flight spans to send
2025-06-09 14:17:01,114 INFO o.a.j.l.DirectJDKLog [main] Stopping service [Tomcat]
2025-06-09 14:17:01,151 INFO o.s.b.a.l.ConditionEvaluationReportLogger [main] 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-06-09 14:17:01,198 ERROR o.s.b.SpringApplication [main] Application run failed
org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'entityManagerFactory' defined in class path resource [org/springframework/boot/autoconfigure/orm/jpa/HibernateJpaConfiguration.class]: Unable to create requested service [org.hibernate.engine.jdbc.env.spi.JdbcEnvironment] due to: Unable to determine Dialect without JDBC metadata (please set 'javax.persistence.jdbc.url', 'hibernate.connection.url', or 'hibernate.dialect')
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1806)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:600)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:205)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:954)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:625)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:754)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:456)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:335)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1363)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1352)
	at com.maersk.sd1.BusinessApplication.main(BusinessApplication.java:13)
Caused by: org.hibernate.service.spi.ServiceException: Unable to create requested service [org.hibernate.engine.jdbc.env.spi.JdbcEnvironment] due to: Unable to determine Dialect without JDBC metadata (please set 'javax.persistence.jdbc.url', 'hibernate.connection.url', or 'hibernate.dialect')
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.createService(AbstractServiceRegistryImpl.java:276)
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.initializeService(AbstractServiceRegistryImpl.java:238)
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.getService(AbstractServiceRegistryImpl.java:215)
	at org.hibernate.boot.model.relational.Database.<init>(Database.java:45)
	at org.hibernate.boot.internal.InFlightMetadataCollectorImpl.getDatabase(InFlightMetadataCollectorImpl.java:223)
	at org.hibernate.boot.internal.InFlightMetadataCollectorImpl.<init>(InFlightMetadataCollectorImpl.java:191)
	at org.hibernate.boot.model.process.spi.MetadataBuildingProcess.complete(MetadataBuildingProcess.java:169)
	at org.hibernate.jpa.boot.internal.EntityManagerFactoryBuilderImpl.metadata(EntityManagerFactoryBuilderImpl.java:1432)
	at org.hibernate.jpa.boot.internal.EntityManagerFactoryBuilderImpl.build(EntityManagerFactoryBuilderImpl.java:1503)
	at org.springframework.orm.jpa.vendor.SpringHibernateJpaPersistenceProvider.createContainerEntityManagerFactory(SpringHibernateJpaPersistenceProvider.java:75)
	at org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean.createNativeEntityManagerFactory(LocalContainerEntityManagerFactoryBean.java:390)
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.buildNativeEntityManagerFactory(AbstractEntityManagerFactoryBean.java:409)
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.afterPropertiesSet(AbstractEntityManagerFactoryBean.java:396)
	at org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean.afterPropertiesSet(LocalContainerEntityManagerFactoryBean.java:366)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1853)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1802)
	... 15 common frames omitted
Caused by: org.hibernate.HibernateException: Unable to determine Dialect without JDBC metadata (please set 'javax.persistence.jdbc.url', 'hibernate.connection.url', or 'hibernate.dialect')
	at org.hibernate.engine.jdbc.dialect.internal.DialectFactoryImpl.determineDialect(DialectFactoryImpl.java:190)
	at org.hibernate.engine.jdbc.dialect.internal.DialectFactoryImpl.buildDialect(DialectFactoryImpl.java:86)
	at org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator.getJdbcEnvironmentWithDefaults(JdbcEnvironmentInitiator.java:141)
	at org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator.getJdbcEnvironmentUsingJdbcMetadata(JdbcEnvironmentInitiator.java:344)
	at org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator.initiateService(JdbcEnvironmentInitiator.java:105)
	at org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator.initiateService(JdbcEnvironmentInitiator.java:66)
	at org.hibernate.boot.registry.internal.StandardServiceRegistryImpl.initiateService(StandardServiceRegistryImpl.java:129)
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.createService(AbstractServiceRegistryImpl.java:263)
	... 30 common frames omitted
2025-06-09 14:17:37,616 INFO o.s.b.StartupInfoLogger [main] Starting BusinessApplication using Java 23.0.1 with PID 26112 (C:\Users\<USER>\Documents\SD1 SDY\SD1-ModuleSDY\target\classes started by AKA665 in C:\Users\<USER>\Documents\SD1 SDY)
2025-06-09 14:17:37,620 INFO o.s.b.SpringApplication [main] No active profile set, falling back to 1 default profile: "default"
2025-06-09 14:17:48,143 INFO o.s.d.r.c.RepositoryConfigurationDelegate [main] Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-09 14:17:51,154 INFO o.s.d.r.c.RepositoryConfigurationDelegate [main] Finished Spring Data repository scanning in 2994 ms. Found 297 JPA repository interfaces.
2025-06-09 14:17:53,689 INFO o.s.b.w.e.t.TomcatWebServer [main] Tomcat initialized with port 8093 (http)
2025-06-09 14:17:53,717 INFO o.a.j.l.DirectJDKLog [main] Initializing ProtocolHandler ["http-nio-8093"]
2025-06-09 14:17:53,728 INFO o.a.j.l.DirectJDKLog [main] Starting service [Tomcat]
2025-06-09 14:17:53,728 INFO o.a.j.l.DirectJDKLog [main] Starting Servlet engine: [Apache Tomcat/10.1.31]
2025-06-09 14:17:53,927 INFO o.a.j.l.DirectJDKLog [main] Initializing Spring embedded WebApplicationContext
2025-06-09 14:17:53,930 INFO o.s.b.w.s.c.ServletWebServerApplicationContext [main] Root WebApplicationContext: initialization completed in 16229 ms
2025-06-09 14:17:56,314 INFO c.z.h.HikariDataSource [main] HikariPool-1 - Starting...
2025-06-09 14:17:59,270 INFO c.z.h.p.HikariPool [main] HikariPool-1 - Added connection ConnectionID:1 ClientConnectionId: 46e347f2-2a1b-4f88-ac29-cff3c01a80e5
2025-06-09 14:17:59,270 INFO c.z.h.HikariDataSource [main] HikariPool-1 - Start completed.
2025-06-09 14:17:59,416 INFO o.h.j.i.u.LogHelper [main] HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-09 14:17:59,578 INFO o.h.Version [main] HHH000412: Hibernate ORM core version 6.3.1.Final
2025-06-09 14:17:59,658 INFO o.h.c.i.RegionFactoryInitiator [main] HHH000026: Second-level cache disabled
2025-06-09 14:18:00,349 INFO o.s.o.j.p.SpringPersistenceUnitInfo [main] No LoadTimeWeaver setup: ignoring JPA class transformer
2025-06-09 14:18:05,090 WARN o.h.m.RootClass [main] HHH000038: Composite-id class does not override equals(): com.maersk.sd1.common.model.PortMasterEntityPropertyPK
2025-06-09 14:18:05,090 WARN o.h.m.RootClass [main] HHH000039: Composite-id class does not override hashCode(): com.maersk.sd1.common.model.PortMasterEntityPropertyPK
2025-06-09 14:18:12,102 INFO o.h.e.t.j.p.i.JtaPlatformInitiator [main] HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-06-09 14:18:12,148 INFO o.s.o.j.AbstractEntityManagerFactoryBean [main] Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-09 14:18:13,332 INFO o.s.d.j.r.q.QueryEnhancerFactory [main] Hibernate is in classpath; If applicable, HQL parser will be used.
2025-06-09 14:18:46,297 WARN o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration [main] spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-09 14:18:51,238 INFO o.s.b.a.e.w.EndpointLinksResolver [main] Exposing 1 endpoint beneath base path '/actuator'
2025-06-09 14:18:51,493 INFO o.a.j.l.DirectJDKLog [main] Starting ProtocolHandler ["http-nio-8093"]
2025-06-09 14:18:51,525 WARN o.s.c.s.AbstractApplicationContext [main] Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Failed to start bean 'webServerStartStop'
2025-06-09 14:18:51,803 INFO o.s.o.j.AbstractEntityManagerFactoryBean [main] Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-09 14:18:51,827 INFO c.z.h.HikariDataSource [main] HikariPool-1 - Shutdown initiated...
2025-06-09 14:18:51,851 INFO c.z.h.HikariDataSource [main] HikariPool-1 - Shutdown completed.
2025-06-09 14:18:52,904 INFO o.s.b.a.l.ConditionEvaluationReportLogger [main] 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-06-09 14:18:52,937 ERROR o.s.b.d.LoggingFailureAnalysisReporter [main] 

***************************
APPLICATION FAILED TO START
***************************

Description:

Web server failed to start. Port 8093 was already in use.

Action:

Identify and stop the process that's listening on port 8093 or configure this application to listen on another port.

2025-06-09 14:25:49,784 INFO o.s.b.StartupInfoLogger [main] Starting BusinessApplication using Java 23.0.1 with PID 9504 (C:\Users\<USER>\Documents\SD1 SDY\SD1-ModuleSDY\target\classes started by AKA665 in C:\Users\<USER>\Documents\SD1 SDY)
2025-06-09 14:25:49,787 INFO o.s.b.SpringApplication [main] No active profile set, falling back to 1 default profile: "default"
2025-06-09 14:25:59,794 INFO o.s.d.r.c.RepositoryConfigurationDelegate [main] Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-09 14:26:02,432 INFO o.s.d.r.c.RepositoryConfigurationDelegate [main] Finished Spring Data repository scanning in 2608 ms. Found 297 JPA repository interfaces.
2025-06-09 14:26:05,046 INFO o.s.b.w.e.t.TomcatWebServer [main] Tomcat initialized with port 8095 (http)
2025-06-09 14:26:05,066 INFO o.a.j.l.DirectJDKLog [main] Initializing ProtocolHandler ["http-nio-8095"]
2025-06-09 14:26:05,074 INFO o.a.j.l.DirectJDKLog [main] Starting service [Tomcat]
2025-06-09 14:26:05,074 INFO o.a.j.l.DirectJDKLog [main] Starting Servlet engine: [Apache Tomcat/10.1.31]
2025-06-09 14:26:05,243 INFO o.a.j.l.DirectJDKLog [main] Initializing Spring embedded WebApplicationContext
2025-06-09 14:26:05,247 INFO o.s.b.w.s.c.ServletWebServerApplicationContext [main] Root WebApplicationContext: initialization completed in 15376 ms
2025-06-09 14:26:16,085 INFO c.z.h.HikariDataSource [main] HikariPool-1 - Starting...
2025-06-09 14:26:21,742 INFO c.z.h.p.HikariPool [main] HikariPool-1 - Added connection ConnectionID:1 ClientConnectionId: 7d6c2d3e-0e15-44bb-ab4a-dc470660b24d
2025-06-09 14:26:21,746 INFO c.z.h.HikariDataSource [main] HikariPool-1 - Start completed.
2025-06-09 14:26:21,829 INFO o.h.j.i.u.LogHelper [main] HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-09 14:26:21,936 INFO o.h.Version [main] HHH000412: Hibernate ORM core version 6.3.1.Final
2025-06-09 14:26:22,001 INFO o.h.c.i.RegionFactoryInitiator [main] HHH000026: Second-level cache disabled
2025-06-09 14:26:22,423 INFO o.s.o.j.p.SpringPersistenceUnitInfo [main] No LoadTimeWeaver setup: ignoring JPA class transformer
2025-06-09 14:26:29,082 WARN o.h.m.RootClass [main] HHH000038: Composite-id class does not override equals(): com.maersk.sd1.common.model.PortMasterEntityPropertyPK
2025-06-09 14:26:29,084 WARN o.h.m.RootClass [main] HHH000039: Composite-id class does not override hashCode(): com.maersk.sd1.common.model.PortMasterEntityPropertyPK
2025-06-09 14:26:32,095 INFO o.h.e.t.j.p.i.JtaPlatformInitiator [main] HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-06-09 14:26:32,126 INFO o.s.o.j.AbstractEntityManagerFactoryBean [main] Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-09 14:26:33,045 INFO o.s.d.j.r.q.QueryEnhancerFactory [main] Hibernate is in classpath; If applicable, HQL parser will be used.
